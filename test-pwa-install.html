<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PWA安装提示预览 - 积分商城</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f5f5f5;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }

        .demo-container {
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
        }

        .demo-card {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .demo-title {
            color: #e93b3d;
            font-weight: 700;
            margin-bottom: 20px;
            text-align: center;
        }

        .demo-btn {
            background: linear-gradient(45deg, #e93b3d, #f10215);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-weight: 600;
            transition: all 0.3s;
            margin: 10px;
        }

        .demo-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(233, 59, 61, 0.3);
            color: white;
        }

        /* PWA模态框样式 */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        @keyframes slideUp {
            from { transform: translateY(30px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
        .pwa-modal-header {
            background: linear-gradient(135deg, #e93b3d, #f10215);
            color: white;
            padding: 24px;
            text-align: center;
            position: relative;
        }
        .pwa-modal-icon {
            width: 64px;
            height: 64px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 16px;
            font-size: 28px;
        }
        .pwa-modal-title {
            font-size: 20px;
            font-weight: 700;
            margin: 0 0 8px 0;
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        .pwa-modal-subtitle {
            font-size: 14px;
            opacity: 0.9;
            margin: 0;
        }
        .pwa-modal-body {
            padding: 24px;
        }
        .pwa-features {
            list-style: none;
            padding: 0;
            margin: 0 0 24px 0;
        }
        .pwa-features li {
            display: flex;
            align-items: center;
            padding: 8px 0;
            font-size: 14px;
            color: #666;
        }
        .pwa-features li::before {
            content: "✓";
            color: #00b74a;
            font-weight: bold;
            margin-right: 12px;
            width: 16px;
            text-align: center;
        }
        .pwa-modal-buttons {
            display: flex;
            gap: 12px;
        }
        .pwa-btn {
            flex: 1;
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            text-align: center;
        }
        .pwa-btn-primary {
            background: linear-gradient(45deg, #e93b3d, #f10215);
            color: white;
            box-shadow: 0 4px 12px rgba(233, 59, 61, 0.3);
        }
        .pwa-btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(233, 59, 61, 0.4);
        }
        .pwa-btn-secondary {
            background: #f8f9fa;
            color: #666;
            border: 1px solid #e0e0e0;
        }
        .pwa-btn-secondary:hover {
            background: #e9ecef;
            color: #495057;
        }
        .pwa-later-options {
            margin-top: 12px;
            text-align: center;
        }
        .pwa-later-link {
            color: #999;
            font-size: 12px;
            text-decoration: none;
            cursor: pointer;
        }
        .pwa-later-link:hover {
            color: #666;
            text-decoration: underline;
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
        }

        .feature-list li:last-child {
            border-bottom: none;
        }

        .feature-list li i {
            color: #00b74a;
            margin-right: 12px;
            width: 20px;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-card">
            <h1 class="demo-title">PWA安装提示优化预览</h1>
            
            <div class="text-center mb-4">
                <button class="demo-btn" onclick="showPWAModal()">
                    <i class="fas fa-mobile-alt me-2"></i>预览安装提示
                </button>
                <button class="demo-btn" onclick="showSuccessToast()">
                    <i class="fas fa-check me-2"></i>预览成功提示
                </button>
                <button class="demo-btn" onclick="showUnavailableToast()">
                    <i class="fas fa-info me-2"></i>预览不可用提示
                </button>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <h5 class="text-primary mb-3">
                        <i class="fas fa-star me-2"></i>优化特性
                    </h5>
                    <ul class="feature-list">
                        <li>
                            <i class="fas fa-bolt"></i>
                            <span>第一时间显示安装提示（2秒延迟）</span>
                        </li>
                        <li>
                            <i class="fas fa-palette"></i>
                            <span>京东风格的视觉设计</span>
                        </li>
                        <li>
                            <i class="fas fa-mobile-alt"></i>
                            <span>响应式模态框设计</span>
                        </li>
                        <li>
                            <i class="fas fa-clock"></i>
                            <span>智能提醒频率控制（24小时）</span>
                        </li>
                        <li>
                            <i class="fas fa-ban"></i>
                            <span>永久拒绝选项</span>
                        </li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h5 class="text-primary mb-3">
                        <i class="fas fa-gift me-2"></i>用户体验
                    </h5>
                    <ul class="feature-list">
                        <li>
                            <i class="fas fa-eye"></i>
                            <span>清晰的功能说明</span>
                        </li>
                        <li>
                            <i class="fas fa-mouse-pointer"></i>
                            <span>明确的操作按钮</span>
                        </li>
                        <li>
                            <i class="fas fa-bell"></i>
                            <span>安装成功反馈</span>
                        </li>
                        <li>
                            <i class="fas fa-shield-alt"></i>
                            <span>避免重复打扰</span>
                        </li>
                        <li>
                            <i class="fas fa-download"></i>
                            <span>导航栏快捷安装</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- PWA安装模态框预览 -->
    <div id="pwa-modal-preview" style="display: none;">
        <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.6); z-index: 10000; display: flex; align-items: center; justify-content: center; backdrop-filter: blur(4px); animation: fadeIn 0.3s ease-out;">
            <div style="background: white; border-radius: 16px; padding: 0; max-width: 400px; width: 90%; box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3); animation: slideUp 0.3s ease-out; overflow: hidden;">
                <div class="pwa-modal-header">
                    <div class="pwa-modal-icon">📱</div>
                    <h3 class="pwa-modal-title">安装积分商城APP</h3>
                    <p class="pwa-modal-subtitle">享受更快速、更便捷的购物体验</p>
                </div>
                
                <div class="pwa-modal-body">
                    <ul class="pwa-features">
                        <li>无需下载，一键安装到桌面</li>
                        <li>离线浏览，随时查看商品信息</li>
                        <li>消息推送，第一时间获取优惠</li>
                        <li>快速启动，秒开应用体验</li>
                        <li>节省流量，智能缓存技术</li>
                    </ul>
                    
                    <div class="pwa-modal-buttons">
                        <button class="pwa-btn pwa-btn-primary" onclick="hidePWAModal()">
                            立即安装
                        </button>
                        <button class="pwa-btn pwa-btn-secondary" onclick="hidePWAModal()">
                            以后提醒
                        </button>
                    </div>
                    
                    <div class="pwa-later-options">
                        <a href="#" class="pwa-later-link" onclick="hidePWAModal()">不再提醒</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showPWAModal() {
            document.getElementById('pwa-modal-preview').style.display = 'block';
        }

        function hidePWAModal() {
            document.getElementById('pwa-modal-preview').style.display = 'none';
        }

        function showSuccessToast() {
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(45deg, #00b74a, #52c41a);
                color: white;
                padding: 16px 24px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0, 183, 74, 0.3);
                z-index: 10001;
                font-size: 14px;
                font-weight: 600;
                animation: slideInRight 0.3s ease-out;
            `;
            
            toast.innerHTML = `
                <style>
                    @keyframes slideInRight {
                        from { transform: translateX(100%); opacity: 0; }
                        to { transform: translateX(0); opacity: 1; }
                    }
                </style>
                <div style="display: flex; align-items: center;">
                    <span style="margin-right: 8px;">🎉</span>
                    安装成功！您可以在桌面找到积分商城应用
                </div>
            `;
            
            document.body.appendChild(toast);
            
            setTimeout(() => {
                if (document.body.contains(toast)) {
                    document.body.removeChild(toast);
                }
            }, 3000);
        }

        function showUnavailableToast() {
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(45deg, #ff6700, #ff8c00);
                color: white;
                padding: 16px 24px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(255, 103, 0, 0.3);
                z-index: 10001;
                font-size: 14px;
                font-weight: 600;
                animation: slideInRight 0.3s ease-out;
                max-width: 300px;
            `;
            
            toast.innerHTML = `
                <style>
                    @keyframes slideInRight {
                        from { transform: translateX(100%); opacity: 0; }
                        to { transform: translateX(0); opacity: 1; }
                    }
                </style>
                <div style="display: flex; align-items: center;">
                    <span style="margin-right: 8px;">ℹ️</span>
                    <div>
                        <div>当前浏览器不支持安装</div>
                        <div style="font-size: 12px; opacity: 0.9; margin-top: 4px;">
                            请使用Chrome、Edge或Safari浏览器
                        </div>
                    </div>
                </div>
            `;
            
            document.body.appendChild(toast);
            
            setTimeout(() => {
                if (document.body.contains(toast)) {
                    document.body.removeChild(toast);
                }
            }, 4000);
        }

        // 点击模态框背景关闭
        document.addEventListener('click', function(e) {
            if (e.target.id === 'pwa-modal-preview') {
                hidePWAModal();
            }
        });
    </script>
</body>
</html>
