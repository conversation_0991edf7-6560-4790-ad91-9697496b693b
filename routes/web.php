<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ApiExtensionController;
use App\Http\Controllers\PointsExchangeController;
use App\Http\Controllers\ShopController;
use App\Http\Controllers\ProductController;
// use App\Http\Controllers\ProductReviewController; // 控制器不存在，已注释
use App\Http\Controllers\OrderController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\LuckyDrawController;
use App\Http\Controllers\PointProductController;
use App\Http\Controllers\CrmController;
use App\Http\Controllers\SsoController;
use App\Http\Controllers\ExamController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

// 首页 - 积分商城
Route::get('/', 'HomeController@index');

// 用户认证路由
Route::get('/login', 'AuthController@showLoginForm')->name('login');
Route::post('/login', 'AuthController@login')->name('login.post');
// 手机验证码登录
Route::post('/phone/login', 'Auth\PhoneLoginController@login')->name('phone.login.post');
Route::get('/register', 'AuthController@showRegistrationForm')->name('register');
Route::post('/register', 'AuthController@register')->name('register.post');
Route::post('/logout', 'AuthController@logout')->name('logout');

// 登录状态检查路由
Route::get('/auth/status', 'AuthStatusController@index')->name('auth.status');

// 会话调试路由 - 仅用于开发环境
Route::get('/debug/session', 'SessionDebugController@debug')->name('debug.session');
Route::get('/debug/session/set', 'SessionDebugController@setSession')->name('debug.session.set');
Route::get('/debug/login', 'SessionDebugController@testLogin')->name('debug.login');
Route::get('/debug/auth', function() {
    return view('auth.debug');
})->name('debug.auth');

// 新增会话测试路由
Route::get('/session-test', function() {
    $sessionData = [
        'is_started' => session()->isStarted(),
        'session_id' => session()->getId(),
        'has_session' => request()->hasSession(),
        'all_data' => session()->all(),
        'domain' => config('session.domain'),
        'cookie_name' => config('session.cookie'),
        'secure' => config('session.secure'),
        'same_site' => config('session.same_site'),
    ];

    return response()->json($sessionData);
})->middleware('web')->name('session.test');

// 积分活动列表 - 公开访问
Route::get('/points/activities', 'PointActivityController@index')->name('points.activities.index');

// 完全公开的店铺相关路由 - 索引页面
Route::get('/shops', [ShopController::class, 'index'])->name('shops.index');

// 将海报生成路由移出 auth 中间件组进行测试
Route::get('/shops/generate-poster', [ShopController::class, 'generatePoster'])->name('shops.generate_poster'); // 已移出auth组

// 临时添加一个直接访问products/create视图的路由
Route::get('/direct-products-create', function() {
    $shop = \App\Models\Shop::find(request('shop'));
    $categories = \App\Models\Category::where('is_active', 1)->orderBy('sort_order')->get();
    return view('products.create', ['shop' => $shop, 'shops' => \App\Models\Shop::all(), 'categories' => $categories]);
})->name('direct.products.create');

// 公开的商品相关路由
Route::get('/products', [ProductController::class, 'index'])->name('products.index');
Route::get('/products/{product}', [ProductController::class, 'show'])->name('products.show');

// 店铺创建和我的店铺路由 - 单独定义但添加auth中间件
Route::get('/shops/create', [ShopController::class, 'create'])->name('shops.create')->middleware('auth');
Route::get('/shops/my', [ShopController::class, 'myShop'])->name('shops.my')->middleware('auth');

// 商品回收站相关路由
Route::get('/shops/trash', [ShopController::class, 'trash'])->name('shops.trash')->middleware('auth');
Route::post('/shops/products/{product}/restore', [ShopController::class, 'restoreProduct'])->name('shops.products.restore')->middleware('auth');
Route::delete('/shops/products/{product}/force-delete', [ShopController::class, 'forceDeleteProduct'])->name('shops.products.force-delete')->middleware('auth');
Route::delete('/shops/trash/clear', [ShopController::class, 'clearTrash'])->name('shops.products.clear-trash')->middleware('auth');

// 店铺详情页 - 公开访问（确保此路由在/shops/xxx之后）
Route::get('/shops/{shop}', [ShopController::class, 'show'])->name('shops.show');

// 需要身份验证的路由
Route::middleware(['auth'])->group(function () {
    // 我的店铺路由
    Route::prefix('shops')->group(function () {
        Route::put('/my', [ShopController::class, 'updateShop'])->name('shops.my.update');
        Route::post('/my/products', [ShopController::class, 'addProducts'])->name('shops.products.add');
        Route::delete('/my/products/{product}', [ShopController::class, 'removeProduct'])->name('shops.products.remove');
    });

    // 积分管理相关路由
    Route::prefix('points')->group(function () {
        Route::get('/import', 'PointsController@importForm')->name('points.import.form');
        Route::post('/import', 'PointsController@import')->name('points.import');
        Route::get('/records', 'PointsController@records')->name('points.records');
        Route::get('/statistics', 'PointsController@statistics')->name('points.statistics');
    });

    // 积分活动参与和历史记录 - 需要身份验证
    Route::post('/points/activities/{activity}/participate', 'PointActivityController@participate')->name('points.activities.participate');
    Route::get('/points/activities/history', 'PointActivityController@history')->name('points.activities.history');

    // 海报生成与管理
    Route::prefix('posters')->group(function () {
        Route::get('/', 'PosterController@index')->name('posters.index');
        Route::get('/create', 'PosterController@create')->name('posters.create');
        Route::post('/', 'PosterController@store')->name('posters.store');
        Route::delete('/{poster}', 'PosterController@destroy')->name('posters.destroy');
        Route::get('/{poster}/stats', 'PosterController@statistics')->name('posters.stats');
    });

    // 订单管理
    Route::prefix('orders')->group(function () {
        Route::get('/', 'OrderController@index')->name('orders.index');
        Route::get('/{order}', 'OrderController@show')->name('orders.show');
        Route::post('/{order}/refund', 'OrderController@applyRefund')->name('orders.refund.apply');
        Route::post('/{order}/cancel', 'OrderController@cancel')->name('orders.cancel');
        Route::post('/{order}/repurchase', [OrderController::class, 'repurchase'])->name('orders.repurchase');
    });

    // 用户中心
    Route::prefix('user')->group(function () {
        Route::get('/profile', 'UserController@profile')->name('user.profile');
        Route::put('/profile', 'UserController@updateProfile')->name('user.profile.update');
        Route::put('/password', 'UserController@updatePassword')->name('user.password.update');
        Route::get('/orders', 'UserController@orders')->name('user.orders');
        Route::get('/points', 'UserController@points')->name('user.points');

        // 用户通知管理
        Route::get('/notifications', 'NotificationController@index')->name('user.notifications');
        Route::post('/notifications/{id}/mark-read', 'NotificationController@markAsRead')->name('user.notifications.mark-read');
        Route::post('/notifications/mark-all-read', 'NotificationController@markAllAsRead')->name('user.notifications.mark-all-read');

        // WebPush推送通知
        Route::prefix('webpush')->name('user.webpush.')->group(function () {
            Route::post('/subscribe', 'WebPushController@subscribe')->name('subscribe');
            Route::post('/unsubscribe', 'WebPushController@unsubscribe')->name('unsubscribe');
            Route::get('/public-key', 'WebPushController@getPublicKey')->name('public-key');
            Route::post('/test', 'WebPushController@sendTestNotification')->name('test');
        });

        // 用户抽奖相关路由
        Route::prefix('lucky-draw')->name('user.lucky-draw.')->middleware(['auth'])->group(function () {
            // 抽奖活动列表
            Route::get('/activities', [LuckyDrawController::class, 'activities'])->name('activities');
            // 抽奖活动详情
            Route::get('/activity/{id}', [LuckyDrawController::class, 'show'])->name('show');
            // 抽奖
            Route::post('/activity/{id}/draw', [LuckyDrawController::class, 'draw'])->name('draw');
            // 抽奖记录
            Route::get('/records', [LuckyDrawController::class, 'records'])->name('records');
            // 提交收货地址
            Route::post('/submit-address', [LuckyDrawController::class, 'submitAddress'])->name('submit-address');
            // 确认收货
            Route::post('/confirm-receipt', [LuckyDrawController::class, 'confirmReceipt'])->name('confirm-receipt');
        });

        // 用户地址管理
        Route::prefix('addresses')->name('user.addresses.')->group(function () {
            Route::get('/', 'UserAddressController@index')->name('index');
            Route::get('/create', 'UserAddressController@create')->name('create');
            Route::post('/', 'UserAddressController@store')->name('store');
            Route::get('/{address}/edit', 'UserAddressController@edit')->name('edit');
            Route::put('/{address}', 'UserAddressController@update')->name('update');
            Route::delete('/{address}', 'UserAddressController@destroy')->name('destroy');
            Route::patch('/{address}/set-default', 'UserAddressController@setDefault')->name('set_default');
        });
    });

    // Points Exchange Routes
    Route::prefix('points/exchange')->name('points.exchange.')->middleware(['auth'])->group(function () {
        // Show confirmation page
        Route::get('/confirm/{productId}', [PointsExchangeController::class, 'showConfirmPage'])->name('confirm');
        // Show optimized confirmation page (Chinese user habits)
        Route::get('/confirm-optimized/{productId}', [PointsExchangeController::class, 'showConfirmPageOptimized'])->name('confirm.optimized');
        // Process the exchange
        Route::post('/process', [PointsExchangeController::class, 'exchange'])->name('process');
        // Exchange success page
        Route::get('/success/{order}', [PointsExchangeController::class, 'success'])->name('success');
        // User's exchange history page
        Route::get('/history', [PointsExchangeController::class, 'history'])->name('history');
        // Exchange order detail page
        Route::get('/order/{orderNo}', [PointsExchangeController::class, 'orderDetail'])->name('order.detail');
        // Cancel order
        Route::post('/order/{orderNo}/cancel', [PointsExchangeController::class, 'cancelOrder'])->name('order.cancel');
        // Confirm received
        Route::post('/order/{orderNo}/confirm', [PointsExchangeController::class, 'confirmReceived'])->name('order.confirm');
    });

    // 为了兼容现有视图，提供一个别名路由
    Route::post('points/exchange/order/{orderNo}/confirm', [PointsExchangeController::class, 'confirmReceived'])->name('points.exchange.order.confirm');

    // 店铺相关路由（需要登录）
    Route::post('/shops', [ShopController::class, 'store'])->name('shops.store');
    Route::get('/shops/{shop}/edit', [ShopController::class, 'edit'])->name('shops.edit')->middleware('shop.owner');
    Route::put('/shops/{shop}', [ShopController::class, 'update'])->name('shops.update')->middleware('shop.owner');
    Route::delete('/shops/{shop}', [ShopController::class, 'destroy'])->name('shops.destroy')->middleware('shop.owner');
    Route::post('/shops/{shop}/set-main', [ShopController::class, 'setMain'])->name('shops.set-main')->middleware('shop.owner');
    Route::post('/shops/products/create', [ShopController::class, 'createProduct'])->name('shops.products.create');
    // 积分兑换订单相关路由
    Route::get('/shops/{shop}/point-exchange-orders/{orderNo}', [ShopController::class, 'pointExchangeOrderDetail'])
        ->name('shops.point_exchange_orders.detail')->middleware(['auth', 'shop.owner']);
    Route::post('/shops/{shop}/point-exchange-orders/{orderNo}/ship', [ShopController::class, 'shipPointExchangeOrder'])
        ->name('shops.point_exchange_orders.ship')->middleware(['auth', 'shop.owner']);

    // 需要登录的商品相关路由
    Route::get('/products/create', function() {
        // 记录日志
        \Log::info('访问创建商品页面', [
            'user_id' => auth()->id(),
            'shop_id' => request('shop'),
            'request_url' => request()->fullUrl(),
            'authenticated' => auth()->check()
        ]);

        // 确保用户已登录
        if (!auth()->check()) {
            return redirect()->route('login')->with('error', '请先登录后再创建商品');
        }

        $shop = null;
        // 如果URL中包含shop参数，使用该参数
        if (request()->has('shop')) {
            try {
                $shop = \App\Models\Shop::findOrFail(request('shop'));

                // 检查用户是否有权限为此店铺添加商品
                if ($shop->user_id !== auth()->id()) {
                    return redirect()->route('shops.index')->with('error', '您没有权限为此店铺添加商品，只有店铺所有者才能管理店铺商品。');
                }
            } catch (\Exception $e) {
                return redirect()->route('shops.index')->with('error', '指定的店铺不存在。');
            }
        } else {
            // 如果URL中没有shop参数，尝试获取用户的主店铺或第一个店铺
            try {
                // 尝试获取用户的主店铺
                $shop = auth()->user()->shop; // 假设User模型中有shop关系用于获取主店铺

                // 如果没有主店铺，则尝试获取用户的第一个店铺
                if (!$shop) {
                    $shop = auth()->user()->shops()->first();
                }
            } catch (\Exception $e) {
                \Log::error('获取用户店铺出错: ' . $e->getMessage(), ['user_id' => auth()->id()]);
            }
        }

        // 获取用户的所有店铺
        $shops = auth()->user()->shops;

        if ($shops->isEmpty() && !$shop) {
            return redirect()->route('shops.create')->with('info', '您还没有店铺，请先创建一个店铺再添加商品。');
        }

        // 如果有店铺但没有选择具体店铺，重定向到我的店铺页面
        if ($shops->isNotEmpty() && !$shop) {
            return redirect()->route('shops.my')->with('info', '请选择要为哪个店铺添加商品。');
        }

        // 使用新的视图文件
        return view('products.create-shop', compact('shop', 'shops'));
    })->name('products.create')->middleware('auth');
    Route::post('/products', [ProductController::class, 'store'])->name('products.store')->middleware('auth');
    Route::post('/point-products', [PointProductController::class, 'store'])->name('point.products.store')->middleware('auth');
    Route::get('/products/{product}/edit', [ProductController::class, 'edit'])->name('products.edit')->middleware('auth');
    Route::put('/products/{product}', [ProductController::class, 'update'])->name('products.update')->middleware('auth');
    Route::delete('/products/{product}', [ProductController::class, 'destroy'])->name('products.destroy')->middleware('auth');
    // Route::get('/products/{product}/review/create', [ProductReviewController::class, 'create'])->name('products.review.create')->middleware('auth'); // 控制器不存在，已注释

    // 海报分享相关路由
    // Route::get('/shops/generate-poster', [ShopController::class, 'generatePoster'])->name('shops.generate_poster'); // 已移出auth组
    Route::post('/shops/share-poster', [ShopController::class, 'sharePoster'])->name('shops.share_poster');
});

// 店铺分享相关路由
Route::get('/s/{code}', 'ShopShareController@show')->name('shop.share.redirect');
Route::get('/shop/{shop}/share', 'ShopShareController@share')->name('shop.share');
Route::post('/shop/{shop}/share/update', 'ShopShareController@updateShare')->name('shop.share.update');
Route::post('/share/{platform}/{share}', 'ShopShareController@recordShare')->name('shop.share.record');

// 店铺分享功能
Route::middleware(['auth'])->group(function () {
    Route::get('/shops/{shop}/share', 'ShopShareController@shareOptions')->name('shop.share.options');
    Route::post('/shops/{shop}/share', 'ShopShareController@updateShareInfo')->name('shop.share.update');
    Route::post('/share/{platform}/{shareId}', 'ShopShareController@recordShare')->name('shop.share.record');
});

// 微信回调路由 - 不需要认证，用于微信服务器验证
Route::any('/admin/wechat/callback', 'Admin\WechatCallbackController@handle')->name('admin.wechat.callback');

// 抽奖活动后台管理路由
Route::prefix('admin/lucky-draw')->name('admin.lucky-draw.')->middleware(['auth:admin'])->group(function () {
    Route::get('/', [App\Http\Controllers\Admin\LuckyDrawController::class, 'index'])->name('index');
    Route::get('/create', [App\Http\Controllers\Admin\LuckyDrawController::class, 'create'])->name('create');
    Route::post('/', [App\Http\Controllers\Admin\LuckyDrawController::class, 'store'])->name('store');
    Route::get('/{luckyDraw}', [App\Http\Controllers\Admin\LuckyDrawController::class, 'show'])->name('show');
    Route::get('/{luckyDraw}/edit', [App\Http\Controllers\Admin\LuckyDrawController::class, 'edit'])->name('edit');
    Route::put('/{luckyDraw}', [App\Http\Controllers\Admin\LuckyDrawController::class, 'update'])->name('update');
    Route::delete('/{luckyDraw}', [App\Http\Controllers\Admin\LuckyDrawController::class, 'destroy'])->name('destroy');

    // 抽奖记录
    Route::get('/{luckyDraw}/records', [App\Http\Controllers\Admin\LuckyDrawController::class, 'records'])->name('records');

    // 发货管理
    Route::post('/records/{record}/ship', [App\Http\Controllers\Admin\LuckyDrawController::class, 'shipPrize'])->name('ship');
    Route::post('/records/{record}/complete', [App\Http\Controllers\Admin\LuckyDrawController::class, 'completePrize'])->name('complete');
    Route::post('/records/{record}/cancel', [App\Http\Controllers\Admin\LuckyDrawController::class, 'cancelPrize'])->name('cancel');
    Route::post('/image/upload', [App\Http\Controllers\Admin\LuckyDrawController::class, 'uploadImage'])->name('image.upload');
});

// 管理员积分兑换订单处理路由
Route::prefix('admin/orders')->name('admin.orders.')->middleware(['auth:admin'])->group(function () {
    Route::get('/point-exchange/{orderNo}', [App\Http\Controllers\Admin\OrderController::class, 'showPointExchangeOrder'])->name('point-exchange.show');
    Route::post('/point-exchange/{orderNo}/ship', [App\Http\Controllers\Admin\OrderController::class, 'shipPointExchangeOrder'])->name('point-exchange.ship');
    Route::post('/point-exchange/{orderNo}/cancel', [App\Http\Controllers\Admin\OrderController::class, 'cancelPointExchangeOrder'])->name('point-exchange.cancel');
});

// 佣金管理路由
Route::prefix('admin/commission')->middleware(['auth:admin'])->group(function () {
    Route::get('/settings', [App\Http\Controllers\Admin\CommissionController::class, 'settings'])->name('commission.settings');
    Route::post('/settings/global', [App\Http\Controllers\Admin\CommissionController::class, 'storeGlobalSetting'])->name('commission.settings.global.store');
    Route::post('/settings/shop', [App\Http\Controllers\Admin\CommissionController::class, 'storeShopSetting'])->name('commission.settings.shop.store');
    Route::delete('/settings/{setting}', [App\Http\Controllers\Admin\CommissionController::class, 'deleteSetting'])->name('commission.settings.delete');

    Route::get('/records', [App\Http\Controllers\Admin\CommissionController::class, 'records'])->name('commission.records');
    Route::post('/commission/{commission}/status', [App\Http\Controllers\Admin\CommissionController::class, 'updateStatus'])->name('commission.status.update');
    Route::post('/adjustment', [App\Http\Controllers\Admin\CommissionController::class, 'createAdjustment'])->name('commission.adjustment.create');

    Route::get('/report', [App\Http\Controllers\Admin\CommissionController::class, 'generateReport'])->name('commission.report');
});

// 数据分析路由
Route::prefix('admin/analytics')->middleware(['auth:admin'])->group(function () {
    Route::get('/', [App\Http\Controllers\Admin\AnalyticsController::class, 'index'])->name('admin.analytics.index');
    Route::get('/data', [App\Http\Controllers\Admin\AnalyticsController::class, 'getData'])->name('admin.analytics.data');
    Route::get('/export', [App\Http\Controllers\Admin\AnalyticsController::class, 'export'])->name('admin.analytics.export');
});

Route::get('/contact', function() {
    return view('contact');
})->name('contact');

// 海报相关路由
Route::get('/posters/share/{path}', 'PosterController@showPoster')->name('posters.share');
Route::get('/posters/generate/{shopId?}', 'PosterController@generateShopPoster')->name('posters.generate')->middleware('auth');
Route::post('/posters/record-share', 'PosterController@recordShare')->name('posters.record-share');

// ================== 测试路由 ==================

// 获取主店铺二维码目标链接 (需要登录)
Route::get('/test/main-shop-qr-url', function () {
    $user = Illuminate\Support\Facades\Auth::user();

    if (!$user) {
        return response('请先登录以测试此功能。', 401);
    }

    $shop = $user->shops()->where('is_main', true)->first();

    if (!$shop) {
        $shop = $user->shops()->first();
    }

    if ($shop) {
        try {
            $shopUrl = route('shops.show', ['shop' => $shop->id]);
            return response("主店铺 (ID: {$shop->id}) 的二维码目标链接是: <a href='{$shopUrl}' target='_blank'>{$shopUrl}</a> <br><br>请点击此链接检查是否能正确访问店铺页面。", 200);
        } catch (\Exception $e) {
            return response("生成店铺链接时出错: " . $e->getMessage(), 500);
        }
    } else {
        return response('错误：当前用户没有任何店铺。', 404);
    }
})->middleware('auth')->name('test.main-shop-qr-url');

// 直接生成并显示主店铺二维码图片 (需要登录)
Route::get('/test/generate-main-shop-qr-image', function () {
    $user = Illuminate\Support\Facades\Auth::user();

    if (!$user) {
        return response('请先登录以测试此功能。', 401);
    }

    $shop = $user->shops()->where('is_main', true)->first();
    if (!$shop) {
        $shop = $user->shops()->first();
    }

    if (!$shop) {
        return response('错误：当前用户没有任何店铺。', 404);
    }

    try {
        $controller = App::make(App\Http\Controllers\PosterController::class);

        // 使用反射调用 protected 方法
        $method = new ReflectionMethod(App\Http\Controllers\PosterController::class, 'generateQrCode');
        $method->setAccessible(true);
        $qrCodePath = $method->invoke($controller, $shop);

        // 检查返回的路径和文件
        if (!$qrCodePath || !file_exists($qrCodePath)) {
             Log::error('测试路由：generateQrCode 未返回有效路径或文件不存在', ['returned_path' => $qrCodePath]);
             return response('错误：二维码生成方法未返回有效文件路径。请检查日志。', 500);
        }

        // 读取文件内容并返回图片响应
        $fileContent = file_get_contents($qrCodePath);
        if ($fileContent === false) {
             Log::error('测试路由：无法读取生成的二维码文件', ['path' => $qrCodePath]);
             return response('错误：无法读取生成的二维码文件。请检查日志。', 500);
        }

        Log::info('测试路由：成功生成并准备返回二维码图片', ['shop_id' => $shop->id, 'path' => $qrCodePath]);
        return response($fileContent)->header('Content-Type', 'image/png');

    } catch (\Exception $e) {
        // generateQrCode 内部已经记录了详细错误
        Log::error('测试路由 /test/generate-main-shop-qr-image 捕获到异常: ' . $e->getMessage());
        return response("错误：生成主店铺二维码图片失败。详细错误已记录到日志中。请检查 storage/logs/laravel.log 文件。<br>错误信息: " . $e->getMessage(), 500);
    }
})->middleware('auth')->name('test.generate-main-shop-qr-image');

// 微信授权相关路由
Route::get('wechat/auth', 'App\Http\Controllers\Auth\WechatAuthController@authorizeWechat')->name('wechat.auth');
Route::get('wechat/callback', 'App\Http\Controllers\Auth\WechatAuthController@callback')->name('wechat.callback');
Route::get('wechat/js-config', 'App\Http\Controllers\Auth\WechatAuthController@getJsConfig')->name('wechat.js-config');
Route::get('wechat/check-login', 'App\Http\Controllers\Auth\WechatAuthController@checkLoginStatus')->name('wechat.check-login');

// 微信绑定相关路由
Route::get('wechat/bind', 'App\Http\Controllers\Auth\WechatBindController@showBindForm')->name('wechat.bind');
Route::post('wechat/bind', 'App\Http\Controllers\Auth\WechatBindController@bind')->name('wechat.bind.post');
Route::get('wechat/bind/callback', 'App\Http\Controllers\Auth\WechatBindController@callback')->name('wechat.bind.callback');
Route::post('wechat/unbind', 'App\Http\Controllers\Auth\WechatBindController@unbind')->name('wechat.unbind');

// 手机号绑定相关路由
Route::get('phone/bind', 'App\Http\Controllers\Auth\PhoneBindController@showBindForm')->name('phone.bind');
Route::post('phone/bind', 'App\Http\Controllers\Auth\PhoneBindController@bindPhone')->name('phone.bind.post');

Route::get('/test-export', function() {
    \Log::info('测试导出路由被访问');
    try {
        // 先检查依赖的类和方法是否存在
        \Log::info('检查依赖类是否存在');
        $classes = [
            'App\Http\Controllers\Admin\ProductController',
            'App\Exports\ProductsExport',
            'Maatwebsite\Excel\Facades\Excel'
        ];

        foreach ($classes as $class) {
            if (!class_exists($class)) {
                throw new \Exception("类 {$class} 不存在");
            }
            \Log::info("类 {$class} 存在");
        }

        // 直接使用Excel门面导出
        \Log::info('直接使用Excel门面测试导出');
        try {
            $export = new \App\Exports\ProductsExport();
            \Log::info('成功创建ProductsExport实例');

            $fileName = '商品数据-' . date('YmdHis') . '.csv';
            \Log::info('文件名: ' . $fileName);

            // 尝试导出CSV格式
            try {
                \Log::info('尝试导出CSV格式');
                $csvType = \Maatwebsite\Excel\Excel::CSV;
                \Log::info('CSV类型: ' . $csvType);
                $response = \Maatwebsite\Excel\Facades\Excel::download($export, $fileName, $csvType);
                \Log::info('CSV导出成功，响应类型: ' . get_class($response));
                return $response;
            } catch (\Throwable $csvError) {
                \Log::error('CSV导出错误: ' . $csvError->getMessage());
                \Log::error('CSV导出堆栈: ' . $csvError->getTraceAsString());

                // CSV失败，尝试HTML格式
                \Log::info('尝试导出简单HTML格式');
                ob_start();
                echo "<html><body><table border='1'>";
                echo "<tr><th>ID</th><th>商品名称</th><th>价格</th></tr>";

                $products = \App\Models\Product::limit(10)->get();
                foreach ($products as $product) {
                    echo "<tr>";
                    echo "<td>{$product->id}</td><td>{$product->name}</td><td>{$product->price}</td>";
                    echo "</tr>";
                }

                echo "</table></body></html>";
                $html = ob_get_clean();

                return response($html)->header('Content-Type', 'text/html');
            }
        } catch (\Throwable $e) {
            \Log::error('Excel导出失败: ' . $e->getMessage());
            \Log::error('Excel导出堆栈: ' . $e->getTraceAsString());
            throw $e;
        }
    } catch (\Throwable $e) {
        \Log::error('导出失败: ' . $e->getMessage());
        \Log::error('导出失败堆栈: ' . $e->getTraceAsString());
        return response()->json([
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ], 500);
    }
});

// 添加检查登录状态的路由
Route::get('/check-auth-status', [App\Http\Controllers\AuthStatusController::class, 'checkStatus']);

// 店铺商品管理
Route::post('/shops/products/add', [ShopController::class, 'addProducts'])->name('shops.products.add');
Route::get('/api/products/search', [App\Http\Controllers\Api\ProductController::class, 'search']);

// CRM系统重定向路由（用于跨域认证）
Route::get('/crm/redirect', [\App\Http\Controllers\CrmController::class, 'redirect'])->middleware('auth:web')->name('crm.redirect');

// 新CRM系统重定向路由（用于跨域认证）
Route::get('/pcrm/redirect', [\App\Http\Controllers\PcrmController::class, 'redirect'])->middleware('auth:web')->name('pcrm.redirect');

// 考试培训系统路由
Route::get('/exam/redirect', [\App\Http\Controllers\ExamController::class, 'redirect'])->middleware('auth:web')->name('exam.redirect');

// SSO验证签名路由
Route::get('/crm/sso/verify', function () {
    return ['success' => true, 'message' => 'URL signature is valid'];
})->middleware('signed')->name('crm.sso.verify');

// 积分兑换页面对比测试
Route::get('/test/exchange-comparison', function () {
    $testProducts = \App\Models\Product::where('is_points_product', true)
                                      ->where('stock', '>', 0)
                                      ->take(5)
                                      ->get();
    return view('test.exchange-comparison', compact('testProducts'));
})->name('test.exchange.comparison');

// 新CRM系统SSO验证签名路由
Route::get('/pcrm/sso/verify', function () {
    return ['success' => true, 'message' => 'URL signature is valid'];
})->middleware('signed')->name('pcrm.sso.verify');

// 添加SSO跨域单点登录路由
Route::get('/sso/to-crm', [SsoController::class, 'redirectToCrm'])->name('sso.to-crm');

// 添加新CRM系统SSO跨域单点登录路由
Route::get('/sso/to-pcrm', function() {
    return redirect()->route('pcrm.redirect');
})->name('sso.to-pcrm');

// SSO测试工具路由
Route::get('/sso/test', [App\Http\Controllers\SsoTestController::class, 'showTestPage'])->name('sso.test');
Route::post('/sso/test/generate', [App\Http\Controllers\SsoTestController::class, 'generateTestLink'])->name('sso.test.generate');
