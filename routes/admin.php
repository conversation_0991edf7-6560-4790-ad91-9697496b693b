<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\PointController;
use App\Http\Controllers\Admin\PointActivityController;
use App\Http\Controllers\Admin\PointExchangeController;
use App\Http\Controllers\Admin\WeWorkController;
use App\Http\Controllers\Admin\AnalyticsController;
use App\Http\Controllers\Admin\ShippingController;
use App\Http\Controllers\Admin\WechatUserController;
use App\Http\Controllers\Admin\UserBehaviorController;
use App\Http\Controllers\Admin\UserFeedbackController;
use App\Http\Controllers\Admin\WorkWechatController;
use App\Http\Controllers\Admin\ApiExtensionController;
use App\Http\Controllers\Admin\ShopController;
use App\Http\Controllers\Admin\CommissionController;
use App\Http\Controllers\Admin\ShopSettlementController;
use App\Http\Controllers\Admin\MessageController;
use App\Http\Controllers\Admin\SmsConfigController;
use App\Http\Controllers\Admin\ShopStatisticsController;
use App\Http\Controllers\Admin\ShopShareStatsController;
use App\Http\Controllers\Admin\LuckyDrawController;
use App\Http\Controllers\Admin\SmsTemplateController;
use App\Http\Controllers\Admin\SmsPointsNotificationController;
use App\Http\Controllers\Admin\ExportController;
use App\Http\Controllers\Admin\NotificationController;
use App\Http\Controllers\Api\SsoController;

// 后台登录页面
Route::get('login', 'Admin\AuthController@showLoginForm')->name('admin.login');
Route::post('login', 'Admin\AuthController@login');
Route::post('logout', 'Admin\AuthController@logout')->name('admin.logout');

// SSO单点登录路由
Route::prefix('sso')->group(function () {
    Route::get('/login', [SsoController::class, 'loginWithSsoToken'])->name('admin.sso.login');
});

// 微信登录
Route::get('wechat-login', 'Admin\WechatAuthController@showLoginForm')->name('admin.wechat.login');
Route::post('wechat-login/generate-qr', 'Admin\WechatAuthController@generateLoginQrCode')->name('admin.wechat.generate-login-qr');
Route::get('wechat-login/check', 'Admin\WechatAuthController@checkLoginStatus')->name('admin.wechat.check-login');
Route::get('wechat-login/handle', 'Admin\WechatAuthController@handleLogin')->name('admin.wechat.handle-login');

// 测试路由 - 用于调试微信功能
Route::get('wechat-test', function() {
    $wechatService = new \App\Services\WechatService();
    $qrCode = $wechatService->generateTempQrCode('test_' . time(), 600);

    return response()->json([
        'success' => true,
        'qrcode' => $qrCode,
        'time' => date('Y-m-d H:i:s')
    ]);
})->name('admin.wechat.test');

// 需要登录的路由
Route::middleware(['auth:admin'])->group(function () {
    // 后台首页
    Route::get('/', 'Admin\DashboardController@index')->name('admin.dashboard');

    // 账号设置
    Route::get('/account/settings', 'Admin\AdminsController@accountSettings')->name('admin.account.settings');
    Route::post('/account/update', 'Admin\AdminsController@updateAccount')->name('admin.account.update');
    Route::delete('/account/wechat/unbind/{admin}', 'Admin\WechatAuthController@unbindWechat')->name('admin.wechat.unbind');
    Route::delete('/account/wework/unbind/{admin}', 'Admin\AdminsController@unbindWework')->name('admin.wework.unbind');

    // 微信绑定
    Route::post('/account/wechat/bind/generate', 'Admin\WechatAuthController@generateBindQrCode')->name('admin.wechat.bind.generate');
    Route::get('/account/wechat/bind/check', 'Admin\WechatAuthController@checkBindStatus')->name('admin.wechat.bind.check');

    // 管理员管理 - 仅超级管理员可访问
    Route::middleware(['admin.super'])->group(function () {
        Route::resource('admins', 'Admin\AdminsController')->names([
            'index' => 'admin.admins.index',
            'create' => 'admin.admins.create',
            'store' => 'admin.admins.store',
            'show' => 'admin.admins.show',
            'edit' => 'admin.admins.edit',
            'update' => 'admin.admins.update',
            'destroy' => 'admin.admins.destroy',
        ]);
        Route::post('admins/{admin}/unbind-wework', 'Admin\AdminsController@unbindWework')->name('admin.admins.unbind_wework');
    });

    // 用户管理 - 仅超级管理员可访问
    Route::middleware(['admin.super'])->group(function () {
        Route::resource('users', 'Admin\UserController')->names([
            'index' => 'admin.users.index',
            'create' => 'admin.users.create',
            'store' => 'admin.users.store',
            'show' => 'admin.users.show',
            'edit' => 'admin.users.edit',
            'update' => 'admin.users.update',
            'destroy' => 'admin.users.destroy',
        ]);
        Route::get('users/list/json', 'Admin\UserController@listJson')->name('users.list.json');
        Route::post('users/{user}/link-wechat', 'Admin\UserController@linkWechatUser')->name('admin.users.link_wechat');
        Route::post('users/sync-wechat', 'Admin\UserController@syncWechatUsers')->name('admin.users.sync_wechat');
        Route::post('users/batch-delete', 'Admin\UserController@batchDelete')->name('admin.users.batch-delete');
    });

    // 企业微信管理 - 仅超级管理员可访问
    Route::middleware(['admin.super'])->prefix('wework')->name('admin.wework.')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\WeWorkController::class, 'index'])->name('index');
        Route::post('/config', [\App\Http\Controllers\Admin\WeWorkController::class, 'updateConfig'])->name('config');
        Route::post('/send', [\App\Http\Controllers\Admin\WeWorkController::class, 'sendMessage'])->name('send');
        Route::get('/messages', [\App\Http\Controllers\Admin\WeWorkController::class, 'messages'])->name('messages');

        // 企业微信消息回调路由，需要支持GET和POST请求
        Route::match(['get', 'post'], '/callback', [\App\Http\Controllers\Admin\WeWorkController::class, 'callback'])->name('callback')->withoutMiddleware(['auth:admin']);

        // 企业微信身份验证
        Route::get('/oauth', [\App\Http\Controllers\Admin\WeWorkController::class, 'oauth'])->name('oauth');
        Route::get('/oauth/callback', [\App\Http\Controllers\Admin\WeWorkController::class, 'oauthCallback'])->name('oauth.callback');
    });

    // 商品管理相关路由 - 需要商品管理员权限
    Route::middleware(['admin.product'])->group(function () {
        // 商品导出和导入路由 - 需要放在资源路由之前，使用专门的导出控制器
        Route::get('product-export', 'Admin\ProductController@export')->name('admin.products.export');
        Route::get('product-test-export', 'Admin\ProductController@testExport')->name('admin.products.test-export');
        Route::get('product-download-template', 'Admin\ProductController@downloadTemplate')->name('admin.products.download-template');
        Route::get('product-import', 'Admin\ProductController@showImport')->name('admin.products.import.show');
        Route::post('product-import', 'Admin\ProductController@import')->name('admin.products.import');

        // 新的导出路由 - 使用专门的ExportController
        Route::get('export/products', 'Admin\ExportController@exportProducts')->name('admin.export.products');
        Route::get('export/products-csv', 'Admin\ExportController@exportProductsCsv')->name('admin.export.products-csv');

        // 商品管理
        Route::resource('products', 'Admin\ProductController')->names([
            'index' => 'admin.products.index',
            'create' => 'admin.products.create',
            'store' => 'admin.products.store',
            'show' => 'admin.products.show',
            'edit' => 'admin.products.edit',
            'update' => 'admin.products.update',
            'destroy' => 'admin.products.destroy',
        ]);

        // 商品积分设置和批量操作
        Route::post('products/update-points', 'Admin\ProductController@updatePoints')->name('admin.products.update-points');
        Route::post('products/batch', 'Admin\ProductController@batchAction')->name('admin.products.batch');

        // 批量上传商品图片
        Route::post('products/batch-upload-images', 'Admin\ProductController@batchUploadImages')->name('admin.products.batch-upload-images');

        // 图片上传 (用于富文本编辑器)
        Route::post('upload-image', 'Admin\ProductController@uploadImage');

        // 分类管理
        Route::resource('categories', 'Admin\CategoryController')->names([
            'index' => 'admin.categories.index',
            'create' => 'admin.categories.create',
            'store' => 'admin.categories.store',
            'show' => 'admin.categories.show',
            'edit' => 'admin.categories.edit',
            'update' => 'admin.categories.update',
            'destroy' => 'admin.categories.destroy',
        ]);
    });

    // 订单管理相关路由 - 需要订单管理员权限
    Route::middleware(['admin.order'])->group(function () {
        // 订单导出 - 需要放在订单资源路由之前
        Route::get('orders/export', 'Admin\OrderController@export')->name('admin.orders.export');

        // 订单管理
        Route::resource('orders', 'Admin\OrderController')->names([
            'index' => 'admin.orders.index',
            'show' => 'admin.orders.show',
            'edit' => 'admin.orders.edit',
            'update' => 'admin.orders.update',
            'destroy' => 'admin.orders.destroy'
        ]);

        // 订单打印
        Route::get('orders/{id}/print', 'Admin\OrderController@print')->name('admin.orders.print');

        // 积分兑换订单详情
        Route::get('orders/point-exchange/{orderNo}', 'Admin\OrderController@showPointExchangeOrder')->name('admin.orders.point_exchange');
    });

    // 系统设置 - 仅超级管理员可访问
    Route::middleware(['admin.super'])->group(function () {
        Route::get('settings', 'Admin\SettingController@index')->name('admin.settings.index');
        Route::put('settings', 'Admin\SettingController@update')->name('admin.settings.update');
    });

    // 积分系统管理 - 需要积分管理员权限
    Route::middleware(['admin.point'])->prefix('points')->name('admin.points.')->group(function () {
        // 积分记录
        Route::get('/', 'Admin\PointController@index')->name('index');
        Route::get('/create', 'Admin\PointController@create')->name('create');
        Route::post('/', 'Admin\PointController@store')->name('store');
        Route::post('/adjust', 'Admin\PointController@adjust')->name('adjust');
        Route::get('/export', 'Admin\PointController@export')->name('export');
        Route::get('/import', 'Admin\PointController@showImport')->name('import.show');
        Route::post('/import', 'Admin\PointController@import')->name('import');
        Route::get('/download-template', 'Admin\PointController@downloadTemplate')->name('download.template');

        // 用户导入功能 - 与积分管理相关
        Route::get('/users/import', 'Admin\UserController@showImport')->name('users.import.show');
        Route::post('/users/import', 'Admin\UserController@import')->name('users.import');
        Route::get('/users/download-template', 'Admin\UserController@downloadTemplate')->name('users.download.template');

        // 积分短信通知
        Route::get('/notification', 'Admin\SmsPointsNotificationController@index')->name('notification.index');
        Route::post('/notification/send', 'Admin\SmsPointsNotificationController@send')->name('notification.send');
        Route::post('/notification/batch-send', 'Admin\SmsPointsNotificationController@batchSend')->name('notification.batch-send');

        // 积分规则
        Route::get('/rules', 'Admin\PointController@rules')->name('rules');
        Route::post('/rules', 'Admin\PointController@updateRules')->name('rules.update');

        // 积分活动
        Route::get('activities/statistics', 'Admin\PointActivityController@statistics')->name('activities.statistics');
        Route::get('activities', 'Admin\PointActivityController@index')->name('activities.index');
        Route::get('activities/create', 'Admin\PointActivityController@create')->name('activities.create');
        Route::post('activities', 'Admin\PointActivityController@store')->name('activities.store');
        Route::get('activities/{activity}', 'Admin\PointActivityController@show')->name('activities.show');
        Route::get('activities/{activity}/edit', 'Admin\PointActivityController@edit')->name('activities.edit');
        Route::put('activities/{activity}', 'Admin\PointActivityController@update')->name('activities.update');
        Route::delete('activities/{activity}', 'Admin\PointActivityController@destroy')->name('activities.destroy');

        // 积分兑换
        Route::get('exchanges/statistics', 'Admin\PointExchangeController@statistics')->name('exchanges.statistics');
        Route::get('exchanges/statistics/export', 'Admin\PointExchangeController@exportStatistics')->name('exchanges.statistics.export');
        Route::get('exchanges/records', 'Admin\PointExchangeController@records')->name('exchanges.records');
        Route::get('exchanges/records/{id}', 'Admin\PointExchangeController@showRecord')->name('exchanges.record.show');

        Route::resource('exchanges', 'Admin\PointExchangeController')->names([
            'index' => 'exchanges.index',
            'create' => 'exchanges.create',
            'store' => 'exchanges.store',
            'show' => 'exchanges.show',
            'edit' => 'exchanges.edit',
            'update' => 'exchanges.update',
            'destroy' => 'exchanges.destroy'
        ]);

        Route::get('/exchanges/{exchange}/records', 'Admin\PointExchangeController@exchangeRecords')->name('exchanges.exchange_records');
        Route::post('/exchanges/{exchange}/ship', 'Admin\PointExchangeController@ship')->name('exchanges.ship');
        Route::post('/exchanges/{exchange}/complete', 'Admin\PointExchangeController@complete')->name('exchanges.complete');
        Route::post('/exchanges/{exchange}/cancel', 'Admin\PointExchangeController@cancel')->name('exchanges.cancel');

        // 积分兑换订单操作路由
        Route::post('/points/exchanges/{exchange}/ship', 'Admin\PointExchangeController@ship')->name('admin.points.exchanges.ship');
        Route::post('/points/exchanges/{exchange}/complete', 'Admin\PointExchangeController@complete')->name('admin.points.exchanges.complete');
        Route::post('/points/exchanges/{exchange}/cancel', 'Admin\PointExchangeController@cancel')->name('admin.points.exchanges.cancel');
    });

    // 抽奖管理 - 需要积分管理员权限
    Route::middleware(['admin.point'])->group(function () {
        // 抽奖活动路由
        Route::resource('lucky-draw', 'Admin\LuckyDrawController')->names([
            'index' => 'admin.lucky-draw.index',
            'create' => 'admin.lucky-draw.create',
            'store' => 'admin.lucky-draw.store',
            'show' => 'admin.lucky-draw.show',
            'edit' => 'admin.lucky-draw.edit',
            'update' => 'admin.lucky-draw.update',
            'destroy' => 'admin.lucky-draw.destroy',
        ]);

        // 特定活动的抽奖记录列表
        Route::get('lucky-draw/{lucky_draw}/records', 'Admin\LuckyDrawController@records')->name('admin.lucky-draw.records');

        // 抽奖记录状态更新
        Route::post('lucky-draw-records/{record}/ship', 'Admin\LuckyDrawController@shipPrize')->name('admin.lucky-draw.records.ship');
        Route::post('lucky-draw-records/{record}/complete', 'Admin\LuckyDrawController@completePrize')->name('admin.lucky-draw.records.complete');
        Route::post('lucky-draw-records/{record}/cancel', 'Admin\LuckyDrawController@cancelPrize')->name('admin.lucky-draw.records.cancel');
    });

    // 物流管理 - 需要物流管理员权限
    Route::middleware(['admin.logistics'])->prefix('shipping')->name('admin.shipping.')->group(function () {
        // 物流方式管理
        Route::get('methods', [ShippingController::class, 'methods'])->name('methods.index');
        Route::get('methods/create', [ShippingController::class, 'createMethod'])->name('methods.create');
        Route::post('methods', [ShippingController::class, 'storeMethod'])->name('methods.store');
        Route::get('methods/{method}/edit', [ShippingController::class, 'editMethod'])->name('methods.edit');
        Route::put('methods/{method}', [ShippingController::class, 'updateMethod'])->name('methods.update');
        Route::delete('methods/{method}', [ShippingController::class, 'destroyMethod'])->name('methods.destroy');

        // 物流跟踪
        Route::get('tracks', [ShippingController::class, 'tracks'])->name('tracks.index');
        Route::get('tracks/create', [ShippingController::class, 'createTrack'])->name('tracks.create');
        Route::post('tracks', [ShippingController::class, 'storeTrack'])->name('tracks.store');
        Route::get('tracks/{track}/edit', [ShippingController::class, 'editTrack'])->name('tracks.edit');
        Route::put('tracks/{track}', [ShippingController::class, 'updateTrack'])->name('tracks.update');
        Route::delete('tracks/{track}', [ShippingController::class, 'destroyTrack'])->name('tracks.destroy');

        // 物流统计
        Route::get('statistics', [ShippingController::class, 'statistics'])->name('statistics');
        Route::get('statistics/export', [ShippingController::class, 'exportStatistics'])->name('statistics.export');
    });

    // 数据分析
    Route::get('analytics', [AnalyticsController::class, 'index'])->name('admin.analytics.index');
    // Route::get('analytics/export', [AnalyticsController::class, 'export'])->name('admin.analytics.export'); // Commented out as export is not implemented
    Route::get('analytics/data', [AnalyticsController::class, 'getData'])->name('admin.analytics.data');
    // Route::get('api/analytics', [AnalyticsController::class, 'getData']); // Removed duplicate/alternative route

    // 店铺分享统计
    Route::prefix('shops')->name('admin.shops.')->group(function () {
        Route::get('shares/stats', [ShopShareStatsController::class, 'index'])->name('shares.stats');
        Route::get('shares/stats/detail', [ShopShareStatsController::class, 'detail'])->name('shares.stats.detail');
        Route::get('shares/stats/export', [ShopShareStatsController::class, 'export'])->name('shares.stats.export');
    });

    // 微信小程序管理
    Route::prefix('wechat')->name('admin.wechat.')->group(function () {
        // 微信用户管理
        Route::get('users/sync', 'Admin\WechatUserController@sync')->name('users.sync');
        Route::get('users/sync-users', 'Admin\WechatUserController@syncUsers')->name('users.sync_users');
        Route::resource('users', 'Admin\WechatUserController')->except(['create', 'edit', 'store'])->names([
            'index' => 'users.index',
            'show' => 'users.show',
            'update' => 'users.update',
            'destroy' => 'users.destroy'
        ]);

        // 用户行为管理
        Route::get('behaviors/export', 'Admin\UserBehaviorController@export')->name('behaviors.export');
        Route::resource('behaviors', 'Admin\UserBehaviorController')->except(['create', 'edit', 'store'])->names([
            'index' => 'behaviors.index',
            'show' => 'behaviors.show',
            'destroy' => 'behaviors.destroy'
        ]);

        // 用户反馈管理
        Route::get('feedbacks/export', 'Admin\UserFeedbackController@export')->name('feedbacks.export');
        Route::resource('feedbacks', 'Admin\UserFeedbackController')->except(['create', 'edit', 'store'])->names([
            'index' => 'feedbacks.index',
            'show' => 'feedbacks.show',
            'update' => 'feedbacks.update',
            'destroy' => 'feedbacks.destroy'
        ]);
    });

    // API扩展管理
    Route::resource('api-extensions', 'Admin\ApiExtensionController')->names([
        'index' => 'admin.api-extensions.index',
        'create' => 'admin.api-extensions.create',
        'store' => 'admin.api-extensions.store',
        'show' => 'admin.api-extensions.show',
        'edit' => 'admin.api-extensions.edit',
        'update' => 'admin.api-extensions.update',
        'destroy' => 'admin.api-extensions.destroy'
    ]);
    Route::patch('api-extensions/{apiExtension}/toggle-status', 'Admin\ApiExtensionController@toggleStatus')
        ->name('admin.api-extensions.toggle-status');

    // 消息管理
    Route::prefix('messages')->name('admin.messages.')->group(function () {
        // 导出路由放在前面，确保能被正确匹配
        Route::get('/export', [MessageController::class, 'export'])->name('export');

        Route::get('/', [MessageController::class, 'index'])->name('index');
        Route::get('/create', [MessageController::class, 'create'])->name('create');
        Route::post('/', [MessageController::class, 'store'])->name('store');
        Route::get('/{message}', [MessageController::class, 'show'])->name('show');
        Route::get('/{message}/edit', [MessageController::class, 'edit'])->name('edit');
        Route::put('/{message}', [MessageController::class, 'update'])->name('update');
        Route::delete('/{message}', [MessageController::class, 'destroy'])->name('destroy');
        Route::post('/{message}/send', [MessageController::class, 'send'])->name('send');
        Route::post('/{message}/cancel', [MessageController::class, 'cancel'])->name('cancel');

        // 短信相关路由
        Route::get('/sms/config', [MessageController::class, 'smsConfig'])->name('sms_config');
        Route::post('/sms/config/update', [MessageController::class, 'updateSmsConfig'])->name('update_sms_config');
        Route::post('/sms/config/test', [MessageController::class, 'testSmsConfig'])->name('test_sms_config');

        // 短信模板管理
        Route::get('/sms/templates', [MessageController::class, 'smsTemplates'])->name('sms_templates');
        Route::get('/sms/templates/create', [MessageController::class, 'createSmsTemplate'])->name('create_sms_template');
        Route::post('/sms/templates', [MessageController::class, 'storeSmsTemplate'])->name('store_sms_template');
        Route::get('/sms/templates/{template}/edit', [MessageController::class, 'editSmsTemplate'])->name('edit_sms_template');
        Route::put('/sms/templates/{template}', [MessageController::class, 'updateSmsTemplate'])->name('update_sms_template');
        Route::delete('/sms/templates/{template}', [MessageController::class, 'destroySmsTemplate'])->name('destroy_sms_template');
        Route::post('/sms/templates/{template}/test', [MessageController::class, 'testSmsTemplate'])->name('test_sms_template');

        // 短信日志相关路由
        Route::get('/sms/logs', [MessageController::class, 'smsLogs'])->name('sms_logs');
        Route::post('/sms/resend', [MessageController::class, 'resendSms'])->name('resend_sms');
    });

    // 多店铺商城管理
    Route::prefix('shops')->name('admin.shops.')->group(function () {
        // 店铺列表管理
        Route::get('/', 'Admin\ShopController@index')->name('index');
        Route::get('/{shop}', 'Admin\ShopController@show')->name('show');
        Route::get('/{shop}/edit', 'Admin\ShopController@edit')->name('edit');
        Route::put('/{shop}', 'Admin\ShopController@update')->name('update');
        Route::delete('/{shop}', 'Admin\ShopController@destroy')->name('destroy');
        Route::patch('/{shop}/toggle-status', 'Admin\ShopController@toggleStatus')->name('toggle-status');

        // 店铺申请管理
        Route::get('/apply/list', [ShopController::class, 'applyList'])->name('apply');
        Route::post('/apply/{shop}/approve', [ShopController::class, 'approveApplication'])->name('apply.approve');
        Route::post('/apply/{shop}/reject', [ShopController::class, 'rejectApplication'])->name('apply.reject');

        // 店铺结算管理
        Route::get('/settlement/list', 'Admin\ShopSettlementController@index')->name('settlement');
        Route::get('/settlement/{id}', 'Admin\ShopSettlementController@show')->name('settlement.show');
        Route::put('/settlement/{id}/status', 'Admin\ShopSettlementController@updateStatus')->name('settlement.status');

        // 佣金设置
        Route::get('/commission/settings', 'Admin\ShopController@commissionSettings')->name('commission');
        Route::post('/commission/settings', 'Admin\ShopController@updateCommissionSettings')->name('commission.update');
        Route::get('/commission/records', 'Admin\ShopController@commissionRecords')->name('commission.records');

        // 店铺统计
        Route::prefix('statistics')->name('shops.statistics.')->group(function () {
            Route::get('/orders', 'Admin\ShopController@ordersStatistics')->name('orders');
            Route::get('/export', 'Admin\ShopController@exportStatistics')->name('export');
            Route::get('/overview', 'Admin\ShopStatisticsController@overview')->name('overview');
            Route::get('/{type}', 'Admin\ShopStatisticsController@getStatisticsData')->name('data');
        });
    });

    // 佣金管理
    Route::prefix('commission')->name('commission.')->group(function () {
        Route::get('/settings', [CommissionController::class, 'settings'])->name('settings');
        Route::post('/settings/global', [CommissionController::class, 'storeGlobalSetting'])->name('settings.global.store');
        Route::post('/settings/shop', [CommissionController::class, 'storeShopSetting'])->name('settings.shop.store');
        Route::delete('/settings/{setting}', [CommissionController::class, 'deleteSetting'])->name('settings.delete');

        Route::get('/records', [CommissionController::class, 'records'])->name('records');
        Route::post('/commission/{commission}/status', [CommissionController::class, 'updateStatus'])->name('status.update');
        Route::post('/adjustment', [CommissionController::class, 'createAdjustment'])->name('adjustment.create');

        Route::get('/report', [CommissionController::class, 'generateReport'])->name('report');
    });

    // 用户搜索 API
    Route::get('api/users/search', 'Admin\UserController@search')->name('admin.api.users.search');

    // 短信模板管理路由
    Route::prefix('sms')->name('admin.sms.')->group(function () {
        // 短信模板管理
        Route::get('/templates', [SmsTemplateController::class, 'index'])->name('templates.index');
        Route::get('/templates/create', [SmsTemplateController::class, 'create'])->name('templates.create');
        Route::post('/templates', [SmsTemplateController::class, 'store'])->name('templates.store');
        Route::get('/templates/{template}', [SmsTemplateController::class, 'show'])->name('templates.show');
        Route::get('/templates/{template}/edit', [SmsTemplateController::class, 'edit'])->name('templates.edit');
        Route::put('/templates/{template}', [SmsTemplateController::class, 'update'])->name('templates.update');
        Route::delete('/templates/{template}', [SmsTemplateController::class, 'destroy'])->name('templates.destroy');
        Route::patch('/templates/{template}/toggle-status', [SmsTemplateController::class, 'toggleStatus'])->name('templates.toggle-status');
        Route::post('/templates/{template}/test-send', [SmsTemplateController::class, 'testSend'])->name('templates.test-send');
    });

    // 管理员通知路由
    Route::prefix('notifications')->name('admin.notifications.')->group(function() {
        Route::get('/', 'Admin\NotificationController@index')->name('index');
        Route::post('/mark-all-read', 'Admin\NotificationController@markAllAsRead')->name('mark-all-read');
        Route::post('/{id}/read', 'Admin\NotificationController@markAsRead')->name('read');
        Route::delete('/{id}', 'Admin\NotificationController@destroy')->name('destroy');
        Route::delete('/', 'Admin\NotificationController@destroyAll')->name('destroy-all');
    });

    // 添加重定向路由，处理旧通知链接
    Route::get('points/exchanges/records/{id}', function($id) {
        $record = \App\Models\PointExchangeRecord::findOrFail($id);
        return redirect()->route('admin.orders.point_exchange', $record->order_no);
    });
});