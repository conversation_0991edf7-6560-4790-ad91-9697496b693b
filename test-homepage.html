<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>积分商城首页 - 京东风格优化预览</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        /* 京东风格主题色 */
        :root {
            --jd-red: #e93b3d;
            --jd-red-light: #f10215;
            --jd-gray: #f5f5f5;
            --jd-orange: #ff6700;
            --jd-blue: #005aa0;
            --jd-green: #00b74a;
            --jd-yellow: #ffcc00;
        }

        /* 整体布局优化 */
        body {
            background-color: var(--jd-gray);
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            color: #333;
        }

        /* 顶部轮播横幅 - 京东风格 */
        .hero-banner {
            background: linear-gradient(135deg, var(--jd-red-light), var(--jd-red));
            border-radius: 8px;
            overflow: hidden;
            margin-bottom: 20px;
            box-shadow: 0 4px 12px rgba(233, 59, 61, 0.2);
        }

        .banner-slide {
            position: relative;
            height: 200px;
            background: linear-gradient(135deg, var(--jd-red-light), var(--jd-red));
            display: flex;
            align-items: center;
            color: white;
            padding: 0 30px;
        }

        .banner-content h1 {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .banner-content p {
            font-size: 1.1rem;
            margin-bottom: 20px;
            opacity: 0.9;
        }

        .banner-buttons .btn {
            margin-right: 10px;
            margin-bottom: 10px;
            border-radius: 20px;
            padding: 8px 20px;
            font-weight: 600;
            transition: all 0.3s;
        }

        .banner-buttons .btn-light {
            background: white;
            color: var(--jd-red);
            border: none;
        }

        .banner-buttons .btn-light:hover {
            background: #f8f9fa;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .banner-buttons .btn-outline-light {
            border: 2px solid white;
            color: white;
            background: transparent;
        }

        .banner-buttons .btn-outline-light:hover {
            background: white;
            color: var(--jd-red);
            transform: translateY(-2px);
        }

        /* 快捷入口 - 京东风格九宫格 */
        .quick-nav {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }

        .quick-nav-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
        }

        .quick-nav-item {
            text-align: center;
            padding: 15px 10px;
            border-radius: 8px;
            transition: all 0.3s;
            text-decoration: none;
            color: #333;
            background: #fafafa;
        }

        .quick-nav-item:hover {
            background: var(--jd-red);
            color: white;
            transform: translateY(-3px);
            box-shadow: 0 4px 12px rgba(233, 59, 61, 0.3);
            text-decoration: none;
        }

        .quick-nav-icon {
            width: 40px;
            height: 40px;
            margin: 0 auto 8px;
            background: var(--jd-red);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            transition: all 0.3s;
        }

        .quick-nav-item:hover .quick-nav-icon {
            background: white;
            color: var(--jd-red);
        }

        .quick-nav-text {
            font-size: 13px;
            font-weight: 600;
        }

        /* 分类标题 - 京东风格 */
        .section-header {
            background: white;
            border-radius: 8px 8px 0 0;
            padding: 15px 20px;
            margin-bottom: 0;
            border-bottom: 2px solid var(--jd-red);
        }

        .section-title {
            font-size: 18px;
            font-weight: 700;
            color: #333;
            margin: 0;
            display: flex;
            align-items: center;
        }

        .section-title .title-icon {
            width: 24px;
            height: 24px;
            background: var(--jd-red);
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-right: 10px;
            font-size: 12px;
        }

        .section-more {
            color: var(--jd-red);
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            display: flex;
            align-items: center;
        }

        .section-more:hover {
            color: var(--jd-red-light);
            text-decoration: none;
        }

        /* 商品卡片 - 京东风格 */
        .section-content {
            background: white;
            border-radius: 0 0 8px 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            margin-bottom: 20px;
        }

        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
            gap: 15px;
        }

        .product-card {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
            transition: all 0.3s;
            position: relative;
            cursor: pointer;
        }

        .product-card:hover {
            border-color: var(--jd-red);
            box-shadow: 0 4px 16px rgba(233, 59, 61, 0.15);
            transform: translateY(-2px);
        }

        .product-image {
            position: relative;
            width: 100%;
            height: 160px;
            overflow: hidden;
        }

        .product-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s;
        }

        .product-card:hover .product-image img {
            transform: scale(1.05);
        }

        .product-badge {
            position: absolute;
            top: 8px;
            right: 8px;
            background: linear-gradient(45deg, var(--jd-red), var(--jd-red-light));
            color: white;
            padding: 4px 8px;
            border-radius: 10px;
            font-size: 11px;
            font-weight: 600;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .product-tag {
            position: absolute;
            top: 8px;
            left: 8px;
            background: var(--jd-orange);
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 10px;
            font-weight: 600;
        }

        .product-info {
            padding: 12px;
        }

        .product-title {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            line-height: 1.4;
            height: 2.8em;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            margin-bottom: 8px;
        }

        .product-title:hover {
            color: var(--jd-red);
        }

        .product-price {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 10px;
        }

        .price-points {
            color: var(--jd-red);
            font-size: 16px;
            font-weight: 700;
            font-family: "Arial", sans-serif;
        }

        .price-points::before {
            content: "￥";
            font-size: 12px;
            margin-right: 2px;
        }

        .price-unit {
            color: #999;
            font-size: 12px;
            margin-left: 2px;
        }

        .btn-exchange {
            width: 100%;
            background: linear-gradient(45deg, var(--jd-red), var(--jd-red-light));
            border: none;
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 13px;
            font-weight: 600;
            transition: all 0.3s;
        }

        .btn-exchange:hover {
            background: linear-gradient(45deg, var(--jd-red-light), #d73027);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(233, 59, 61, 0.3);
            color: white;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .banner-slide {
                height: 150px;
                padding: 0 20px;
            }

            .banner-content h1 {
                font-size: 1.5rem;
            }

            .banner-content p {
                font-size: 1rem;
            }

            .quick-nav-grid {
                grid-template-columns: repeat(4, 1fr);
                gap: 10px;
            }

            .quick-nav-item {
                padding: 10px 5px;
            }

            .quick-nav-icon {
                width: 35px;
                height: 35px;
                font-size: 16px;
            }

            .quick-nav-text {
                font-size: 12px;
            }

            .product-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 10px;
            }

            .product-image {
                height: 140px;
            }
        }
    </style>
</head>
<body>
    <div class="container mt-3">
        <!-- 顶部横幅 -->
        <div class="hero-banner">
            <div class="banner-slide">
                <div class="banner-content">
                    <h1><i class="fas fa-coins me-2"></i>欢迎来到积分商城</h1>
                    <p>用积分兑换心仪商品，享受购物新体验</p>
                    <div class="banner-buttons">
                        <a href="#" class="btn btn-light">
                            <i class="fas fa-shopping-cart me-2"></i>立即购物
                        </a>
                        <a href="#" class="btn btn-outline-light">
                            <i class="fas fa-user me-2"></i>登录账户
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快捷导航 -->
        <div class="quick-nav">
            <div class="quick-nav-grid">
                <a href="#" class="quick-nav-item">
                    <div class="quick-nav-icon">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div class="quick-nav-text">商品兑换</div>
                </a>
                <a href="#" class="quick-nav-item">
                    <div class="quick-nav-icon">
                        <i class="fas fa-history"></i>
                    </div>
                    <div class="quick-nav-text">兑换记录</div>
                </a>
                <a href="#" class="quick-nav-item">
                    <div class="quick-nav-icon">
                        <i class="fas fa-store"></i>
                    </div>
                    <div class="quick-nav-text">店铺大全</div>
                </a>
                <a href="#" class="quick-nav-item">
                    <div class="quick-nav-icon">
                        <i class="fas fa-gift"></i>
                    </div>
                    <div class="quick-nav-text">积分活动</div>
                </a>
            </div>
        </div>

        <!-- 热门商品区域 -->
        <div class="section-header">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="section-title">
                    <span class="title-icon">
                        <i class="fas fa-fire"></i>
                    </span>
                    热门商品
                </h2>
                <a href="#" class="section-more">
                    查看全部 <i class="fas fa-angle-right ms-1"></i>
                </a>
            </div>
        </div>
        <div class="section-content">
            <div class="product-grid">
                <!-- 示例商品卡片 -->
                <div class="product-card">
                    <div class="product-tag">HOT</div>
                    <div class="product-image">
                        <img src="https://via.placeholder.com/160x160/e93b3d/ffffff?text=商品1" alt="商品1">
                        <div class="product-badge">6950 积分</div>
                    </div>
                    <div class="product-info">
                        <div class="product-title">ThinkPlus联想128GB Type-C USB3.2 高速U盘</div>
                        <div class="product-price">
                            <span class="price-points">6950</span>
                            <span class="price-unit">积分</span>
                        </div>
                        <button class="btn-exchange">
                            <i class="fas fa-exchange-alt me-1"></i>立即兑换
                        </button>
                    </div>
                </div>

                <div class="product-card">
                    <div class="product-image">
                        <img src="https://via.placeholder.com/160x160/1890ff/ffffff?text=商品2" alt="商品2">
                        <div class="product-badge">34900 积分</div>
                    </div>
                    <div class="product-info">
                        <div class="product-title">米家宠物喂食器2 小米自动狗狗猫咪喂食器</div>
                        <div class="product-price">
                            <span class="price-points">34900</span>
                            <span class="price-unit">积分</span>
                        </div>
                        <button class="btn-exchange">
                            <i class="fas fa-exchange-alt me-1"></i>立即兑换
                        </button>
                    </div>
                </div>

                <div class="product-card">
                    <div class="product-image">
                        <img src="https://via.placeholder.com/160x160/52c41a/ffffff?text=商品3" alt="商品3">
                        <div class="product-badge">2890 积分</div>
                    </div>
                    <div class="product-info">
                        <div class="product-title">小米手环8 NFC版 智能运动手环 血氧监测</div>
                        <div class="product-price">
                            <span class="price-points">2890</span>
                            <span class="price-unit">积分</span>
                        </div>
                        <button class="btn-exchange">
                            <i class="fas fa-exchange-alt me-1"></i>立即兑换
                        </button>
                    </div>
                </div>

                <div class="product-card">
                    <div class="product-image">
                        <img src="https://via.placeholder.com/160x160/ff6700/ffffff?text=商品4" alt="商品4">
                        <div class="product-badge">1590 积分</div>
                    </div>
                    <div class="product-info">
                        <div class="product-title">无线蓝牙耳机 降噪运动耳机 长续航</div>
                        <div class="product-price">
                            <span class="price-points">1590</span>
                            <span class="price-unit">积分</span>
                        </div>
                        <button class="btn-exchange">
                            <i class="fas fa-exchange-alt me-1"></i>立即兑换
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
