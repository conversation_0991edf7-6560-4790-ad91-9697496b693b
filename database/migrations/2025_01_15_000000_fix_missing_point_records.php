<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use App\Models\PointsExchange;
use App\Models\PointRecord;

class FixMissingPointRecords extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // 为现有的积分兑换记录补充积分记录
        $exchanges = PointsExchange::all();
        
        foreach ($exchanges as $exchange) {
            // 检查是否已经有对应的积分记录
            $existingRecord = PointRecord::where('user_id', $exchange->user_id)
                                        ->where('type', 'exchange')
                                        ->where('description', 'like', "%{$exchange->order_no}%")
                                        ->first();
            
            if (!$existingRecord) {
                // 创建积分扣除记录
                PointRecord::create([
                    'user_id' => $exchange->user_id,
                    'points' => -$exchange->points_cost,
                    'type' => 'exchange',
                    'description' => "积分兑换商品，订单号：{$exchange->order_no}",
                    'created_at' => $exchange->created_at,
                    'updated_at' => $exchange->updated_at,
                ]);
                
                // 如果订单已取消，还需要创建退回积分的记录
                if ($exchange->status === 'cancelled') {
                    PointRecord::create([
                        'user_id' => $exchange->user_id,
                        'points' => $exchange->points_cost,
                        'type' => 'exchange',
                        'description' => "取消积分兑换订单退回积分，订单号：{$exchange->order_no}",
                        'created_at' => $exchange->updated_at,
                        'updated_at' => $exchange->updated_at,
                    ]);
                }
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // 删除由此迁移创建的积分记录
        PointRecord::where('type', 'exchange')
                   ->where('description', 'like', '%积分兑换商品，订单号：%')
                   ->orWhere('description', 'like', '%取消积分兑换订单退回积分，订单号：%')
                   ->delete();
    }
}
