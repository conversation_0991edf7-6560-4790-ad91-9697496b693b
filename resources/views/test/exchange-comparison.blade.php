@extends('layouts.app')

@section('title', '积分兑换页面对比测试')

@section('content')
<div class="container">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-exchange-alt me-2"></i>
                        积分兑换页面版本对比
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>页面对比说明</h6>
                        <p class="mb-2">我们为积分兑换确认页面创建了两个版本：</p>
                        <ul class="mb-0">
                            <li><strong>原版页面</strong>：简洁的Bootstrap风格，适合国际化用户</li>
                            <li><strong>优化版页面</strong>：京东风格设计，更符合中国用户使用习惯</li>
                        </ul>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="card border-secondary">
                                <div class="card-header bg-secondary text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-desktop me-2"></i>
                                        原版页面
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <h6>特点：</h6>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check text-success me-2"></i>简洁的Bootstrap风格</li>
                                        <li><i class="fas fa-check text-success me-2"></i>标准的表单布局</li>
                                        <li><i class="fas fa-check text-success me-2"></i>基础的交互反馈</li>
                                        <li><i class="fas fa-check text-success me-2"></i>国际化设计语言</li>
                                    </ul>
                                    
                                    <h6 class="mt-3">测试商品：</h6>
                                    @if(isset($testProducts) && $testProducts->count() > 0)
                                        @foreach($testProducts->take(3) as $product)
                                        <div class="mb-2">
                                            <a href="{{ route('points.exchange.confirm', $product->id) }}" 
                                               class="btn btn-outline-secondary btn-sm w-100" target="_blank">
                                                <i class="fas fa-external-link-alt me-1"></i>
                                                {{ Str::limit($product->name, 20) }}
                                            </a>
                                        </div>
                                        @endforeach
                                    @else
                                        <p class="text-muted">暂无测试商品</p>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card border-danger">
                                <div class="card-header bg-danger text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-star me-2"></i>
                                        优化版页面（推荐）
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <h6>特点：</h6>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check text-success me-2"></i>京东风格视觉设计</li>
                                        <li><i class="fas fa-check text-success me-2"></i>卡片式分块布局</li>
                                        <li><i class="fas fa-check text-success me-2"></i>增强的交互体验</li>
                                        <li><i class="fas fa-check text-success me-2"></i>符合中国用户习惯</li>
                                        <li><i class="fas fa-check text-success me-2"></i>更好的移动端适配</li>
                                        <li><i class="fas fa-check text-success me-2"></i>安全感和信任感设计</li>
                                    </ul>
                                    
                                    <h6 class="mt-3">测试商品：</h6>
                                    @if(isset($testProducts) && $testProducts->count() > 0)
                                        @foreach($testProducts->take(3) as $product)
                                        <div class="mb-2">
                                            <a href="{{ route('points.exchange.confirm.optimized', $product->id) }}" 
                                               class="btn btn-outline-danger btn-sm w-100" target="_blank">
                                                <i class="fas fa-external-link-alt me-1"></i>
                                                {{ Str::limit($product->name, 20) }}
                                            </a>
                                        </div>
                                        @endforeach
                                    @else
                                        <p class="text-muted">暂无测试商品</p>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6><i class="fas fa-lightbulb me-2 text-warning"></i>使用建议</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6 class="text-primary">原版页面适用场景：</h6>
                                            <ul class="small">
                                                <li>国际化用户群体</li>
                                                <li>简洁风格偏好</li>
                                                <li>快速开发需求</li>
                                                <li>标准化设计要求</li>
                                            </ul>
                                        </div>
                                        <div class="col-md-6">
                                            <h6 class="text-danger">优化版页面适用场景：</h6>
                                            <ul class="small">
                                                <li>中国大陆用户为主</li>
                                                <li>电商平台风格</li>
                                                <li>提升转化率需求</li>
                                                <li>增强用户信任感</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="text-center mt-4">
                        <div class="btn-group" role="group">
                            <a href="{{ route('admin.products.index') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>
                                返回商品管理
                            </a>
                            <a href="{{ route('points.exchange.history') }}" class="btn btn-primary">
                                <i class="fas fa-history me-1"></i>
                                查看兑换记录
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.btn {
    transition: all 0.2s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

.border-danger {
    border-color: #e3001b !important;
}

.bg-danger {
    background-color: #e3001b !important;
}

.btn-outline-danger {
    border-color: #e3001b;
    color: #e3001b;
}

.btn-outline-danger:hover {
    background-color: #e3001b;
    border-color: #e3001b;
    color: white;
}

.text-danger {
    color: #e3001b !important;
}
</style>
@endsection
