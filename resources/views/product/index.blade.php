@extends('layouts.app')

@section('title', '积分商城 - 海量好物等你兑换')

@section('styles')
<style>
    /* 京东风格主题色 */
    :root {
        --jd-red: #e93b3d;
        --jd-red-light: #f10215;
        --jd-gray: #f5f5f5;
        --jd-light: #fff8f0;
        --jd-orange: #ff6700;
        --jd-blue: #005aa0;
    }

    /* 整体布局 */
    body {
        background-color: var(--jd-gray);
        color: #333;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    }

    /* 页面标题栏 */
    .page-header {
        background: linear-gradient(135deg, var(--jd-red-light), var(--jd-red));
        color: white;
        padding: 15px 0;
        margin-bottom: 0;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .page-header h2 {
        font-weight: 600;
        margin: 0;
        font-size: 1.5rem;
    }

    /* 顶部搜索栏 - 京东风格 */
    .search-bar {
        background: var(--jd-red);
        padding: 12px 0;
        margin-bottom: 15px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .search-box {
        max-width: 500px;
        margin: 0 auto;
    }

    .search-box .form-control {
        border-radius: 3px 0 0 3px;
        border: 2px solid #fff;
        padding: 12px 15px;
        font-size: 14px;
        box-shadow: none;
        background: #fff;
    }

    .search-box .form-control:focus {
        border-color: #fff;
        box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
    }

    .search-box .btn {
        border-radius: 0 3px 3px 0;
        padding: 12px 20px;
        background: var(--jd-orange);
        border: 2px solid var(--jd-orange);
        font-weight: 600;
        font-size: 14px;
        transition: all 0.2s;
    }

    .search-box .btn:hover {
        background: #e55a00;
        border-color: #e55a00;
        transform: translateY(-1px);
    }

    /* 分类导航 - 京东风格 */
    .category-nav {
        background: white;
        border-radius: 4px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        margin-bottom: 15px;
        overflow: hidden;
        border: 1px solid #e0e0e0;
    }

    .category-nav .nav-title {
        background: linear-gradient(to right, #f8f8f8, #fff);
        color: #333;
        padding: 15px;
        font-weight: 600;
        border-bottom: 1px solid #e0e0e0;
        margin: 0;
        font-size: 14px;
        position: relative;
    }

    .category-nav .nav-title::after {
        content: '';
        position: absolute;
        left: 0;
        bottom: 0;
        width: 100%;
        height: 2px;
        background: linear-gradient(to right, var(--jd-red), var(--jd-orange));
    }

    .category-nav .nav-link {
        color: #666;
        padding: 12px 15px;
        border-left: 3px solid transparent;
        transition: all 0.2s;
        font-size: 13px;
        display: flex;
        align-items: center;
        text-decoration: none;
        border-bottom: 1px solid #f5f5f5;
    }

    .category-nav .nav-link:last-child {
        border-bottom: none;
    }

    .category-nav .nav-link:hover {
        color: var(--jd-red);
        background-color: #fafafa;
        border-left-color: var(--jd-orange);
        transform: translateX(2px);
    }

    .category-nav .nav-link.active {
        color: var(--jd-red);
        background-color: var(--jd-light);
        border-left-color: var(--jd-red);
        font-weight: 600;
    }

    .category-nav .nav-link i {
        width: 18px;
        text-align: center;
        margin-right: 10px;
        color: #999;
        font-size: 12px;
    }

    .category-nav .nav-link:hover i,
    .category-nav .nav-link.active i {
        color: var(--jd-red);
    }

    /* 折叠箭头动画 */
    .category-nav .nav-title i.transition-transform {
        transition: transform 0.2s ease;
    }

    .category-nav .nav-title[aria-expanded="true"] i.transition-transform {
        transform: rotate(180deg);
    }

    .category-nav .nav-title:hover {
        background: linear-gradient(to right, #f0f0f0, #fafafa);
    }

    /* 筛选栏 - 京东风格 */
    .filter-bar {
        background: white;
        padding: 15px;
        border-radius: 4px;
        margin-bottom: 15px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        border: 1px solid #e0e0e0;
    }

    .filter-bar .btn-group .btn {
        border: 1px solid #ddd;
        background: white;
        color: #666;
        font-size: 13px;
        padding: 8px 16px;
        border-radius: 3px;
        margin-right: 8px;
        margin-bottom: 8px;
        transition: all 0.2s;
        font-weight: 500;
    }

    .filter-bar .btn-group .btn:hover {
        border-color: var(--jd-red);
        color: var(--jd-red);
        background: #fafafa;
    }

    .filter-bar .btn-group .btn.active {
        background: var(--jd-red);
        color: white;
        border-color: var(--jd-red);
        box-shadow: 0 2px 4px rgba(233, 59, 61, 0.2);
    }

    .filter-bar .form-check {
        margin-right: 20px;
        margin-bottom: 8px;
    }

    .filter-bar .form-check-label {
        font-size: 13px;
        cursor: pointer;
        font-weight: 500;
        color: #666;
    }

    .filter-bar .form-check-input {
        margin-top: 0.2em;
    }

    .filter-bar .form-check-input:checked {
        background-color: var(--jd-red);
        border-color: var(--jd-red);
    }

    .filter-bar .form-check-input:focus {
        box-shadow: 0 0 0 0.2rem rgba(233, 59, 61, 0.25);
    }

    /* 商品卡片 - 京东风格 */
    .product-card-wrapper {
        margin-bottom: 15px;
        padding: 0 5px;
    }

    .product-card {
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        overflow: hidden;
        transition: all 0.2s ease;
        background-color: white;
        box-shadow: 0 1px 2px rgba(0,0,0,0.05);
        position: relative;
        height: 100%;
        cursor: pointer;
    }

    .product-card:hover {
        border-color: var(--jd-red);
        box-shadow: 0 2px 8px rgba(233, 59, 61, 0.15);
        transform: translateY(-2px);
    }

    .product-card .card-img-top {
        height: 200px;
        object-fit: cover;
        transition: transform 0.2s ease;
        border-bottom: 1px solid #f0f0f0;
    }

    .product-card:hover .card-img-top {
        transform: scale(1.02);
    }

    /* 商品标签 - 京东风格 */
    .product-tag {
        position: absolute;
        top: 5px;
        left: 5px;
        padding: 3px 8px;
        border-radius: 2px;
        font-size: 10px;
        font-weight: 600;
        z-index: 2;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .tag-hot {
        background: linear-gradient(45deg, #ff4757, #ff3838);
        color: white;
        box-shadow: 0 2px 4px rgba(255, 71, 87, 0.3);
    }

    .tag-new {
        background: linear-gradient(45deg, #2ed573, #1dd1a1);
        color: white;
        box-shadow: 0 2px 4px rgba(46, 213, 115, 0.3);
    }

    .tag-recommend {
        background: linear-gradient(45deg, #3742fa, #2f3542);
        color: white;
        box-shadow: 0 2px 4px rgba(55, 66, 250, 0.3);
    }

    .product-card .card-body {
        padding: 12px;
        display: flex;
        flex-direction: column;
        height: calc(100% - 200px);
    }

    .product-card .card-title {
        font-size: 14px;
        margin-bottom: 10px;
        height: 40px;
        overflow: hidden;
        color: #333;
        line-height: 1.4;
        font-weight: 500;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
    }

    .product-card .card-title:hover {
        color: var(--jd-red);
    }

    /* 价格显示 - 京东风格 */
    .product-card .price-section {
        display: flex;
        align-items: baseline;
    }

    .product-card .points {
        color: var(--jd-red);
        font-size: 18px;
        font-weight: 700;
        font-family: "Arial", sans-serif;
    }

    .product-card .points-unit {
        font-size: 12px;
        font-weight: 500;
        margin-left: 2px;
        color: var(--jd-red);
    }

    .product-card .sales {
        color: #999;
        font-size: 12px;
        font-weight: 400;
    }

    /* 库存状态区域 */
    .product-card .stock-section {
        min-height: 20px;
        display: flex;
        align-items: center;
    }

    /* 库存显示样式 - 京东风格 */
    .product-card .stock {
        font-size: 12px;
        font-weight: 500;
        padding: 4px 0;
        display: flex;
        align-items: center;
    }

    .product-card .stock i {
        margin-right: 4px;
        font-size: 11px;
    }

    .product-card .stock.text-success {
        color: #52c41a !important;
    }

    .product-card .stock.text-warning {
        color: #faad14 !important;
    }

    .product-card .stock.text-danger {
        color: #ff4d4f !important;
    }

    /* 兑换按钮 - 京东风格 */
    .product-card .btn-exchange {
        background: linear-gradient(135deg, var(--jd-red), #d73027);
        color: white;
        border: none;
        border-radius: 3px;
        padding: 8px 12px;
        font-size: 13px;
        font-weight: 600;
        width: 100%;
        transition: all 0.2s;
        margin-top: auto;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .product-card .btn-exchange:hover {
        background: linear-gradient(135deg, #d73027, #c62d42);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(233, 59, 61, 0.3);
    }

    .product-card .btn-exchange:active {
        transform: translateY(0);
    }

    .product-card .btn-exchange.disabled {
        background: #ccc;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }

    /* 分页样式 - 京东风格 */
    .pagination {
        justify-content: center;
        margin-top: 30px;
        margin-bottom: 20px;
    }

    .pagination .page-link {
        border: 1px solid #ddd;
        color: #666;
        margin: 0 2px;
        border-radius: 3px;
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 13px;
        font-weight: 500;
        transition: all 0.2s;
    }

    .pagination .page-link:hover {
        border-color: var(--jd-red);
        color: var(--jd-red);
        background-color: #fafafa;
    }

    .pagination .page-item.active .page-link {
        background-color: var(--jd-red);
        border-color: var(--jd-red);
        color: white;
        box-shadow: 0 2px 4px rgba(233, 59, 61, 0.2);
    }

    .pagination .page-item.disabled .page-link {
        color: #ccc;
        border-color: #eee;
        background-color: #f8f8f8;
    }

    /* 空状态 - 京东风格 */
    .empty-state {
        text-align: center;
        padding: 60px 20px;
        background-color: white;
        border-radius: 4px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        border: 1px solid #e0e0e0;
    }

    .empty-state i {
        font-size: 64px;
        color: #ddd;
        margin-bottom: 20px;
        opacity: 0.6;
    }

    .empty-state p {
        color: #999;
        font-size: 16px;
        margin-bottom: 20px;
    }

    .empty-state .btn {
        border-radius: 3px;
        padding: 10px 20px;
        font-weight: 500;
    }

    /* 响应式调整 - 移动端优化 */
    @media (max-width: 1200px) {
        .product-card-wrapper {
            padding: 0 3px;
        }
    }

    @media (max-width: 992px) {
        .category-nav {
            margin-bottom: 15px;
        }

        .filter-bar {
            padding: 12px;
        }

        .filter-bar .btn-group .btn {
            font-size: 12px;
            padding: 6px 12px;
        }
    }

    @media (max-width: 768px) {
        .page-header {
            padding: 10px 0;
        }

        .page-header h2 {
            font-size: 1.3rem;
        }

        .search-bar {
            padding: 8px 0;
            margin-bottom: 10px;
        }

        .search-box .form-control,
        .search-box .btn {
            padding: 10px 12px;
            font-size: 13px;
        }

        .category-nav .nav-title {
            padding: 12px;
            font-size: 13px;
        }

        .category-nav .nav-link {
            padding: 10px 12px;
            font-size: 12px;
        }

        .filter-bar {
            padding: 10px;
        }

        .filter-bar .btn-group .btn {
            font-size: 11px;
            padding: 5px 10px;
            margin-right: 5px;
        }

        .filter-bar .form-check {
            margin-right: 15px;
        }

        .filter-bar .form-check-label {
            font-size: 12px;
        }

        .product-card .card-img-top {
            height: 160px;
        }

        .product-card .card-body {
            padding: 10px;
        }

        .product-card .card-title {
            font-size: 13px;
            height: 36px;
        }

        .product-card .points {
            font-size: 16px;
        }

        .product-card .btn-exchange {
            font-size: 12px;
            padding: 6px 10px;
        }

        .pagination .page-link {
            width: 32px;
            height: 32px;
            font-size: 12px;
        }
    }

    @media (max-width: 576px) {
        .product-card-wrapper {
            margin-bottom: 10px;
        }

        .product-card .card-img-top {
            height: 140px;
        }

        .product-card .card-body {
            padding: 8px;
        }

        .product-card .card-title {
            font-size: 12px;
            height: 32px;
        }

        .product-card .points {
            font-size: 14px;
        }

        .product-card .stock {
            font-size: 11px;
        }

        .product-card .btn-exchange {
            font-size: 11px;
            padding: 5px 8px;
        }
    }
</style>
@endsection

@section('content')
<!-- 页面标题栏 -->
<div class="page-header d-none d-md-block">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h2><i class="fas fa-shopping-bag me-2"></i> 积分商城</h2>
            </div>
            <div class="col-md-6 text-md-end">
                <span class="text-white">
                    <i class="fas fa-coins me-1"></i> 海量好物，等你兑换
                </span>
            </div>
        </div>
    </div>
</div>

<!-- 搜索栏 -->
<div class="search-bar">
    <div class="container">
        <form action="{{ route('products.index') }}" method="GET" class="search-box">
            <div class="input-group">
                <input type="text" class="form-control" name="search" placeholder="搜索你想要的商品..." value="{{ request('search') }}">
                <button class="btn btn-primary" type="submit">
                    <i class="fas fa-search me-1"></i> 搜索
                </button>
            </div>
        </form>
    </div>
</div>

<div class="container">
    <div class="row">
        <!-- 左侧分类导航 -->
        <div class="col-lg-3">
            <div class="category-nav">
                <h5 class="nav-title d-flex justify-content-between align-items-center"
                    data-bs-toggle="collapse"
                    href="#categoryCollapse"
                    role="button"
                    aria-expanded="false"
                    aria-controls="categoryCollapse"
                    style="cursor: pointer;">
                    <span><i class="fas fa-list-ul me-2"></i> 商品分类</span>
                    <i class="fas fa-chevron-down transition-transform"></i>
                </h5>
                <div class="collapse" id="categoryCollapse">
                    <div class="nav flex-column">
                        <a href="{{ route('products.index') }}"
                           class="nav-link {{ !request('category_id') ? 'active' : '' }}">
                            <i class="fas fa-th-large"></i> 全部商品
                        </a>
                        @foreach($categories as $category)
                            <a href="{{ route('products.index', ['category_id' => $category->id]) }}"
                               class="nav-link {{ request('category_id') == $category->id ? 'active' : '' }}">
                                <i class="fas fa-tag"></i> {{ $category->name }}
                            </a>
                        @endforeach
                    </div>
                </div>
            </div>

            <div class="category-nav">
                <h5 class="nav-title d-flex justify-content-between align-items-center"
                    data-bs-toggle="collapse"
                    href="#pointsRangeCollapse"
                    role="button"
                    aria-expanded="false"
                    aria-controls="pointsRangeCollapse"
                    style="cursor: pointer;">
                    <span><i class="fas fa-coins me-2"></i> 积分范围</span>
                    <i class="fas fa-chevron-down transition-transform"></i>
                </h5>
                <div class="collapse" id="pointsRangeCollapse">
                    <div class="nav flex-column">
                        <a href="{{ route('products.index', ['points' => '0-1000'] + request()->except('points')) }}"
                           class="nav-link {{ request('points') == '0-1000' ? 'active' : '' }}">
                            <i class="fas fa-piggy-bank"></i> 1000积分以下
                        </a>
                        <a href="{{ route('products.index', ['points' => '1000-3000'] + request()->except('points')) }}"
                           class="nav-link {{ request('points') == '1000-3000' ? 'active' : '' }}">
                            <i class="fas fa-piggy-bank"></i> 1000-3000积分
                        </a>
                        <a href="{{ route('products.index', ['points' => '3000-5000'] + request()->except('points')) }}"
                           class="nav-link {{ request('points') == '3000-5000' ? 'active' : '' }}">
                            <i class="fas fa-piggy-bank"></i> 3000-5000积分
                        </a>
                        <a href="{{ route('products.index', ['points' => '5000-+'] + request()->except('points')) }}"
                           class="nav-link {{ request('points') == '5000-+' ? 'active' : '' }}">
                            <i class="fas fa-piggy-bank"></i> 5000积分以上
                        </a>
                    </div>
                </div>
            </div>

            <!-- 热门推荐 -->
            @if(isset($hotProducts) && $hotProducts->count() > 0)
            <div class="category-nav d-none d-lg-block">
                <h5 class="nav-title"><i class="fas fa-fire me-2"></i> 热门推荐</h5>
                <div class="px-3 py-2">
                    @foreach($hotProducts->take(3) as $hotProduct)
                    <div class="d-flex mb-3 pb-3 border-bottom">
                        <div style="width: 50px; height: 50px; flex-shrink: 0;">
                            <img src="{{ asset('storage/' . $hotProduct->cover_image) }}" alt="{{ $hotProduct->name }}" class="img-fluid rounded" style="width: 100%; height: 100%; object-fit: cover;">
                        </div>
                        <div class="ms-2">
                            <a href="{{ route('points.exchange.confirm', $hotProduct->id) }}" class="text-decoration-none">
                                <div style="font-size: 12px; line-height: 1.3; height: 32px; overflow: hidden; color: #555;">{{ $hotProduct->name }}</div>
                            </a>
                            <div class="mt-1" style="font-size: 13px; color: var(--jd-red); font-weight: 600;">{{ $hotProduct->pointExchange->points_required ?? 0 }} 积分</div>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
            @endif
        </div>

        <!-- 右侧商品列表 -->
        <div class="col-lg-9">
            <!-- 筛选栏 -->
            <div class="filter-bar">
                <div class="d-flex flex-wrap justify-content-between align-items-center">
                    <div class="btn-group mb-2 mb-md-0">
                        <a href="{{ route('products.index', ['sort' => 'default'] + request()->except('sort')) }}"
                           class="btn {{ !request('sort') || request('sort') == 'default' ? 'active' : '' }}">
                            <i class="fas fa-sort me-1"></i> 默认排序
                        </a>
                        <a href="{{ route('products.index', ['sort' => 'points_asc'] + request()->except('sort')) }}"
                           class="btn {{ request('sort') == 'points_asc' ? 'active' : '' }}">
                            <i class="fas fa-sort-amount-down-alt me-1"></i> 积分从低到高
                        </a>
                        <a href="{{ route('products.index', ['sort' => 'points_desc'] + request()->except('sort')) }}"
                           class="btn {{ request('sort') == 'points_desc' ? 'active' : '' }}">
                            <i class="fas fa-sort-amount-up me-1"></i> 积分从高到低
                        </a>
                        <a href="{{ route('products.index', ['sort' => 'sales'] + request()->except('sort')) }}"
                           class="btn {{ request('sort') == 'sales' ? 'active' : '' }}">
                            <i class="fas fa-chart-line me-1"></i> 销量优先
                        </a>
                    </div>

                    <div class="d-flex flex-wrap">
                        <div class="form-check me-3">
                            <input class="form-check-input" type="checkbox" id="isHot"
                                   {{ request('is_hot') ? 'checked' : '' }}
                                   onchange="window.location.href='{{ route('products.index', ['is_hot' => request('is_hot') ? 0 : 1] + request()->except('is_hot')) }}'">
                            <label class="form-check-label" for="isHot">
                                <i class="fas fa-fire text-danger me-1"></i> 热门
                            </label>
                        </div>
                        <div class="form-check me-3">
                            <input class="form-check-input" type="checkbox" id="isNew"
                                   {{ request('is_new') ? 'checked' : '' }}
                                   onchange="window.location.href='{{ route('products.index', ['is_new' => request('is_new') ? 0 : 1] + request()->except('is_new')) }}'">
                            <label class="form-check-label" for="isNew">
                                <i class="fas fa-star text-warning me-1"></i> 新品
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="isRecommend"
                                   {{ request('is_recommend') ? 'checked' : '' }}
                                   onchange="window.location.href='{{ route('products.index', ['is_recommend' => request('is_recommend') ? 0 : 1] + request()->except('is_recommend')) }}'">
                            <label class="form-check-label" for="isRecommend">
                                <i class="fas fa-thumbs-up text-primary me-1"></i> 推荐
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 商品列表 - 京东风格网格布局 -->
            <div class="row g-3">
                @forelse($products as $product)
                    <div class="col-xl-3 col-lg-4 col-md-6 col-6 product-card-wrapper">
                        <div class="product-card">
                            <!-- 商品标签 -->
                            @if($product->is_hot)
                                <div class="product-tag tag-hot">HOT</div>
                            @elseif($product->is_new)
                                <div class="product-tag tag-new">NEW</div>
                            @elseif($product->is_recommend)
                                <div class="product-tag tag-recommend">推荐</div>
                            @endif

                            <!-- 商品图片 -->
                            <a href="{{ route('points.exchange.confirm', $product->id) }}" class="text-decoration-none">
                                <img src="{{ product_image_url($product->cover_image) }}"
                                     class="card-img-top"
                                     alt="{{ $product->name }}"
                                     loading="lazy">
                            </a>

                            <!-- 商品信息 -->
                            <div class="card-body">
                                <a href="{{ route('points.exchange.confirm', $product->id) }}" class="text-decoration-none">
                                    <h5 class="card-title" title="{{ $product->name }}">{{ Str::limit($product->name, 35) }}</h5>
                                </a>

                                <!-- 价格和销量 -->
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <div class="price-section">
                                        <span class="points">{{ $product->pointExchange->points_required ?? 0 }}</span>
                                        <span class="points-unit">积分</span>
                                    </div>
                                    <span class="sales">{{ $product->sales_count }}人兑换</span>
                                </div>

                                <!-- 库存状态 -->
                                <div class="stock-section mb-3">
                                    <span class="stock {{ $product->stock > 10 ? 'text-success' : ($product->stock > 0 ? 'text-warning' : 'text-danger') }}">
                                        <i class="fas fa-cubes"></i>
                                        @if($product->stock > 10)
                                            现货充足
                                        @elseif($product->stock > 0)
                                            仅剩{{ $product->stock }}件
                                        @else
                                            暂时缺货
                                        @endif
                                    </span>
                                </div>

                                <!-- 兑换按钮 -->
                                <a href="{{ route('points.exchange.confirm', $product->id) }}"
                                   class="btn btn-exchange {{ $product->stock == 0 ? 'disabled' : '' }}"
                                   {{ $product->stock == 0 ? 'onclick="return false;"' : '' }}>
                                    <i class="fas fa-{{ $product->stock > 0 ? 'shopping-cart' : 'times' }}"></i>
                                    {{ $product->stock > 0 ? '立即兑换' : '暂时缺货' }}
                                </a>
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="col-12">
                        <div class="empty-state">
                            <i class="fas fa-shopping-basket"></i>
                            <p>暂无相关商品，换个条件试试吧</p>
                            <a href="{{ route('products.index') }}" class="btn btn-outline-primary mt-3">
                                <i class="fas fa-sync-alt me-1"></i> 查看全部商品
                            </a>
                        </div>
                    </div>
                @endforelse
            </div>

            <!-- 分页 -->
            <div class="d-flex justify-content-center mt-4 mb-5">
                {{ $products->withQueryString()->links('pagination::bootstrap-5') }}
            </div>
        </div>
    </div>
</div>
@endsection