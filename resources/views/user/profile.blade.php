@extends('layouts.app')

@section('title', '个人资料')

@section('styles')
@include('user.partials.mobile_styles')
<style>
    /* 京东风格主题色 */
    :root {
        --jd-red: #e93b3d;
        --jd-red-light: #f10215;
        --jd-gray: #f5f5f5;
        --jd-orange: #ff6700;
        --jd-blue: #005aa0;
        --jd-green: #00b74a;
        --jd-yellow: #ffcc00;
    }

    /* 整体布局 */
    body {
        background-color: var(--jd-gray);
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    }

    /* 用户中心侧边栏 - 京东风格 */
    .user-sidebar .card {
        border: 1px solid #e0e0e0;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        overflow: hidden;
    }

    .user-sidebar .card-header {
        background: linear-gradient(135deg, var(--jd-red-light), var(--jd-red));
        color: white;
        border: none;
        padding: 20px;
        text-align: center;
    }

    .user-sidebar .card-header h5 {
        margin: 0;
        font-weight: 700;
        font-size: 1.2rem;
        text-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }

    .user-sidebar .list-group-item {
        border: none;
        border-bottom: 1px solid #f0f0f0;
        padding: 15px 20px;
        transition: all 0.3s;
        color: #666;
        font-weight: 500;
    }

    .user-sidebar .list-group-item:hover {
        background: linear-gradient(135deg, #fff8f0, #ffeee6);
        color: var(--jd-red);
        transform: translateX(5px);
    }

    .user-sidebar .list-group-item.active {
        background: linear-gradient(135deg, var(--jd-red), var(--jd-red-light));
        color: white;
        border-color: var(--jd-red);
        box-shadow: 0 4px 12px rgba(233, 59, 61, 0.3);
    }

    .user-sidebar .list-group-item i {
        width: 20px;
        text-align: center;
        margin-right: 10px;
    }

    .user-sidebar .badge {
        background: var(--jd-orange);
        color: white;
        border-radius: 10px;
        padding: 4px 8px;
        font-size: 11px;
        font-weight: 600;
    }

    /* 主内容区域 - 京东风格 */
    .main-content .card {
        border: 1px solid #e0e0e0;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        margin-bottom: 20px;
    }

    .main-content .card-header {
        background: white;
        border-bottom: 2px solid var(--jd-red);
        border-radius: 12px 12px 0 0 !important;
        padding: 0;
    }

    /* Tab导航 - 京东风格 */
    .nav-tabs {
        border: none;
        padding: 8px;
    }

    .nav-tabs .nav-link {
        border: none;
        border-radius: 8px;
        padding: 12px 24px;
        margin: 0 4px;
        color: #666;
        font-weight: 600;
        transition: all 0.3s;
    }

    .nav-tabs .nav-link:hover {
        background: #f8f9fa;
        color: var(--jd-red);
        border: none;
    }

    .nav-tabs .nav-link.active {
        background: linear-gradient(45deg, var(--jd-red), var(--jd-red-light));
        color: white;
        border: none;
        box-shadow: 0 4px 12px rgba(233, 59, 61, 0.3);
    }

    /* 头像区域 - 京东风格 */
    .avatar-section {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-radius: 12px;
        padding: 30px;
        margin-bottom: 30px;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .avatar-section::before {
        content: '';
        position: absolute;
        top: -50px;
        right: -50px;
        width: 100px;
        height: 100px;
        background: rgba(233, 59, 61, 0.1);
        border-radius: 50%;
    }

    .avatar-section img {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        border: 4px solid white;
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        margin-bottom: 15px;
        transition: transform 0.3s;
    }

    .avatar-section img:hover {
        transform: scale(1.05);
    }

    .avatar-section .btn {
        background: var(--jd-red);
        color: white;
        border: none;
        border-radius: 20px;
        padding: 8px 20px;
        font-weight: 600;
        transition: all 0.3s;
    }

    .avatar-section .btn:hover {
        background: var(--jd-red-light);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(233, 59, 61, 0.3);
        color: white;
    }

    /* 表单样式 - 京东风格 */
    .form-label {
        color: #333;
        font-weight: 600;
        margin-bottom: 8px;
    }

    .form-control {
        border: 2px solid #e0e0e0;
        border-radius: 8px;
        padding: 12px 16px;
        font-size: 14px;
        transition: all 0.3s;
    }

    .form-control:focus {
        border-color: var(--jd-red);
        box-shadow: 0 0 0 0.2rem rgba(233, 59, 61, 0.25);
    }

    .btn-primary {
        background: linear-gradient(45deg, var(--jd-red), var(--jd-red-light));
        border: none;
        border-radius: 8px;
        padding: 12px 30px;
        font-weight: 600;
        transition: all 0.3s;
    }

    .btn-primary:hover {
        background: linear-gradient(45deg, var(--jd-red-light), #d73027);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(233, 59, 61, 0.3);
    }

    /* 账号关联卡片 - 京东风格 */
    .account-link-card {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border: 1px solid #e0e0e0;
        border-radius: 12px;
        padding: 25px;
        margin-bottom: 20px;
        position: relative;
        overflow: hidden;
    }

    .account-link-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--jd-green), #52c41a);
    }

    .account-link-card h5 {
        color: #333;
        font-weight: 700;
        margin-bottom: 10px;
    }

    .account-link-card .fab {
        color: var(--jd-green);
        font-size: 1.5rem;
    }

    .account-link-card .btn {
        border-radius: 20px;
        padding: 8px 20px;
        font-weight: 600;
        transition: all 0.3s;
    }

    .account-link-card .btn-primary {
        background: var(--jd-green);
        border-color: var(--jd-green);
    }

    .account-link-card .btn-primary:hover {
        background: #389e0d;
        border-color: #389e0d;
        transform: translateY(-2px);
    }

    .account-link-card .btn-outline-primary {
        color: var(--jd-green);
        border-color: var(--jd-green);
    }

    .account-link-card .btn-outline-primary:hover {
        background: var(--jd-green);
        border-color: var(--jd-green);
        color: white;
        transform: translateY(-2px);
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .user-sidebar {
            margin-bottom: 20px;
        }

        .avatar-section {
            padding: 20px;
        }

        .avatar-section img {
            width: 100px;
            height: 100px;
        }

        .nav-tabs .nav-link {
            padding: 10px 16px;
            font-size: 14px;
        }
    }
</style>
@endsection

@section('content')
<!-- 桌面版视图 -->
<div class="desktop-view container mt-4">
    <div class="row">
        <div class="col-md-3 user-sidebar">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-user-cog me-2"></i>用户中心
                    </h5>
                </div>
                <div class="list-group list-group-flush">
                    <a href="{{ route('user.profile') }}" class="list-group-item list-group-item-action active">
                        <i class="fas fa-user-circle"></i>个人资料
                    </a>
                    <a href="{{ url('/user/notifications') }}" class="list-group-item list-group-item-action {{ request()->is('user/notifications*') ? 'active' : '' }}">
                        <i class="fas fa-bell"></i>我的通知
                        @if(Auth::user()->unreadNotifications->count() > 0)
                            <span class="badge float-end">{{ Auth::user()->unreadNotifications->count() }}</span>
                        @endif
                    </a>
                    <a href="{{ route('user.points') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-coins"></i>积分明细
                    </a>
                    <a href="{{ route('points.exchange.history') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-exchange-alt"></i>兑换记录
                    </a>
                    <a href="{{ route('shops.my') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-store"></i>我的店铺
                    </a>
                </div>
            </div>
        </div>

        <div class="col-md-9 main-content">
            <div class="card">
                <div class="card-header">
                    <ul class="nav nav-tabs card-header-tabs" id="profileTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="profile-tab" data-bs-toggle="tab" data-bs-target="#profile" type="button" role="tab" aria-controls="profile" aria-selected="true">
                                <i class="fas fa-user me-2"></i>个人资料
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="password-tab" data-bs-toggle="tab" data-bs-target="#password" type="button" role="tab" aria-controls="password" aria-selected="false">
                                <i class="fas fa-lock me-2"></i>修改密码
                            </button>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    <div class="tab-content" id="profileTabsContent">
                        <!-- 个人资料编辑 -->
                        <div class="tab-pane fade show active" id="profile" role="tabpanel" aria-labelledby="profile-tab">
                            <form method="POST" action="{{ route('user.profile.update') }}" enctype="multipart/form-data">
                                @csrf
                                @method('PUT')

                                <div class="avatar-section">
                                    @if(Auth::user()->avatar)
                                        <img src="{{ avatar_url(Auth::user()->avatar) }}" alt="{{ Auth::user()->name }} Avatar">
                                    @else
                                        <img src="{{ asset('images/default-avatar.png') }}" alt="Default Avatar">
                                    @endif
                                    <div>
                                        <label for="avatar" class="btn">
                                            <i class="fas fa-camera me-2"></i>更换头像
                                        </label>
                                        <input type="file" class="form-control d-none @error('avatar') is-invalid @enderror" id="avatar" name="avatar" accept="image/*">
                                        @error('avatar')
                                            <div class="invalid-feedback d-block text-center">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="name" class="form-label">姓名 (昵称)</label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name" value="{{ old('name', Auth::user()->name) }}">
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="username" class="form-label">登录用户名 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('username') is-invalid @enderror" id="username" name="username" value="{{ old('username', Auth::user()->username) }}" required>
                                    <small class="form-text text-muted">用于登录，只能包含字母、数字、下划线和破折号。</small>
                                    @error('username')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="email" class="form-label">电子邮箱</label>
                                    <input type="email" class="form-control @error('email') is-invalid @enderror" id="email" name="email" value="{{ old('email', Auth::user()->email) }}">
                                    @error('email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="phone" class="form-label">手机号码</label>
                                    <input type="text" class="form-control @error('phone') is-invalid @enderror" id="phone" name="phone" value="{{ old('phone', Auth::user()->phone) }}">
                                    @error('phone')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <button type="submit" class="btn btn-primary">更新资料</button>
                            </form>
                        </div>

                        <!-- 密码修改 -->
                        <div class="tab-pane fade" id="password" role="tabpanel" aria-labelledby="password-tab">
                            <form method="POST" action="{{ route('user.password.update') }}">
                                @csrf
                                @method('PUT')

                                <div class="mb-3">
                                    <label for="current_password" class="form-label">当前密码</label>
                                    <input type="password" class="form-control @error('current_password') is-invalid @enderror" id="current_password" name="current_password" required>
                                    @error('current_password')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="password" class="form-label">新密码</label>
                                    <input type="password" class="form-control @error('password') is-invalid @enderror" id="password" name="password" required>
                                    <div class="form-text">密码长度至少为8个字符</div>
                                    @error('password')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="password_confirmation" class="form-label">确认新密码</label>
                                    <input type="password" class="form-control" id="password_confirmation" name="password_confirmation" required>
                                </div>

                                <button type="submit" class="btn btn-primary">更新密码</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <div class="account-link-card">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5>
                            <i class="fab fa-weixin me-2"></i>微信账号绑定
                        </h5>
                        <p class="mb-0 text-muted">
                            @if(!empty($user->wechat_id))
                                <i class="fas fa-check-circle text-success me-1"></i>
                                已绑定微信账号: {{ $user->wechat_account ?: '未知昵称' }}
                            @else
                                <i class="fas fa-exclamation-circle text-warning me-1"></i>
                                尚未绑定微信账号，绑定后可享受更多便利功能
                            @endif
                        </p>
                    </div>
                    <a href="{{ route('wechat.bind') }}" class="btn {{ !empty($user->wechat_id) ? 'btn-outline-primary' : 'btn-primary' }}">
                        <i class="fab fa-weixin me-2"></i>
                        {{ !empty($user->wechat_id) ? '管理绑定' : '立即绑定' }}
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 移动端视图 - 小程序风格 -->
<div class="mobile-view">
    <!-- 头部和头像 -->
    @include('user.partials.mobile_header')

    <!-- 功能菜单项 -->
    @include('user.partials.mobile_menu')

    <!-- 移动端个人资料表单 -->
    <div class="container mt-4">
        @if(session('success'))
            <div class="alert alert-success">
                {{ session('success') }}
            </div>
        @endif

        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">更新个人资料</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ route('user.profile.update') }}" enctype="multipart/form-data">
                    @csrf
                    @method('PUT')

                    <div class="mb-4 text-center">
                        <label for="avatar_mobile" class="btn btn-sm btn-outline-secondary">选择新头像</label>
                        <input type="file" class="form-control d-none @error('avatar') is-invalid @enderror" id="avatar_mobile" name="avatar" accept="image/*">
                        @error('avatar')
                            <div class="invalid-feedback d-block text-center">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="name_mobile" class="form-label">姓名 (昵称)</label>
                        <input type="text" class="form-control @error('name') is-invalid @enderror" id="name_mobile" name="name" value="{{ old('name', Auth::user()->name) }}">
                        @error('name')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="username_mobile" class="form-label">登录用户名 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control @error('username') is-invalid @enderror" id="username_mobile" name="username" value="{{ old('username', Auth::user()->username) }}" required>
                        <small class="form-text text-muted">用于登录，只能包含字母、数字、下划线和破折号。</small>
                        @error('username')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="email_mobile" class="form-label">电子邮箱</label>
                        <input type="email" class="form-control @error('email') is-invalid @enderror" id="email_mobile" name="email" value="{{ old('email', Auth::user()->email) }}">
                        @error('email')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="phone_mobile" class="form-label">手机号码</label>
                        <input type="text" class="form-control @error('phone') is-invalid @enderror" id="phone_mobile" name="phone" value="{{ old('phone', Auth::user()->phone) }}">
                        @error('phone')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <button type="submit" class="btn btn-primary w-100">更新资料</button>
                </form>
            </div>
        </div>

        <!-- 移动端修改密码表单 -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">修改密码</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ route('user.password.update') }}">
                    @csrf
                    @method('PUT')

                    <div class="mb-3">
                        <label for="current_password_mobile" class="form-label">当前密码</label>
                        <input type="password" class="form-control @error('current_password') is-invalid @enderror" id="current_password_mobile" name="current_password" required>
                        @error('current_password')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="password_mobile" class="form-label">新密码</label>
                        <input type="password" class="form-control @error('password') is-invalid @enderror" id="password_mobile" name="password" required>
                        <div class="form-text">密码长度至少为8个字符</div>
                        @error('password')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="password_confirmation_mobile" class="form-label">确认新密码</label>
                        <input type="password" class="form-control" id="password_confirmation_mobile" name="password_confirmation" required>
                    </div>

                    <button type="submit" class="btn btn-primary w-100">更新密码</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection