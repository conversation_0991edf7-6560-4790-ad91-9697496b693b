@extends('admin.layouts.app')

@section('title', '商品管理')

@section('header', '商品管理')

@section('content')
<div class="card">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col">
                <h5 class="mb-0">商品列表</h5>
            </div>
            <div class="col text-end">
                <div class="btn-group">
                    <a href="{{ route('admin.products.create') }}" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus me-1"></i>添加商品
                    </a>
                    <button type="button" class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#importModal">
                        <i class="fas fa-file-import me-1"></i>批量导入
                    </button>
                    <button type="button" id="exportButton" class="btn btn-info btn-sm">
                        <i class="fas fa-file-export me-1"></i>批量导出
                    </button>
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-toggle="modal" data-bs-target="#batchImageUploadModal">
                        <i class="fas fa-images me-1"></i>批量上传图片
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="mb-3">
            <form action="{{ route('admin.products.index') }}" method="GET" class="row g-3">
                <div class="col-md-2">
                    <input type="text" name="search" class="form-control form-control-sm" placeholder="搜索商品名称" value="{{ request('search') }}">
                </div>
                <div class="col-md-2">
                    <select name="category" class="form-select form-select-sm">
                        <option value="">所有分类</option>
                        @foreach($categories ?? [] as $category)
                            <option value="{{ $category->id }}" {{ request('category') == $category->id ? 'selected' : '' }}>
                                {{ $category->name }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-2">
                    <select name="status" class="form-select form-select-sm">
                        <option value="">所有状态</option>
                        <option value="1" {{ request('status') == '1' ? 'selected' : '' }}>上架</option>
                        <option value="0" {{ request('status') == '0' ? 'selected' : '' }}>下架</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select name="is_points_product" class="form-select form-select-sm">
                        <option value="">积分商品</option>
                        <option value="1" {{ request('is_points_product') == '1' ? 'selected' : '' }}>是</option>
                        <option value="0" {{ request('is_points_product') == '0' ? 'selected' : '' }}>否</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <div class="d-flex">
                        <button type="submit" class="btn btn-sm btn-secondary me-2">筛选</button>
                        <a href="{{ route('admin.products.index') }}" class="btn btn-sm btn-outline-secondary">重置</a>
                    </div>
                </div>
            </form>
        </div>

        <div class="table-responsive">
            <table class="table table-hover table-striped table-bordered">
                <thead class="table-light">
                    <tr>
                        <th width="40">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="selectAll">
                            </div>
                        </th>
                        <th width="60">ID</th>
                        <th width="80">图片</th>
                        <th>商品名称</th>
                        <th>分类</th>
                        <th width="80">价格</th>
                        <th width="80">库存</th>
                        <th width="80">兑换量</th>
                        <th width="100">状态</th>
                        <th width="120">积分信息</th>
                        <th width="150">操作</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($products ?? [] as $product)
                    <tr>
                        <td>
                            <div class="form-check">
                                <input class="form-check-input product-checkbox" type="checkbox" value="{{ $product->id }}">
                            </div>
                        </td>
                        <td>{{ $product->id }}</td>
                        <td>
                            <img src="{{ product_image_url($product->cover_image) }}" alt="{{ $product->name }}" class="img-thumbnail" style="max-width: 50px; max-height: 50px;">
                        </td>
                        <td>{{ $product->name }}</td>
                        <td>{{ $product->category->name ?? '未分类' }}</td>
                        <td>¥{{ $product->price }}</td>
                        <td>{{ $product->stock }}</td>
                        <td>{{ $product->sales_count }}</td>
                        <td>
                            <span class="badge {{ $product->is_on_sale ? 'bg-success' : 'bg-secondary' }}">
                                {{ $product->is_on_sale ? '上架' : '下架' }}
                            </span>
                        </td>
                        <td>
                            @if($product->is_points_product)
                                <span class="badge bg-warning">积分商品</span>
                                <div class="small mt-1">{{ $product->pointsRequired ?? '未设置' }} 分</div>
                            @else
                                <span class="badge bg-light text-dark">普通商品</span>
                            @endif
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ route('admin.products.show', $product->id) }}" class="btn btn-info">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ route('admin.products.edit', $product->id) }}" class="btn btn-primary">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button type="button" class="btn btn-warning"
                                        data-bs-toggle="modal"
                                        data-bs-target="#pointsModal"
                                        data-product-id="{{ $product->id }}"
                                        data-product-name="{{ $product->name }}"
                                        data-points-required="{{ $product->pointsRequired }}"
                                        data-is-points-product="{{ $product->is_points_product ? '1' : '0' }}">
                                    <i class="fas fa-coins"></i>
                                </button>
                                <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal{{ $product->id }}">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>

                            <!-- 删除确认弹窗 -->
                            <div class="modal fade" id="deleteModal{{ $product->id }}" tabindex="-1" aria-hidden="true">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title">确认删除</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            <p>您确定要删除商品 "{{ $product->name }}" 吗？此操作不可逆。</p>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                            <form action="{{ route('admin.products.destroy', $product->id) }}" method="POST">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-danger">确认删除</button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="11" class="text-center py-4">暂无商品数据</td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <div class="d-flex justify-content-end mt-3">
            {{ $products->links() ?? '' }}
        </div>

        <!-- 批量操作表单 -->
        <div class="card mt-4">
            <div class="card-header bg-light">
                <h5 class="mb-0">批量操作</h5>
            </div>
            <div class="card-body">
                <form id="batchActionForm" action="{{ route('admin.products.batch') }}" method="POST">
                    @csrf
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="action" class="form-label">选择操作</label>
                                <select class="form-select" id="action" name="action" required>
                                    <option value="">请选择操作</option>
                                    <option value="on_sale">批量上架</option>
                                    <option value="off_sale">批量下架</option>
                                    <option value="set_points">批量设为积分商品</option>
                                    <option value="unset_points">批量取消积分商品</option>
                                    <option value="delete">批量删除</option>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-3" id="pointsRequiredGroup" style="display: none;">
                            <div class="mb-3">
                                <label for="points_required" class="form-label">所需积分</label>
                                <input type="number" class="form-control" id="points_required" name="points_required" min="1" step="1">
                            </div>
                        </div>

                        <div class="col-md-5">
                            <div class="mb-3 d-flex align-items-end h-100">
                                <button type="submit" class="btn btn-primary" id="batchSubmitBtn" disabled>
                                    <i class="fas fa-check me-1"></i>执行批量操作
                                </button>
                                <span class="ms-3 text-danger" id="selectedCount">未选择商品</span>
                            </div>
                        </div>
                    </div>

                    <div id="selectedProducts">
                        <!-- 这里会通过JS动态添加被选中的商品ID -->
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 积分设置弹窗 -->
<div class="modal fade" id="pointsModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">积分商品设置</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="pointsForm" action="{{ route('admin.products.update-points') }}" method="POST">
                @csrf
                <input type="hidden" id="productId" name="product_id">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">商品名称</label>
                        <input type="text" class="form-control" id="productName" readonly>
                    </div>
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="isPointsProduct" name="is_points_product" value="1">
                            <label class="form-check-label" for="isPointsProduct">设为积分商品</label>
                        </div>
                    </div>
                    <div class="mb-3" id="pointsRequiredGroup">
                        <label for="pointsRequired" class="form-label">所需积分</label>
                        <input type="number" class="form-control" id="pointsRequired" name="points_required" min="1" step="1">
                        <div class="form-text">设置兑换该商品所需的积分数量</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">保存设置</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 批量导入弹窗 -->
<div class="modal fade" id="importModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">批量导入商品</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ route('admin.products.import') }}" method="POST" enctype="multipart/form-data">
                @csrf
                <div class="modal-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>图片导入功能说明</h6>
                        <p class="mb-2">现在支持在Excel表格中导入商品图片，有两种方式：</p>
                        <ul class="mb-2">
                            <li><strong>网络图片URL：</strong>在"封面图片URL"或"商品图片URLs"列中填写图片的网络地址</li>
                            <li><strong>本地图片文件：</strong>将图片文件放在服务器的 <code>storage/app/import/images/</code> 文件夹中，然后在"封面图片文件名"列中填写文件名</li>
                        </ul>
                        <p class="mb-0 small text-muted">
                            <i class="fas fa-lightbulb me-1"></i>
                            提示：多张商品图片URL用英文逗号分隔，支持jpg、jpeg、png、gif、webp格式
                        </p>
                    </div>

                    <div class="mb-3">
                        <label for="importFile" class="form-label">选择Excel文件</label>
                        <input type="file" class="form-control" id="importFile" name="file" accept=".xlsx, .xls, .csv" required>
                        <div class="form-text">支持 .xlsx, .xls, .csv 格式文件</div>
                    </div>
                    <div class="mb-3">
                        <div class="dropdown">
                            <button class="btn btn-outline-primary btn-sm dropdown-toggle" type="button" id="templateDownloadDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-download me-1"></i>下载导入模板
                            </button>
                            <ul class="dropdown-menu" aria-labelledby="templateDownloadDropdown">
                                <li><a class="dropdown-item" href="{{ route('admin.products.download-template', ['format' => 'xlsx']) }}">Excel模板 (.xlsx)</a></li>
                                <li><a class="dropdown-item" href="{{ route('admin.products.download-template', ['format' => 'xls']) }}">Excel模板 (.xls)</a></li>
                                <li><a class="dropdown-item" href="{{ route('admin.products.download-template', ['format' => 'csv']) }}">CSV模板 (.csv)</a></li>
                            </ul>
                        </div>
                    </div>

                    <div class="mb-3">
                        <button type="button" class="btn btn-outline-info btn-sm" data-bs-toggle="collapse" data-bs-target="#imageImportHelp" aria-expanded="false">
                            <i class="fas fa-question-circle me-1"></i>查看图片导入详细说明
                        </button>
                        <div class="collapse mt-2" id="imageImportHelp">
                            <div class="card card-body">
                                <h6>图片导入步骤：</h6>
                                <ol>
                                    <li>下载最新的导入模板</li>
                                    <li>如果使用本地图片文件：
                                        <ul>
                                            <li>在服务器上创建目录：<code>storage/app/import/images/</code></li>
                                            <li>将图片文件上传到该目录</li>
                                            <li>在Excel的"封面图片文件名"列中填写文件名（如：product1.jpg）</li>
                                        </ul>
                                    </li>
                                    <li>如果使用网络图片：
                                        <ul>
                                            <li>在"封面图片URL"列中填写完整的图片网址</li>
                                            <li>在"商品图片URLs"列中填写多张图片网址，用英文逗号分隔</li>
                                        </ul>
                                    </li>
                                    <li>上传Excel文件进行导入</li>
                                </ol>
                                <p class="mb-0 small text-warning">
                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                    注意：图片文件大小建议不超过2MB，系统会自动下载网络图片并保存到本地
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">开始导入</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 批量上传图片模态框 -->
<div class="modal fade" id="batchUploadModal" tabindex="-1" role="dialog" aria-labelledby="batchUploadModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="batchUploadModalLabel">批量上传商品图片</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fa fa-info-circle"></i> 请上传商品图片，文件名应与商品名称完全一致（不包含扩展名）。支持JPG, PNG, GIF格式，单张图片不超过2MB。
                </div>

                <form id="batchUploadForm" action="{{ route('admin.products.batch-upload-images') }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    <div class="form-group">
                        <label for="images">选择图片文件</label>
                        <input type="file" name="images[]" id="images" class="form-control" multiple accept="image/jpeg,image/png,image/gif">
                        <small class="form-text text-muted">可以一次选择多个图片文件</small>
                    </div>

                    <div class="progress mt-3 d-none" id="uploadProgress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
                    </div>

                    <div id="uploadResult" class="mt-3"></div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" id="startUploadBtn">开始上传</button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const productCheckboxes = document.querySelectorAll('.product-checkbox');
    const selectedCountSpan = document.getElementById('selectedCount');
    const selectedProductsDiv = document.getElementById('selectedProducts');
    const batchActionSelect = document.getElementById('action');
    const batchSubmitBtn = document.getElementById('batchSubmitBtn');
    const pointsRequiredGroup = document.getElementById('pointsRequiredGroup'); // Batch action points group
    const pointsRequiredInput = document.getElementById('points_required'); // Batch action points input
    const batchActionForm = document.getElementById('batchActionForm');

    // --- Modal related elements ---
    const pointsModalElement = document.getElementById('pointsModal');
    const pointsModal = pointsModalElement ? new bootstrap.Modal(pointsModalElement) : null; // Get modal instance if exists
    const pointsModalProductIdInput = document.getElementById('productId');
    const pointsModalProductNameInput = document.getElementById('productName');
    const pointsModalIsPointsCheckbox = document.getElementById('isPointsProduct');
    const pointsModalPointsRequiredInput = document.getElementById('pointsRequired'); // Modal points input
    const pointsModalPointsRequiredGroup = document.querySelector('#pointsModal #pointsRequiredGroup'); // Modal points group


    // --- Functions ---

    // 更新提交按钮状态
    function updateSubmitButtonState() {
        const selectedCheckboxes = document.querySelectorAll('.product-checkbox:checked');
        const actionSelected = batchActionSelect.value !== '';
        batchSubmitBtn.disabled = !(selectedCheckboxes.length > 0 && actionSelected);
    }

    // 更新已选商品数量和隐藏字段
    function updateSelectedProducts() {
        const selectedProducts = [];
        const selectedCheckboxes = document.querySelectorAll('.product-checkbox:checked');

        selectedCheckboxes.forEach(checkbox => {
            selectedProducts.push(checkbox.value);
        });

        // 更新选中数量显示
        if (selectedCheckboxes.length > 0) {
            selectedCountSpan.textContent = '已选择 ' + selectedCheckboxes.length + ' 件商品';
        } else {
            selectedCountSpan.textContent = '未选择商品';
        }

        // 清空并重新添加所有选中的商品ID作为隐藏字段
        selectedProductsDiv.innerHTML = ''; // Clear previous hidden inputs
        selectedProducts.forEach(productId => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'products[]';
            input.value = productId;
            selectedProductsDiv.appendChild(input);
        });

        // 更新提交按钮状态
        updateSubmitButtonState();
    }

    // 切换积分输入框（批量操作）的显示和必填状态
    function toggleBatchPointsRequired(show) {
        if (pointsRequiredGroup) {
             pointsRequiredGroup.style.display = show ? 'block' : 'none';
        }
       if (pointsRequiredInput) {
            if (show) {
                pointsRequiredInput.setAttribute('required', '');
            } else {
                pointsRequiredInput.removeAttribute('required');
            }
       }
    }

    // 切换积分输入框（Modal）的显示和必填状态
    function toggleModalPointsRequired(show) {
         if (pointsModalPointsRequiredGroup) {
             pointsModalPointsRequiredGroup.style.display = show ? 'block' : 'none';
         }
         if (pointsModalPointsRequiredInput) {
            if (show) {
                 pointsModalPointsRequiredInput.setAttribute('required', '');
            } else {
                pointsModalPointsRequiredInput.removeAttribute('required');
            }
         }
    }


    // --- Event Listeners ---

    // 全选/取消全选
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('click', function() {
            productCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateSelectedProducts();
        });
    }

    // 单个复选框点击
    productCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('click', function() {
            // 如果所有单个都选中了，则勾选全选框；否则取消全选框
            if (selectAllCheckbox) {
                 const allChecked = document.querySelectorAll('.product-checkbox:checked').length === productCheckboxes.length;
                 selectAllCheckbox.checked = allChecked;
            }
            updateSelectedProducts();
        });
    });

    // 批量操作类型切换
    if (batchActionSelect) {
        batchActionSelect.addEventListener('change', function() {
            const action = this.value;
            toggleBatchPointsRequired(action === 'set_points');
            updateSubmitButtonState(); // Update button state when action changes
        });
         // Initial check in case the page loads with an action pre-selected
         toggleBatchPointsRequired(batchActionSelect.value === 'set_points');
    }


    // 积分商品设置弹窗 (Using Bootstrap 5 vanilla JS API)
    if (pointsModalElement) {
        pointsModalElement.addEventListener('show.bs.modal', function (event) {
            const button = event.relatedTarget; // Button that triggered the modal
            if (!button) return; // Exit if no related target

            const productId = button.dataset.productId;
            const productName = button.dataset.productName;
            const pointsRequired = button.dataset.pointsRequired;
            const isPointsProduct = button.dataset.isPointsProduct === '1';

            if (pointsModalProductIdInput) pointsModalProductIdInput.value = productId || '';
            if (pointsModalProductNameInput) pointsModalProductNameInput.value = productName || '';
            if (pointsModalIsPointsCheckbox) pointsModalIsPointsCheckbox.checked = isPointsProduct;
            if (pointsModalPointsRequiredInput) pointsModalPointsRequiredInput.value = pointsRequired || '';

            // 根据是否是积分商品显示/隐藏积分设置
            toggleModalPointsRequired(isPointsProduct);
        });
    }

    // 切换积分商品状态时（Modal内）显示/隐藏积分设置
    if (pointsModalIsPointsCheckbox) {
        pointsModalIsPointsCheckbox.addEventListener('change', function() {
            toggleModalPointsRequired(this.checked);
        });
    }

    // 批量操作表单提交确认
    if (batchActionForm) {
        batchActionForm.addEventListener('submit', function(e) {
            const action = batchActionSelect.value;
            const selectedCount = document.querySelectorAll('.product-checkbox:checked').length;

            if (action === 'delete' && selectedCount > 0) {
                if (!confirm('确认要删除选中的 ' + selectedCount + ' 件商品吗？此操作不可恢复！')) {
                    e.preventDefault(); // Prevent form submission
                }
            }
             // Add confirmation for other potentially destructive actions if needed
        });
    }

    // Initial state setup
    updateSelectedProducts(); // Initialize count and button state on page load

    // 批量图片上传相关代码
    const dropZone = document.getElementById('dropZone');
    const fileInput = document.getElementById('imageFiles');
    const previewContainer = document.getElementById('previewContainer');
    const uploadForm = document.getElementById('batchImageUploadForm');
    const uploadProgress = document.getElementById('uploadProgress');
    const uploadProgressBar = document.getElementById('uploadProgressBar');

    if (dropZone && fileInput) {
        // 拖放区域事件处理
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, preventDefaults, false);
        });

        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        ['dragenter', 'dragover'].forEach(eventName => {
            dropZone.addEventListener(eventName, highlight, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, unhighlight, false);
        });

        function highlight() {
            dropZone.classList.add('border-primary', 'bg-light');
        }

        function unhighlight() {
            dropZone.classList.remove('border-primary', 'bg-light');
        }

        // 处理文件拖放
        dropZone.addEventListener('drop', handleDrop, false);

        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;
            fileInput.files = files;
            updateFilePreview();
        }

        // 监听文件选择变化
        fileInput.addEventListener('change', updateFilePreview);

        function updateFilePreview() {
            previewContainer.innerHTML = '';
            if (fileInput.files.length > 0) {
                for (let i = 0; i < fileInput.files.length; i++) {
                    const file = fileInput.files[i];
                    if (file.type.match('image.*')) {
                        const reader = new FileReader();

                        reader.onload = function(e) {
                            const preview = document.createElement('div');
                            preview.className = 'col-md-3 col-sm-4 col-6 mb-3';
                            preview.innerHTML = `
                                <div class="card">
                                    <img src="${e.target.result}" class="card-img-top" style="height: 120px; object-fit: cover;">
                                    <div class="card-body p-2">
                                        <p class="card-text small text-truncate" title="${file.name}">${file.name}</p>
                                        <p class="card-text small text-muted">${formatFileSize(file.size)}</p>
                                    </div>
                                </div>
                            `;
                            previewContainer.appendChild(preview);
                        };

                        reader.readAsDataURL(file);
                    }
                }
            } else {
                previewContainer.innerHTML = '<div class="col-12 text-center text-muted">没有选择图片</div>';
            }
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 表单提交处理
        if (uploadForm) {
            uploadForm.addEventListener('submit', function(e) {
                e.preventDefault();

                if (fileInput.files.length === 0) {
                    alert('请先选择要上传的图片');
                    return;
                }

                const formData = new FormData(this);

                // 显示进度条
                uploadProgress.style.display = 'block';
                uploadProgressBar.style.width = '0%';
                uploadProgressBar.textContent = '0%';

                const xhr = new XMLHttpRequest();

                xhr.open('POST', this.action, true);

                xhr.upload.onprogress = function(e) {
                    if (e.lengthComputable) {
                        const percentComplete = Math.round((e.loaded / e.total) * 100);
                        uploadProgressBar.style.width = percentComplete + '%';
                        uploadProgressBar.textContent = percentComplete + '%';
                    }
                };

                xhr.onload = function() {
                    if (xhr.status === 200) {
                        const response = JSON.parse(xhr.responseText);

                        if (response.success) {
                            alert('图片上传成功！');
                            // 隐藏模态框
                            const modal = bootstrap.Modal.getInstance(document.getElementById('batchImageUploadModal'));
                            modal.hide();
                            // 重新加载页面
                            window.location.reload();
                        } else {
                            alert('上传失败：' + response.message);
                        }
                    } else {
                        alert('上传失败，请稍后重试');
                    }

                    // 隐藏进度条
                    uploadProgress.style.display = 'none';
                };

                xhr.onerror = function() {
                    alert('上传出错，请检查网络连接');
                    uploadProgress.style.display = 'none';
                };

                xhr.send(formData);
            });
        }
    }

    // 导出按钮点击处理
    const exportButton = document.getElementById('exportButton');
    if (exportButton) {
        exportButton.addEventListener('click', function() {
            // 显示加载中消息
            Swal.fire({
                title: '处理中...',
                html: '正在准备导出数据，请稍候',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // 使用window.location.href直接访问导出路由，而不是使用fetch API
            // 这样可以保持用户的身份验证状态，避免401错误
            window.location.href = '{{ route("admin.products.export") }}';

            // 设置一个延时，3秒后关闭加载提示
            setTimeout(() => {
                Swal.fire({
                    icon: 'success',
                    title: '导出已开始',
                    text: '如果您的文件没有自动下载，请检查浏览器设置或点击确定重试',
                    showCancelButton: true,
                    confirmButtonText: '重试',
                    cancelButtonText: '取消'
                }).then((result) => {
                    if (result.isConfirmed) {
                        window.location.href = '{{ route("admin.products.export") }}';
                    }
                });
            }, 3000);
        });
    } else {
        console.error('导出按钮不存在');
    }
});
</script>
@endsection

<!-- 批量图片上传模态框 -->
<div class="modal fade" id="batchImageUploadModal" tabindex="-1" aria-labelledby="batchImageUploadModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="batchImageUploadModalLabel">批量上传商品图片</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="batchImageUploadForm" action="{{ route('admin.products.batch-upload-images') }}" method="POST" enctype="multipart/form-data">
                @csrf
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>提示：</strong>上传的图片文件名将用于匹配对应的商品名称。每个商品名称都是唯一的，系统将自动匹配并更新。
                    </div>

                    <div id="dropZone" class="border border-2 border-dashed rounded p-4 text-center mb-3" style="min-height: 200px;">
                        <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                        <h5>拖放图片到这里上传</h5>
                        <p class="text-muted">或者</p>
                        <div class="mb-3">
                            <input type="file" id="imageFiles" name="images[]" class="form-control" multiple accept="image/*">
                        </div>
                        <p class="small text-muted mt-2">支持 JPG, PNG, GIF 格式的图片，单张图片大小不超过 2MB</p>
                    </div>

                    <div class="progress mb-3" id="uploadProgress" style="display: none;">
                        <div id="uploadProgressBar" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                    </div>

                    <h5 class="mb-3">图片预览</h5>
                    <div id="previewContainer" class="row">
                        <div class="col-12 text-center text-muted">没有选择图片</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">开始上传</button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
    $(document).ready(function() {
        // 批量上传图片处理
        $('#startUploadBtn').on('click', function() {
            var formData = new FormData($('#batchUploadForm')[0]);
            var files = $('#images')[0].files;

            // 验证是否选择了文件
            if (files.length === 0) {
                Swal.fire({
                    icon: 'error',
                    title: '请选择文件',
                    text: '请先选择要上传的图片文件'
                });
                return;
            }

            // 显示进度条
            $('#uploadProgress').removeClass('d-none');

            // 禁用上传按钮
            $('#startUploadBtn').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> 上传中...');

            // 使用AJAX上传文件
            $.ajax({
                url: '{{ route("admin.products.batch-upload-images") }}',
                type: 'POST',
                data: formData,
                contentType: false,
                processData: false,
                xhr: function() {
                    var xhr = new window.XMLHttpRequest();

                    // 上传进度
                    xhr.upload.addEventListener("progress", function(evt) {
                        if (evt.lengthComputable) {
                            var percentComplete = evt.loaded / evt.total * 100;
                            $('#uploadProgress .progress-bar').css('width', percentComplete + '%').attr('aria-valuenow', percentComplete);
                        }
                    }, false);

                    return xhr;
                },
                success: function(response) {
                    // 完成进度条
                    $('#uploadProgress .progress-bar').css('width', '100%').attr('aria-valuenow', 100);

                    // 显示成功消息
                    Swal.fire({
                        icon: 'success',
                        title: '上传成功',
                        text: response.message
                    });

                    // 显示上传结果
                    var resultHtml = '<div class="alert alert-success"><h6>上传结果:</h6>';
                    resultHtml += '<p>' + response.message + '</p>';

                    if (response.results && response.results.length > 0) {
                        resultHtml += '<ul>';
                        response.results.forEach(function(result) {
                            if (result.status === 'success') {
                                resultHtml += '<li class="text-success">' + result.filename + ' → ' + result.product + ' (成功)</li>';
                            } else {
                                resultHtml += '<li class="text-danger">' + result.filename + ' (失败: ' + result.reason + ')</li>';
                            }
                        });
                        resultHtml += '</ul>';
                    }

                    resultHtml += '</div>';

                    $('#uploadResult').html(resultHtml);

                    // 重置按钮状态
                    $('#startUploadBtn').prop('disabled', false).html('开始上传');

                    // 如果全部成功，延迟后刷新页面
                    if (response.success && response.results.every(r => r.status === 'success')) {
                        setTimeout(function() {
                            location.reload();
                        }, 3000);
                    }
                },
                error: function(xhr, status, error) {
                    // 显示错误消息
                    var errorMessage = '上传失败';

                    try {
                        var response = JSON.parse(xhr.responseText);
                        if (response.message) {
                            errorMessage = response.message;
                        }
                    } catch (e) {
                        errorMessage = '上传失败: ' + error;
                    }

                    Swal.fire({
                        icon: 'error',
                        title: '上传失败',
                        text: errorMessage
                    });

                    // 重置进度条
                    $('#uploadProgress').addClass('d-none');
                    $('#uploadProgress .progress-bar').css('width', '0%').attr('aria-valuenow', 0);

                    // 重置按钮状态
                    $('#startUploadBtn').prop('disabled', false).html('开始上传');

                    // 显示错误信息
                    $('#uploadResult').html('<div class="alert alert-danger">上传失败: ' + errorMessage + '</div>');
                }
            });
        });
    });
</script>
@endpush