@extends('admin.layouts.app')

@section('title', '数据分析中心')

@section('styles')
<style>
    .chart-container {
        height: 400px;
        width: 100%;
        margin-bottom: 20px;
    }
    .summary-card {
        transition: transform 0.3s ease;
        border: none;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .summary-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 20px rgba(0,0,0,0.15);
    }
    .card-header {
        background: transparent;
        border-bottom: 1px solid #e3e6f0;
        font-weight: 600;
    }
    .btn-date-range {
        border-radius: 20px;
        padding: 8px 16px;
        margin: 0 5px;
        transition: all 0.3s;
    }
    .btn-date-range.active {
        background: #4e73df;
        color: white;
        border-color: #4e73df;
    }
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255,255,255,0.8);
        display: none;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    }
    .loading-spinner {
        width: 50px;
        height: 50px;
        border: 5px solid #f3f3f3;
        border-top: 5px solid #4e73df;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    .no-data {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 300px;
        color: #6c757d;
        font-size: 16px;
    }
</style>
@endsection

@section('content')
<div class="container-fluid">
    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner"></div>
    </div>

    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">数据分析中心</h1>
        <button class="btn btn-primary btn-sm" id="refreshBtn">
            <i class="fas fa-sync-alt"></i> 刷新数据
        </button>
    </div>

    <!-- Date Range Selector -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-body">
                    <div class="d-flex align-items-center flex-wrap">
                        <div class="mr-3 mb-2">
                            <label class="form-label mb-1">日期范围：</label>
                            <input type="date" class="form-control d-inline-block" id="startDate" style="width: 150px;">
                            <span class="mx-2">至</span>
                            <input type="date" class="form-control d-inline-block" id="endDate" style="width: 150px;">
                        </div>
                        <div class="mb-2">
                            <button type="button" class="btn btn-outline-primary btn-sm btn-date-range" data-days="7">最近7天</button>
                            <button type="button" class="btn btn-outline-primary btn-sm btn-date-range active" data-days="30">最近30天</button>
                            <button type="button" class="btn btn-outline-primary btn-sm btn-date-range" data-days="90">最近90天</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card summary-card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">新增用户</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="summary-new-users">0</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-plus fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card summary-card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">兑换积分</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="summary-sales">0</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-coins fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card summary-card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">积分发放</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="summary-points-issued">0</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-gift fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card summary-card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">积分使用</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="summary-points-used">0</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row 1 -->
    <div class="row">
        <!-- User Growth Chart -->
        <div class="col-xl-6 col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">用户增长趋势</h6>
                </div>
                <div class="card-body">
                    <div class="chart-container" id="userGrowthChart"></div>
                </div>
            </div>
        </div>

        <!-- Active Users Chart -->
        <div class="col-xl-6 col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">活跃用户趋势</h6>
                </div>
                <div class="card-body">
                    <div class="chart-container" id="activeUsersChart"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row 2 -->
    <div class="row">
        <!-- Sales Trend Chart -->
        <div class="col-xl-6 col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">积分兑换趋势</h6>
                </div>
                <div class="card-body">
                    <div class="chart-container" id="salesTrendChart"></div>
                </div>
            </div>
        </div>

        <!-- Points Trend Chart -->
        <div class="col-xl-6 col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">积分收支趋势</h6>
                </div>
                <div class="card-body">
                    <div class="chart-container" id="pointsTrendChart"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row 3: Distribution Charts -->
    <div class="row">
        <!-- Payment Methods Distribution -->
        <div class="col-xl-6 col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-danger">兑换状态分布</h6>
                </div>
                <div class="card-body">
                    <div class="chart-container" id="paymentMethodsChart"></div>
                </div>
            </div>
        </div>

        <!-- Points Category Distribution -->
        <div class="col-xl-6 col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-secondary">商品分类兑换分布</h6>
                </div>
                <div class="card-body">
                    <div class="chart-container" id="categoryDistributionChart"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tables Row -->
    <div class="row">
        <!-- Hot Selling Products Table -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">热门兑换商品 (Top 10)</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped" id="hotProductsTable">
                            <thead>
                                <tr>
                                    <th>排名</th>
                                    <th>商品名称</th>
                                    <th>兑换数量</th>
                                    <th>消耗积分</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- 动态填充 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Points Product Ranking Table -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">积分活跃用户 (Top 10)</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped" id="productRankingTable">
                            <thead>
                                <tr>
                                    <th>排名</th>
                                    <th>用户名称</th>
                                    <th>活动次数</th>
                                    <th>积分总量</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- 动态填充 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/moment@2.29.4/moment.min.js"></script>

<script>
$(document).ready(function() {
    // 全局变量
    let charts = {};
    const dataUrl = '{{ route("admin.analytics.data") }}';

    // 设置默认日期范围（最近30天）
    const endDate = moment().format('YYYY-MM-DD');
    const startDate = moment().subtract(29, 'days').format('YYYY-MM-DD');

    $('#startDate').val(startDate);
    $('#endDate').val(endDate);

    // 初始化图表
    function initCharts() {
        // 用户增长趋势图
        charts.userGrowth = echarts.init(document.getElementById('userGrowthChart'));

        // 活跃用户趋势图
        charts.activeUsers = echarts.init(document.getElementById('activeUsersChart'));

        // 积分兑换趋势图
        charts.salesTrend = echarts.init(document.getElementById('salesTrendChart'));

        // 积分收支趋势图
        charts.pointsTrend = echarts.init(document.getElementById('pointsTrendChart'));

        // 兑换状态分布图
        charts.paymentMethods = echarts.init(document.getElementById('paymentMethodsChart'));

        // 商品分类兑换分布图
        charts.categoryDistribution = echarts.init(document.getElementById('categoryDistributionChart'));

        // 响应式调整
        window.addEventListener('resize', function() {
            Object.values(charts).forEach(chart => chart.resize());
        });
    }

    // 显示加载状态
    function showLoading() {
        $('#loadingOverlay').show();
    }

    // 隐藏加载状态
    function hideLoading() {
        $('#loadingOverlay').hide();
    }

    // 获取分析数据
    function fetchAnalyticsData(start, end) {
        showLoading();

        $.ajax({
            url: dataUrl,
            method: 'GET',
            data: {
                start_date: start,
                end_date: end
            },
            success: function(response) {
                console.log('数据获取成功:', response);
                updateSummaryCards(response);
                updateCharts(response);
                updateTables(response);
                hideLoading();
            },
            error: function(xhr, status, error) {
                console.error('数据获取失败:', error);
                hideLoading();
                alert('数据获取失败，请稍后重试');
            }
        });
    }

    // 更新统计卡片
    function updateSummaryCards(data) {
        // 计算总数
        const newUsersTotal = data.userAnalytics.newUsers.reduce((sum, item) => sum + item.count, 0);
        const salesTotal = data.salesAnalytics.sales.reduce((sum, item) => sum + item.amount, 0);
        const pointsIssuedTotal = data.pointsAnalytics.issued.reduce((sum, item) => sum + item.points, 0);
        const pointsUsedTotal = data.pointsAnalytics.consumed.reduce((sum, item) => sum + item.points, 0);

        $('#summary-new-users').text(newUsersTotal.toLocaleString());
        $('#summary-sales').text(salesTotal.toLocaleString());
        $('#summary-points-issued').text(pointsIssuedTotal.toLocaleString());
        $('#summary-points-used').text(pointsUsedTotal.toLocaleString());
    }

    // 更新图表
    function updateCharts(data) {
        // 用户增长趋势图
        const userGrowthOption = {
            title: { text: '用户增长趋势', left: 'center' },
            tooltip: { trigger: 'axis' },
            xAxis: {
                type: 'category',
                data: data.userAnalytics.newUsers.map(item => item.date)
            },
            yAxis: { type: 'value' },
            series: [{
                name: '新增用户',
                type: 'line',
                data: data.userAnalytics.newUsers.map(item => item.count),
                smooth: true,
                itemStyle: { color: '#4e73df' }
            }]
        };
        charts.userGrowth.setOption(userGrowthOption);

        // 活跃用户趋势图
        const activeUsersOption = {
            title: { text: '活跃用户趋势', left: 'center' },
            tooltip: { trigger: 'axis' },
            xAxis: {
                type: 'category',
                data: data.userAnalytics.activeUsers.map(item => item.date)
            },
            yAxis: { type: 'value' },
            series: [{
                name: '活跃用户',
                type: 'line',
                data: data.userAnalytics.activeUsers.map(item => item.count),
                smooth: true,
                itemStyle: { color: '#1cc88a' }
            }]
        };
        charts.activeUsers.setOption(activeUsersOption);

        // 积分兑换趋势图
        const salesTrendOption = {
            title: { text: '积分兑换趋势', left: 'center' },
            tooltip: { trigger: 'axis' },
            xAxis: {
                type: 'category',
                data: data.salesAnalytics.sales.map(item => item.date)
            },
            yAxis: { type: 'value' },
            series: [{
                name: '兑换积分',
                type: 'bar',
                data: data.salesAnalytics.sales.map(item => item.amount),
                itemStyle: { color: '#36b9cc' }
            }]
        };
        charts.salesTrend.setOption(salesTrendOption);

        // 积分收支趋势图
        const pointsTrendOption = {
            title: { text: '积分收支趋势', left: 'center' },
            tooltip: { trigger: 'axis' },
            legend: { data: ['积分发放', '积分使用'], top: 30 },
            xAxis: {
                type: 'category',
                data: data.pointsAnalytics.issued.map(item => item.date)
            },
            yAxis: { type: 'value' },
            series: [{
                name: '积分发放',
                type: 'line',
                data: data.pointsAnalytics.issued.map(item => item.points),
                smooth: true,
                itemStyle: { color: '#f6c23e' }
            }, {
                name: '积分使用',
                type: 'line',
                data: data.pointsAnalytics.consumed.map(item => item.points),
                smooth: true,
                itemStyle: { color: '#e74a3b' }
            }]
        };
        charts.pointsTrend.setOption(pointsTrendOption);

        // 兑换状态分布图
        if (data.salesAnalytics.paymentMethods.length > 0) {
            const paymentMethodsOption = {
                title: { text: '兑换状态分布', left: 'center' },
                tooltip: { trigger: 'item' },
                series: [{
                    name: '兑换状态',
                    type: 'pie',
                    radius: '50%',
                    data: data.salesAnalytics.paymentMethods.map(item => ({
                        value: item.count,
                        name: item.payment_method_label
                    })),
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    }
                }]
            };
            charts.paymentMethods.setOption(paymentMethodsOption);
        }

        // 商品分类兑换分布图
        if (data.pointsAnalytics.categoryDistribution.length > 0) {
            const categoryDistributionOption = {
                title: { text: '商品分类兑换分布', left: 'center' },
                tooltip: { trigger: 'item' },
                series: [{
                    name: '分类兑换',
                    type: 'pie',
                    radius: '50%',
                    data: data.pointsAnalytics.categoryDistribution.map(item => ({
                        value: item.exchange_count,
                        name: item.category_name
                    })),
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    }
                }]
            };
            charts.categoryDistribution.setOption(categoryDistributionOption);
        }
    }

    // 更新表格
    function updateTables(data) {
        // 更新热门兑换商品表格
        const hotProductsTableBody = $('#hotProductsTable tbody');
        hotProductsTableBody.empty();

        if (data.salesAnalytics.hotProducts.length > 0) {
            data.salesAnalytics.hotProducts.forEach((product, index) => {
                hotProductsTableBody.append(`
                    <tr>
                        <td>${index + 1}</td>
                        <td>${product.name}</td>
                        <td>${product.total_quantity}</td>
                        <td>${product.total_amount}</td>
                    </tr>
                `);
            });
        } else {
            hotProductsTableBody.append('<tr><td colspan="4" class="text-center">暂无数据</td></tr>');
        }

        // 更新积分活跃用户表格
        const productRankingTableBody = $('#productRankingTable tbody');
        productRankingTableBody.empty();

        if (data.pointsAnalytics.productRanking.length > 0) {
            data.pointsAnalytics.productRanking.forEach((user, index) => {
                productRankingTableBody.append(`
                    <tr>
                        <td>${index + 1}</td>
                        <td>${user.product_name}</td>
                        <td>${user.total_quantity}</td>
                        <td>${user.total_points}</td>
                    </tr>
                `);
            });
        } else {
            productRankingTableBody.append('<tr><td colspan="4" class="text-center">暂无数据</td></tr>');
        }
    }

    // 事件处理
    // 日期范围快捷按钮
    $('.btn-date-range').click(function() {
        $('.btn-date-range').removeClass('active');
        $(this).addClass('active');

        const days = $(this).data('days');
        const end = moment().format('YYYY-MM-DD');
        const start = moment().subtract(days - 1, 'days').format('YYYY-MM-DD');

        $('#startDate').val(start);
        $('#endDate').val(end);

        fetchAnalyticsData(start, end);
    });

    // 日期输入框变化
    $('#startDate, #endDate').change(function() {
        const start = $('#startDate').val();
        const end = $('#endDate').val();

        if (start && end && start <= end) {
            $('.btn-date-range').removeClass('active');
            fetchAnalyticsData(start, end);
        }
    });

    // 刷新按钮
    $('#refreshBtn').click(function() {
        const start = $('#startDate').val();
        const end = $('#endDate').val();
        fetchAnalyticsData(start, end);
    });

    // 初始化
    initCharts();
    fetchAnalyticsData(startDate, endDate);
});
</script>
@endpush