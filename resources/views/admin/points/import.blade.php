@extends('admin.layouts.app')

@section('title', '导入积分记录')

@section('header', '导入积分记录')

@push('styles')
<style>
    .custom-file-label.selected {
        color: #495057;
        background-color: #e3f2fd;
        border-color: #2196f3;
    }

    .custom-file-label.selected::after {
        background-color: #2196f3;
        border-color: #2196f3;
        color: white;
        content: "已选择";
    }

    .custom-file:hover .custom-file-label {
        border-color: #80bdff;
        background-color: #f8f9fa;
    }

    .border-primary.bg-light {
        border-color: #007bff !important;
        background-color: #e3f2fd !important;
    }

    #import-progress {
        animation: fadeIn 0.3s ease-in;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
    }
</style>
@endpush

@section('content')
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">导入积分记录</h3>
                <div class="card-tools">
                    <a href="{{ route('admin.points.index') }}" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left"></i> 返回列表
                    </a>
                </div>
            </div>
            <div class="card-body">
                @if ($errors->any())
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
                @endif

                @if (session('errorRows'))
                <div class="alert alert-warning">
                    <h5><i class="fas fa-exclamation-triangle"></i> 部分数据导入失败</h5>
                    <p>以下行数据导入失败：</p>
                    <ul>
                        @foreach (session('errorRows') as $errorRow)
                        <li>{{ $errorRow }}</li>
                        @endforeach
                    </ul>
                </div>
                @endif

                <div class="row">
                    <div class="col-md-6">
                        <form action="{{ route('admin.points.import') }}" method="POST" enctype="multipart/form-data" class="mb-4">
                            @csrf

                            <div class="form-group">
                                <label for="file">选择导入文件</label>
                                <div class="custom-file">
                                    <input type="file" name="file" class="custom-file-input" id="file" accept=".csv,.txt,.xlsx,.xls" required>
                                    <label class="custom-file-label" for="file">选择文件</label>
                                </div>
                                <small class="form-text text-muted">支持CSV、Excel格式文件，大小不超过10MB。支持自动编码转换（GBK、GB2312等）</small>
                            </div>

                            <div class="form-group">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-upload"></i> 开始导入
                                </button>
                            </div>
                        </form>
                    </div>

                    <div class="col-md-6">
                        <div class="card h-100">
                            <div class="card-header bg-info text-white">
                                <h5 class="mb-0"><i class="fas fa-info-circle"></i> 导入说明</h5>
                            </div>
                            <div class="card-body">
                                <h6>文件格式要求：</h6>
                                <ol class="pl-3">
                                    <li>支持CSV、Excel(.xlsx/.xls)格式文件</li>
                                    <li>支持多种编码格式（UTF-8、GBK、GB2312等），系统会自动转换</li>
                                    <li>第一行必须是表头：<code>用户标识,积分,类型,描述</code></li>
                                    <li>用户标识可以是**登录用户名**、邮箱、手机号或用户昵称 (按此优先级查找)</li>
                                    <li>积分可正可负，但不能为0</li>
                                    <li>类型可选值：<code>manual</code>(手动调整), <code>activity</code>(活动奖励), <code>exchange</code>(积分兑换), <code>order</code>(订单消费)</li>
                                </ol>

                                <h6 class="mt-3">示例内容：</h6>
                                <pre class="bg-light p-2 border rounded"><code>用户标识,积分,类型,描述
login_user1,150,manual,后台导入积分
<EMAIL>,100,manual,批量导入奖励
13800138000,-50,exchange,兑换商品
nickname_user,200,activity,活动奖励</code></pre>

                                <div class="mt-2">
                                    <a href="{{ route('admin.points.download.template') }}?format=csv" class="btn btn-outline-secondary btn-sm">
                                        <i class="fas fa-file-csv"></i> 下载CSV模板
                                    </a>
                                    <a href="{{ route('admin.points.download.template') }}?format=excel" class="btn btn-outline-success btn-sm ml-2">
                                        <i class="fas fa-file-excel"></i> 下载Excel模板
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // 处理文件选择后显示文件名
    $('#file').on('change', function(e) {
        if (e.target.files && e.target.files.length > 0) {
            var fileName = e.target.files[0].name;
            var fileSize = (e.target.files[0].size / 1024 / 1024).toFixed(2); // 转换为MB

            // 更新文件标签显示
            var label = $(this).next('.custom-file-label');
            label.html(fileName + ' <small class="text-muted">(' + fileSize + ' MB)</small>');
            label.addClass('selected');

            // 验证文件大小
            if (e.target.files[0].size > 10 * 1024 * 1024) { // 10MB
                alert('文件大小不能超过10MB，请选择较小的文件。');
                $(this).val('');
                label.html('选择文件');
                label.removeClass('selected');
                return;
            }

            // 验证文件类型
            var allowedTypes = ['.csv', '.txt', '.xlsx', '.xls'];
            var fileExtension = '.' + fileName.split('.').pop().toLowerCase();

            if (!allowedTypes.includes(fileExtension)) {
                alert('不支持的文件格式，请选择CSV或Excel文件。');
                $(this).val('');
                label.html('选择文件');
                label.removeClass('selected');
                return;
            }

            console.log('文件选择成功:', fileName, fileSize + 'MB');
        } else {
            // 没有选择文件时重置标签
            $(this).next('.custom-file-label').html('选择文件').removeClass('selected');
        }
    });

    // 表单提交时的验证和加载状态
    $('form').on('submit', function(e) {
        var fileInput = $('#file')[0];

        if (!fileInput.files || fileInput.files.length === 0) {
            e.preventDefault();
            alert('请先选择要导入的文件。');
            return false;
        }

        // 显示加载状态
        var submitBtn = $(this).find('button[type="submit"]');
        submitBtn.prop('disabled', true);
        submitBtn.html('<i class="fas fa-spinner fa-spin"></i> 导入中...');

        // 显示进度提示
        if (!$('#import-progress').length) {
            $(this).after('<div id="import-progress" class="alert alert-info mt-3"><i class="fas fa-clock"></i> 正在处理文件，请稍候...</div>');
        }
    });

    // 拖拽上传支持
    var dropArea = $('.custom-file');

    dropArea.on('dragover', function(e) {
        e.preventDefault();
        e.stopPropagation();
        $(this).addClass('border-primary bg-light');
    });

    dropArea.on('dragleave', function(e) {
        e.preventDefault();
        e.stopPropagation();
        $(this).removeClass('border-primary bg-light');
    });

    dropArea.on('drop', function(e) {
        e.preventDefault();
        e.stopPropagation();
        $(this).removeClass('border-primary bg-light');

        var files = e.originalEvent.dataTransfer.files;
        if (files.length > 0) {
            $('#file')[0].files = files;
            $('#file').trigger('change');
        }
    });
});
</script>
@endpush