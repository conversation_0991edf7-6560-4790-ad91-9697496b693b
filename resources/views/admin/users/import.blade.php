@extends('admin.layouts.app')

@section('title', '用户导入')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">用户数据导入</h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.users.index') }}" class="btn btn-default btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回用户列表
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <!-- 成功消息 -->
                    @if (session('success'))
                    <div class="alert alert-success alert-dismissible">
                        <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                        <h5><i class="icon fas fa-check"></i> 导入成功！</h5>
                        {{ session('success') }}

                        @if (session('import_stats'))
                        <div class="mt-3">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="info-box bg-success">
                                        <span class="info-box-icon"><i class="fas fa-users"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">成功导入</span>
                                            <span class="info-box-number">{{ session('import_stats')['success_count'] }}</span>
                                        </div>
                                    </div>
                                </div>
                                @if (session('import_stats')['error_count'] > 0)
                                <div class="col-md-4">
                                    <div class="info-box bg-warning">
                                        <span class="info-box-icon"><i class="fas fa-exclamation-triangle"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">导入失败</span>
                                            <span class="info-box-number">{{ session('import_stats')['error_count'] }}</span>
                                        </div>
                                    </div>
                                </div>
                                @endif
                                <div class="col-md-4">
                                    <div class="info-box bg-info">
                                        <span class="info-box-icon"><i class="fas fa-file"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">总处理行数</span>
                                            <span class="info-box-number">{{ session('import_stats')['total_processed'] }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 操作按钮 -->
                            <div class="mt-3 text-center">
                                <a href="{{ route('admin.users.index') }}" class="btn btn-primary">
                                    <i class="fas fa-list"></i> 查看用户列表
                                </a>
                                <button type="button" class="btn btn-secondary ml-2" onclick="location.reload()">
                                    <i class="fas fa-redo"></i> 继续导入
                                </button>
                            </div>
                        </div>
                        @endif
                    </div>
                    @endif

                    <!-- 错误消息 -->
                    @if ($errors->any())
                    <div class="alert alert-danger alert-dismissible">
                        <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                        <h5><i class="icon fas fa-ban"></i> 导入失败！</h5>
                        <ul class="mb-0">
                            @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                    @endif

                    <!-- 错误行详情 -->
                    @if (session('errorRows') && count(session('errorRows')) > 0)
                    <div class="alert alert-warning alert-dismissible">
                        <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                        <h5><i class="icon fas fa-exclamation-triangle"></i> 部分数据导入失败</h5>
                        <p>以下行数据导入失败，请检查数据格式：</p>
                        <div class="error-details" style="max-height: 200px; overflow-y: auto;">
                            <ul class="mb-0">
                                @foreach (session('errorRows') as $errorRow)
                                <li><small>{{ $errorRow }}</small></li>
                                @endforeach
                            </ul>
                        </div>
                    </div>
                    @endif

                    <!-- 导入说明 -->
                    <div class="alert alert-info">
                        <h5><i class="icon fas fa-info"></i> 导入说明</h5>
                        <ul class="mb-0">
                            <li>支持 CSV 和 Excel 文件格式（.csv, .xlsx, .xls）</li>
                            <li>文件大小限制：10MB</li>
                            <li><strong>必填字段：</strong>姓名</li>
                            <li><strong>联系方式：</strong>邮箱和手机号至少要填写一个</li>
                            <li><strong>可选字段：</strong>邮箱、密码、用户名、积分、角色、状态、备注</li>
                            <li>如果提供密码，长度不能少于6位；如果不提供，系统将生成默认密码</li>
                            <li>手机号格式：11位数字，以1开头</li>
                            <li>角色可填：普通用户、管理员</li>
                            <li>状态可填：启用、禁用</li>
                            <li>系统会自动检测文件编码并转换为UTF-8</li>
                            <li>导入过程中会验证数据唯一性，重复数据将被跳过</li>
                        </ul>
                    </div>

                    <!-- 下载模板 -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card card-outline card-primary">
                                <div class="card-header">
                                    <h3 class="card-title">下载导入模板</h3>
                                </div>
                                <div class="card-body">
                                    <p>请先下载模板文件，按照模板格式准备数据：</p>
                                    <div class="btn-group">
                                        <a href="{{ route('admin.points.users.download.template', ['format' => 'csv']) }}"
                                           class="btn btn-outline-primary">
                                            <i class="fas fa-download"></i> 下载 CSV 模板
                                        </a>
                                        <a href="{{ route('admin.points.users.download.template', ['format' => 'excel']) }}"
                                           class="btn btn-outline-success">
                                            <i class="fas fa-download"></i> 下载 Excel 模板
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card card-outline card-warning">
                                <div class="card-header">
                                    <h3 class="card-title">字段说明</h3>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>字段名</th>
                                                <th>是否必填</th>
                                                <th>说明</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>姓名</td>
                                                <td><span class="badge badge-danger">必填</span></td>
                                                <td>用户显示名称</td>
                                            </tr>
                                            <tr>
                                                <td>邮箱</td>
                                                <td><span class="badge badge-secondary">可选</span></td>
                                                <td>用户邮箱地址</td>
                                            </tr>
                                            <tr>
                                                <td>手机号</td>
                                                <td><span class="badge badge-warning">条件必填</span></td>
                                                <td>用户手机号码（邮箱和手机号至少填一个）</td>
                                            </tr>
                                            <tr>
                                                <td>密码</td>
                                                <td><span class="badge badge-secondary">可选</span></td>
                                                <td>登录密码（至少6位，不填则生成默认密码）</td>
                                            </tr>
                                            <tr>
                                                <td>用户名</td>
                                                <td><span class="badge badge-secondary">可选</span></td>
                                                <td>登录用户名</td>
                                            </tr>
                                            <tr>
                                                <td>积分</td>
                                                <td><span class="badge badge-secondary">可选</span></td>
                                                <td>初始积分数量</td>
                                            </tr>
                                            <tr>
                                                <td>角色</td>
                                                <td><span class="badge badge-secondary">可选</span></td>
                                                <td>管理员/普通用户</td>
                                            </tr>
                                            <tr>
                                                <td>状态</td>
                                                <td><span class="badge badge-secondary">可选</span></td>
                                                <td>启用/禁用</td>
                                            </tr>
                                            <tr>
                                                <td>备注</td>
                                                <td><span class="badge badge-secondary">可选</span></td>
                                                <td>用户备注信息</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 文件上传表单 -->
                    <div class="card card-outline card-success">
                        <div class="card-header">
                            <h3 class="card-title">上传导入文件</h3>
                        </div>
                        <div class="card-body">
                            <form action="{{ route('admin.points.users.import') }}" method="POST" enctype="multipart/form-data" id="importForm">
                                @csrf
                                <div class="form-group">
                                    <label for="file">选择文件</label>
                                    <div class="input-group">
                                        <div class="custom-file">
                                            <input type="file" class="custom-file-input @error('file') is-invalid @enderror"
                                                   id="file" name="file" accept=".csv,.xlsx,.xls,.txt" required>
                                            <label class="custom-file-label" for="file">选择文件...</label>
                                        </div>
                                        <div class="input-group-append">
                                            <button type="submit" class="btn btn-success" id="importBtn">
                                                <i class="fas fa-upload"></i> 开始导入
                                            </button>
                                        </div>
                                    </div>
                                    @error('file')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">
                                        支持 CSV、Excel 文件，最大 10MB
                                    </small>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- 导入结果显示 -->
                    @if(session('success') || session('errorRows'))
                        <div class="card card-outline card-info mt-4">
                            <div class="card-header">
                                <h3 class="card-title">导入结果</h3>
                            </div>
                            <div class="card-body">
                                @if(session('success'))
                                    <div class="alert alert-success">
                                        <i class="fas fa-check"></i> {{ session('success') }}
                                    </div>
                                @endif

                                @if(session('errorRows') && count(session('errorRows')) > 0)
                                    <div class="alert alert-warning">
                                        <h5><i class="icon fas fa-exclamation-triangle"></i> 以下行导入失败：</h5>
                                        <ul class="mb-0">
                                            @foreach(session('errorRows') as $error)
                                                <li>{{ $error }}</li>
                                            @endforeach
                                        </ul>
                                    </div>
                                @endif
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
$(document).ready(function() {
    // 文件选择显示文件名
    $('.custom-file-input').on('change', function() {
        let fileName = $(this).val().split('\\').pop();
        $(this).siblings('.custom-file-label').addClass('selected').html(fileName);
    });

    // 表单提交时显示加载状态
    $('#importForm').on('submit', function() {
        $('#importBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 导入中...');
    });

    // 拖拽上传支持
    let dropArea = $('.card-body');

    dropArea.on('dragover', function(e) {
        e.preventDefault();
        $(this).addClass('border-primary');
    });

    dropArea.on('dragleave', function(e) {
        e.preventDefault();
        $(this).removeClass('border-primary');
    });

    dropArea.on('drop', function(e) {
        e.preventDefault();
        $(this).removeClass('border-primary');

        let files = e.originalEvent.dataTransfer.files;
        if (files.length > 0) {
            $('#file')[0].files = files;
            $('.custom-file-label').addClass('selected').html(files[0].name);
        }
    });
});
</script>
@endsection
