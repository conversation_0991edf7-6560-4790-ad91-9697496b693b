@extends('admin.layouts.app')

@section('title', '用户管理')

@section('header', '用户管理')

@section('content')
<div class="card">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col">
                <h5 class="mb-0">用户列表</h5>
            </div>
            <div class="col text-right">
                <!-- 批量操作按钮 -->
                <button type="button" class="btn btn-danger btn-sm mr-2" id="batchDeleteBtn" style="display: none;">
                    <i class="fas fa-trash mr-1"></i>批量删除 (<span id="selectedCount">0</span>)
                </button>

                <form action="{{ route('admin.users.sync_wechat') }}" method="POST" class="d-inline">
                    @csrf
                    <button type="submit" class="btn btn-info btn-sm mr-2">
                        <i class="fas fa-sync mr-1"></i>同步微信用户
                    </button>
                </form>
                <a href="{{ route('admin.points.users.import.show') }}" class="btn btn-success btn-sm mr-2">
                    <i class="fas fa-upload mr-1"></i>导入用户
                </a>
                <a href="{{ route('admin.users.create') }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-plus mr-1"></i>添加用户
                </a>
            </div>
        </div>
    </div>
    <div class="card-body">
        <!-- 搜索表单 -->
        <form action="{{ route('admin.users.index') }}" method="GET" class="mb-4">
            <div class="row">
                <div class="col-md-3 mb-2">
                    <input type="text" class="form-control" name="search" placeholder="用户名/邮箱/手机" value="{{ request('search') }}">
                </div>
                <div class="col-md-2 mb-2">
                    <select class="form-control" name="role">
                        <option value="">所有角色</option>
                        <option value="admin" {{ request('role') === 'admin' ? 'selected' : '' }}>管理员</option>
                        <option value="user" {{ request('role') === 'user' ? 'selected' : '' }}>普通用户</option>
                    </select>
                </div>
                <div class="col-md-2 mb-2">
                    <select class="form-control" name="status">
                        <option value="">所有状态</option>
                        <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>启用</option>
                        <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>禁用</option>
                    </select>
                </div>
                <div class="col-md-2 mb-2">
                    <input type="date" class="form-control" name="start_date" placeholder="开始日期" value="{{ request('start_date') }}">
                </div>
                <div class="col-md-2 mb-2">
                    <input type="date" class="form-control" name="end_date" placeholder="结束日期" value="{{ request('end_date') }}">
                </div>
                <div class="col-md-1 mb-2">
                    <button type="submit" class="btn btn-primary btn-block">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </form>

        <!-- 用户列表 -->
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th width="40">
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="selectAll">
                                <label class="custom-control-label" for="selectAll"></label>
                            </div>
                        </th>
                        <th>ID</th>
                        <th>头像</th>
                        <th>用户名</th>
                        <th>邮箱</th>
                        <th>手机</th>
                        <th>微信信息</th>
                        <th>角色</th>
                        <th>状态</th>
                        <th>积分</th>
                        <th>注册时间</th>
                        <th>最后登录</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($users as $user)
                        <tr>
                            <td>
                                @if($user->id !== auth()->id())
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input user-checkbox" id="user{{ $user->id }}" value="{{ $user->id }}">
                                        <label class="custom-control-label" for="user{{ $user->id }}"></label>
                                    </div>
                                @endif
                            </td>
                            <td>{{ $user->id }}</td>
                            <td>
                                @if($user->avatar)
                                    <img src="{{ avatar_url($user->avatar) }}" alt="{{ $user->name }} Avatar" class="rounded-circle img-thumbnail" width="35" height="35" style="object-fit: cover;">
                                @else
                                    <img src="{{ asset('images/default-avatar.png') }}" alt="Default Avatar" class="rounded-circle img-thumbnail" width="35" height="35" style="object-fit: cover;">
                                @endif
                            </td>
                            <td>{{ $user->name }}</td>
                            <td>{{ $user->email }}</td>
                            <td>{{ $user->phone }}</td>
                            <td>
                                @if($user->wechat_id)
                                    <span class="badge bg-info" title="微信ID: {{ $user->wechat_id }}">
                                        <i class="fab fa-weixin"></i> {{ $user->wechat_account ?: '已绑定' }}
                                    </span>
                                @else
                                    <span class="badge bg-light text-dark">未绑定</span>
                                @endif
                            </td>
                            <td>
                                @if($user->is_admin)
                                    <span class="badge bg-danger">管理员</span>
                                @else
                                    <span class="badge bg-secondary">普通用户</span>
                                @endif
                            </td>
                            <td>
                                @if($user->is_active)
                                    <span class="badge bg-success">启用</span>
                                @else
                                    <span class="badge bg-danger">禁用</span>
                                @endif
                            </td>
                            <td>{{ $user->points }}</td>
                            <td>{{ $user->created_at ? $user->created_at->format('Y-m-d H:i') : '-' }}</td>
                            <td>{{ $user->last_login_at ? $user->last_login_at->format('Y-m-d H:i') : '-' }}</td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{{ route('admin.users.show', $user->id) }}" class="btn btn-info" title="查看">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('admin.users.edit', $user->id) }}" class="btn btn-primary" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    @if($user->id !== auth()->id())
                                        <button type="button" class="btn btn-danger" title="删除" data-bs-toggle="modal" data-bs-target="#deleteModal{{ $user->id }}">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    @endif
                                </div>

                                <!-- 删除确认模态框 -->
                                @if($user->id !== auth()->id())
                                    <div class="modal fade" id="deleteModal{{ $user->id }}" tabindex="-1">
                                        <div class="modal-dialog">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title">确认删除</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                </div>
                                                <div class="modal-body">
                                                    确定要删除用户 "{{ $user->name }}" 吗？此操作不可恢复。
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                                    <form action="{{ route('admin.users.destroy', $user->id) }}" method="POST" class="d-inline">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-danger">删除</button>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endif
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="13" class="text-center">暂无用户数据</td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- 分页 -->
        <div class="mt-4">
            {{ $users->appends(request()->query())->links() }}
        </div>
    </div>
</div>

<!-- 批量删除确认模态框 -->
<div class="modal fade" id="batchDeleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">批量删除确认</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>确定要删除选中的 <strong id="deleteCount">0</strong> 个用户吗？</p>
                <p class="text-danger"><i class="fas fa-exclamation-triangle"></i> 此操作不可恢复！</p>
                <div id="deleteUserList" class="mt-3"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmBatchDelete">
                    <i class="fas fa-trash"></i> 确认删除
                </button>
            </div>
        </div>
    </div>
</div>

@endsection

@section('scripts')
<script>
$(document).ready(function() {
    const batchDeleteBtn = $('#batchDeleteBtn');
    const selectedCount = $('#selectedCount');
    const selectAllCheckbox = $('#selectAll');
    const userCheckboxes = $('.user-checkbox');

    // 全选/取消全选
    selectAllCheckbox.on('change', function() {
        const isChecked = $(this).is(':checked');
        userCheckboxes.prop('checked', isChecked);
        updateBatchDeleteButton();
    });

    // 单个复选框变化
    userCheckboxes.on('change', function() {
        updateSelectAllState();
        updateBatchDeleteButton();
    });

    // 更新全选状态
    function updateSelectAllState() {
        const totalCheckboxes = userCheckboxes.length;
        const checkedCheckboxes = userCheckboxes.filter(':checked').length;

        if (checkedCheckboxes === 0) {
            selectAllCheckbox.prop('indeterminate', false).prop('checked', false);
        } else if (checkedCheckboxes === totalCheckboxes) {
            selectAllCheckbox.prop('indeterminate', false).prop('checked', true);
        } else {
            selectAllCheckbox.prop('indeterminate', true);
        }
    }

    // 更新批量删除按钮状态
    function updateBatchDeleteButton() {
        const checkedCount = userCheckboxes.filter(':checked').length;
        selectedCount.text(checkedCount);

        if (checkedCount > 0) {
            batchDeleteBtn.show();
        } else {
            batchDeleteBtn.hide();
        }
    }

    // 批量删除按钮点击
    batchDeleteBtn.on('click', function() {
        const selectedUsers = [];
        const selectedIds = [];

        userCheckboxes.filter(':checked').each(function() {
            const userId = $(this).val();
            const userName = $(this).closest('tr').find('td:nth-child(4)').text().trim();
            selectedUsers.push(userName);
            selectedIds.push(userId);
        });

        // 更新模态框内容
        $('#deleteCount').text(selectedUsers.length);
        const userListHtml = selectedUsers.map(name => `<span class="badge badge-secondary mr-1">${name}</span>`).join('');
        $('#deleteUserList').html('<div class="mb-2"><strong>将要删除的用户：</strong></div>' + userListHtml);

        // 存储要删除的用户ID
        $('#confirmBatchDelete').data('userIds', selectedIds);

        // 显示模态框
        $('#batchDeleteModal').modal('show');
    });

    // 确认批量删除
    $('#confirmBatchDelete').on('click', function() {
        const userIds = $(this).data('userIds');
        const button = $(this);

        // 显示加载状态
        button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 删除中...');

        // 发送删除请求
        $.ajax({
            url: '{{ route("admin.users.batch-delete") }}',
            method: 'POST',
            data: {
                user_ids: userIds,
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                $('#batchDeleteModal').modal('hide');

                if (response.success) {
                    // 显示成功消息
                    showAlert('success', response.message || '批量删除成功');

                    // 刷新页面或移除已删除的行
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                } else {
                    showAlert('danger', response.message || '删除失败');
                }
            },
            error: function(xhr) {
                $('#batchDeleteModal').modal('hide');

                let errorMessage = '删除失败';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }

                showAlert('danger', errorMessage);
            },
            complete: function() {
                // 恢复按钮状态
                button.prop('disabled', false).html('<i class="fas fa-trash"></i> 确认删除');
            }
        });
    });

    // 显示提示消息
    function showAlert(type, message) {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="close" data-dismiss="alert">
                    <span>&times;</span>
                </button>
            </div>
        `;

        // 在页面顶部显示提示
        $('.card-body').prepend(alertHtml);

        // 3秒后自动隐藏
        setTimeout(() => {
            $('.alert').fadeOut();
        }, 3000);
    }
});
</script>
@endsection