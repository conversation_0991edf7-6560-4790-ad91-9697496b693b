@extends('layouts.app')

@section('title', '积分兑换记录')

@section('styles')
<style>
    /* 京东风格主题色 */
    :root {
        --jd-red: #e93b3d;
        --jd-red-light: #f10215;
        --jd-gray: #f5f5f5;
        --jd-light: #fff8f0;
        --jd-orange: #ff6700;
        --jd-blue: #005aa0;
    }

    /* 整体布局 - 京东风格 */
    body {
        background-color: var(--jd-gray);
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    }

    .exchange-history-container {
        background-color: var(--jd-gray);
        padding-top: 1rem;
        padding-bottom: 2rem;
        min-height: calc(100vh - 200px);
    }

    /* 页面标题 - 京东风格 */
    .page-title {
        background: linear-gradient(135deg, var(--jd-red-light), var(--jd-red));
        color: white;
        padding: 20px 0;
        margin-bottom: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border-radius: 4px;
    }

    .page-title h3 {
        margin: 0;
        font-weight: 600;
        font-size: 1.5rem;
    }

    /* 筛选栏 - 京东风格 */
    .filter-section {
        background: white;
        border-radius: 4px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        border: 1px solid #e0e0e0;
    }

    .filter-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 2px solid var(--jd-red);
        display: inline-block;
    }

    .filter-tabs {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-bottom: 15px;
    }

    .filter-tab {
        padding: 8px 16px;
        border: 1px solid #ddd;
        border-radius: 3px;
        background: white;
        color: #666;
        text-decoration: none;
        font-size: 13px;
        font-weight: 500;
        transition: all 0.2s;
        cursor: pointer;
    }

    .filter-tab:hover {
        border-color: var(--jd-red);
        color: var(--jd-red);
        background: #fafafa;
        text-decoration: none;
    }

    .filter-tab.active {
        background: var(--jd-red);
        color: white;
        border-color: var(--jd-red);
        box-shadow: 0 2px 4px rgba(233, 59, 61, 0.2);
    }

    .filter-tab .badge {
        font-size: 10px;
        padding: 2px 6px;
        border-radius: 10px;
        font-weight: 600;
    }

    .filter-tab.active .badge {
        background-color: rgba(255, 255, 255, 0.2) !important;
        color: white !important;
    }

    /* 搜索框样式 */
    .input-group-text {
        background: #f8f9fa;
        border-color: #ddd;
        color: #666;
    }

    .form-control {
        border-color: #ddd;
        font-size: 13px;
    }

    .form-control:focus {
        border-color: var(--jd-red);
        box-shadow: 0 0 0 0.2rem rgba(233, 59, 61, 0.25);
    }

    .btn-primary {
        background: var(--jd-red);
        border-color: var(--jd-red);
        font-size: 13px;
        font-weight: 600;
    }

    .btn-primary:hover {
        background: #d73027;
        border-color: #d73027;
    }

    .btn-outline-secondary {
        border-color: #ddd;
        color: #666;
        font-size: 13px;
    }

    .btn-outline-secondary:hover {
        background: #f8f9fa;
        border-color: #adb5bd;
        color: #495057;
    }

    .date-filter {
        display: flex;
        align-items: center;
        gap: 10px;
        flex-wrap: wrap;
    }

    .date-filter .form-control {
        border-radius: 3px;
        border: 1px solid #ddd;
        padding: 8px 12px;
        font-size: 13px;
        max-width: 150px;
    }

    .date-filter .form-control:focus {
        border-color: var(--jd-red);
        box-shadow: 0 0 0 0.2rem rgba(233, 59, 61, 0.25);
    }

    .filter-btn {
        background: var(--jd-orange);
        color: white;
        border: none;
        border-radius: 3px;
        padding: 8px 16px;
        font-size: 13px;
        font-weight: 600;
        transition: all 0.2s;
        cursor: pointer;
    }

    .filter-btn:hover {
        background: #e55a00;
        transform: translateY(-1px);
    }

    .reset-btn {
        background: #6c757d;
        color: white;
        border: none;
        border-radius: 3px;
        padding: 8px 16px;
        font-size: 13px;
        font-weight: 500;
        transition: all 0.2s;
        cursor: pointer;
        text-decoration: none;
    }

    .reset-btn:hover {
        background: #5a6268;
        color: white;
        text-decoration: none;
    }

    /* 订单卡片 - 京东风格 */
    .exchange-card {
        background-color: white;
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        padding: 15px;
        margin-bottom: 15px;
        transition: all 0.2s;
        box-shadow: 0 1px 2px rgba(0,0,0,0.05);
    }

    .exchange-card:hover {
        border-color: var(--jd-red);
        box-shadow: 0 2px 8px rgba(233, 59, 61, 0.15);
        transform: translateY(-1px);
    }

    .exchange-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 12px;
        border-bottom: 1px solid #f0f0f0;
        margin-bottom: 15px;
    }

    .exchange-date {
        color: #666;
        font-size: 13px;
        font-weight: 500;
    }

    .exchange-status {
        padding: 4px 12px;
        border-radius: 3px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    /* 状态颜色 - 京东风格 */
    .status-pending {
        background: linear-gradient(45deg, #faad14, #ffc53d);
        color: white;
        box-shadow: 0 2px 4px rgba(250, 173, 20, 0.3);
    }
    .status-shipped {
        background: linear-gradient(45deg, #1890ff, #40a9ff);
        color: white;
        box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);
    }
    .status-completed {
        background: linear-gradient(45deg, #52c41a, #73d13d);
        color: white;
        box-shadow: 0 2px 4px rgba(82, 196, 26, 0.3);
    }
    .status-cancelled {
        background: linear-gradient(45deg, #ff4d4f, #ff7875);
        color: white;
        box-shadow: 0 2px 4px rgba(255, 77, 79, 0.3);
    }

    .exchange-content {
        display: flex;
        align-items: flex-start;
    }

    .exchange-image {
        width: 70px;
        height: 70px;
        flex-shrink: 0;
        border-radius: 4px;
        overflow: hidden;
        margin-right: 15px;
        border: 1px solid #f0f0f0;
    }

    .exchange-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.2s;
    }

    .exchange-image:hover img {
        transform: scale(1.05);
    }

    .exchange-info {
        flex-grow: 1;
        min-width: 0;
    }

    .exchange-product-name {
        font-size: 15px;
        font-weight: 600;
        margin-bottom: 8px;
        color: #333;
        line-height: 1.4;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .exchange-product-name:hover {
        color: var(--jd-red);
    }

    .exchange-order-no {
        color: #666;
        font-size: 13px;
        margin-bottom: 5px;
        font-family: 'Courier New', monospace;
    }

    .exchange-meta {
        color: #666;
        font-size: 13px;
        margin-bottom: 5px;
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .exchange-points {
        color: var(--jd-red);
        font-weight: 700;
        font-size: 16px;
        font-family: "Arial", sans-serif;
    }

    .exchange-quantity {
        color: #999;
        font-size: 13px;
    }

    /* 操作按钮 - 京东风格 */
    .exchange-actions {
        margin-top: 15px;
        text-align: right;
        display: flex;
        justify-content: flex-end;
        gap: 8px;
        flex-wrap: wrap;
    }

    .btn-sm {
        font-size: 12px;
        padding: 6px 12px;
        border-radius: 3px;
        font-weight: 500;
        transition: all 0.2s;
        border: 1px solid;
        text-decoration: none;
    }

    .btn-outline-primary {
        color: var(--jd-blue);
        border-color: var(--jd-blue);
        background: white;
    }

    .btn-outline-primary:hover {
        background: var(--jd-blue);
        color: white;
        transform: translateY(-1px);
    }

    .btn-outline-danger {
        color: #ff4d4f;
        border-color: #ff4d4f;
        background: white;
    }

    .btn-outline-danger:hover {
        background: #ff4d4f;
        color: white;
        transform: translateY(-1px);
    }

    .btn-success {
        background: linear-gradient(45deg, #52c41a, #73d13d);
        border-color: #52c41a;
        color: white;
    }

    .btn-success:hover {
        background: linear-gradient(45deg, #389e0d, #52c41a);
        transform: translateY(-1px);
    }

    /* 空状态 - 京东风格 */
    .empty-state {
        text-align: center;
        padding: 60px 20px;
        background-color: white;
        border-radius: 4px;
        border: 1px solid #e0e0e0;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .empty-state-icon {
        font-size: 64px;
        color: #ddd;
        margin-bottom: 20px;
        opacity: 0.6;
    }

    .empty-state-text {
        color: #999;
        margin-bottom: 25px;
        font-size: 16px;
    }

    .empty-state .btn {
        background: var(--jd-red);
        color: white;
        border: none;
        border-radius: 3px;
        padding: 12px 24px;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.2s;
    }

    .empty-state .btn:hover {
        background: #d73027;
        transform: translateY(-1px);
        color: white;
        text-decoration: none;
    }

    /* 分页 - 京东风格 */
    .pagination-container {
        margin-top: 30px;
        display: flex;
        justify-content: center;
    }

    .pagination .page-link {
        border: 1px solid #ddd;
        color: #666;
        margin: 0 2px;
        border-radius: 3px;
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 13px;
        font-weight: 500;
        transition: all 0.2s;
    }

    .pagination .page-link:hover {
        border-color: var(--jd-red);
        color: var(--jd-red);
        background-color: #fafafa;
    }

    .pagination .page-item.active .page-link {
        background-color: var(--jd-red);
        border-color: var(--jd-red);
        color: white;
        box-shadow: 0 2px 4px rgba(233, 59, 61, 0.2);
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .filter-tabs {
            gap: 8px;
        }

        .filter-tab {
            padding: 6px 12px;
            font-size: 12px;
        }

        .date-filter {
            flex-direction: column;
            align-items: stretch;
            gap: 8px;
        }

        .date-filter .form-control {
            max-width: none;
        }

        .exchange-image {
            width: 60px;
            height: 60px;
            margin-right: 12px;
        }

        .exchange-product-name {
            font-size: 14px;
        }

        .exchange-actions {
            flex-direction: column;
            align-items: stretch;
        }

        .btn-sm {
            width: 100%;
            margin-bottom: 5px;
        }

        .pagination .page-link {
            width: 32px;
            height: 32px;
            font-size: 12px;
        }
    }
</style>
@endsection

@section('content')
<div class="container exchange-history-container">
    <div class="row justify-content-center">
        <div class="col-md-10 col-lg-8">
            <!-- 页面标题 -->
            <div class="page-title text-center">
                <h3><i class="fas fa-history me-2"></i>积分兑换记录</h3>
            </div>

            <!-- 筛选区域 -->
            <div class="filter-section">
                <div class="filter-title">
                    <i class="fas fa-filter me-2"></i>订单筛选
                </div>

                <!-- 状态筛选标签 -->
                <div class="filter-tabs">
                    <a href="{{ route('points.exchange.history') }}"
                       class="filter-tab {{ !request('status') ? 'active' : '' }}">
                        <i class="fas fa-list me-1"></i>全部订单
                        @if(isset($statistics))
                            <span class="badge bg-secondary ms-1">{{ $statistics['total'] }}</span>
                        @endif
                    </a>
                    <a href="{{ route('points.exchange.history', ['status' => 'pending']) }}"
                       class="filter-tab {{ request('status') == 'pending' ? 'active' : '' }}">
                        <i class="fas fa-clock me-1"></i>待发货
                        @if(isset($statistics))
                            <span class="badge bg-warning ms-1">{{ $statistics['pending'] }}</span>
                        @endif
                    </a>
                    <a href="{{ route('points.exchange.history', ['status' => 'shipped']) }}"
                       class="filter-tab {{ request('status') == 'shipped' ? 'active' : '' }}">
                        <i class="fas fa-shipping-fast me-1"></i>已发货
                        @if(isset($statistics))
                            <span class="badge bg-info ms-1">{{ $statistics['shipped'] }}</span>
                        @endif
                    </a>
                    <a href="{{ route('points.exchange.history', ['status' => 'completed']) }}"
                       class="filter-tab {{ request('status') == 'completed' ? 'active' : '' }}">
                        <i class="fas fa-check-circle me-1"></i>已完成
                        @if(isset($statistics))
                            <span class="badge bg-success ms-1">{{ $statistics['completed'] }}</span>
                        @endif
                    </a>
                    <a href="{{ route('points.exchange.history', ['status' => 'cancelled']) }}"
                       class="filter-tab {{ request('status') == 'cancelled' ? 'active' : '' }}">
                        <i class="fas fa-times-circle me-1"></i>已取消
                        @if(isset($statistics))
                            <span class="badge bg-danger ms-1">{{ $statistics['cancelled'] }}</span>
                        @endif
                    </a>
                </div>

                <!-- 搜索功能 -->
                <form action="{{ route('points.exchange.history') }}" method="GET" class="mb-3">
                    @if(request('status'))
                        <input type="hidden" name="status" value="{{ request('status') }}">
                    @endif
                    @if(request('start_date'))
                        <input type="hidden" name="start_date" value="{{ request('start_date') }}">
                    @endif
                    @if(request('end_date'))
                        <input type="hidden" name="end_date" value="{{ request('end_date') }}">
                    @endif

                    <div class="row g-2">
                        <div class="col-md-4">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" name="search" class="form-control"
                                       placeholder="搜索商品名称" value="{{ request('search') }}">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-receipt"></i>
                                </span>
                                <input type="text" name="order_no" class="form-control"
                                       placeholder="搜索订单号" value="{{ request('order_no') }}">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary flex-fill">
                                    <i class="fas fa-search me-1"></i>搜索
                                </button>
                                <a href="{{ route('points.exchange.history') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-undo"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </form>

                <!-- 日期筛选 -->
                <form action="{{ route('points.exchange.history') }}" method="GET" class="date-filter">
                    @if(request('status'))
                        <input type="hidden" name="status" value="{{ request('status') }}">
                    @endif

                    <label for="start_date" class="form-label mb-0">开始日期:</label>
                    <input type="date" id="start_date" name="start_date" class="form-control"
                           value="{{ request('start_date') }}">

                    <label for="end_date" class="form-label mb-0">结束日期:</label>
                    <input type="date" id="end_date" name="end_date" class="form-control"
                           value="{{ request('end_date') }}">

                    <button type="submit" class="filter-btn">
                        <i class="fas fa-search me-1"></i>筛选
                    </button>

                    <a href="{{ route('points.exchange.history') }}" class="reset-btn">
                        <i class="fas fa-undo me-1"></i>重置
                    </a>
                </form>
            </div>

            <!-- 订单列表 -->
            @forelse($orders as $order)
                <div class="exchange-card">
                    <div class="exchange-header">
                        <span class="exchange-date">
                            <i class="fas fa-calendar-alt me-1"></i>
                            {{ $order->created_at->format('Y-m-d H:i') }}
                        </span>
                        <span class="exchange-status status-{{ $order->status }}">
                            @switch($order->status)
                                @case('pending')
                                    <i class="fas fa-clock me-1"></i>待发货
                                    @break
                                @case('shipped')
                                    <i class="fas fa-shipping-fast me-1"></i>已发货
                                    @break
                                @case('completed')
                                    <i class="fas fa-check-circle me-1"></i>已完成
                                    @break
                                @case('cancelled')
                                    <i class="fas fa-times-circle me-1"></i>已取消
                                    @break
                                @default
                                    {{ $order->status }}
                            @endswitch
                        </span>
                    </div>

                    <div class="exchange-content">
                        <div class="exchange-image">
                            @php
                                $product = $order->product ?? ($order->exchange ? $order->exchange->product : null);
                            @endphp
                            <img src="{{ $product ? product_image_url($product->cover_image) : asset('images/placeholder.jpg') }}"
                                 alt="{{ $product ? $product->name : '商品图片' }}"
                                 loading="lazy">
                        </div>
                        <div class="exchange-info">
                            <div class="exchange-product-name" title="{{ $product ? $product->name : '商品信息已删除' }}">
                                {{ $product ? $product->name : '商品信息已删除' }}
                            </div>
                            <div class="exchange-order-no">
                                <i class="fas fa-receipt me-1"></i>订单号: {{ $order->order_no }}
                            </div>
                            <div class="exchange-meta">
                                <span>
                                    <i class="fas fa-coins me-1"></i>消耗积分:
                                    <span class="exchange-points">{{ $order->points }}</span>
                                </span>
                                @if(isset($order->quantity) && $order->quantity > 1)
                                    <span class="exchange-quantity">
                                        <i class="fas fa-cubes me-1"></i>数量: {{ $order->quantity }}
                                    </span>
                                @endif
                            </div>
                        </div>
                    </div>

                    <div class="exchange-actions">
                        <a href="{{ route('points.exchange.order.detail', $order->order_no) }}"
                           class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-eye me-1"></i>查看详情
                        </a>

                        @if($order->status === 'pending')
                            <button class="btn btn-outline-danger btn-sm cancel-order" data-order="{{ $order->order_no }}">
                                <i class="fas fa-times me-1"></i>取消订单
                            </button>
                        @endif

                        @if($order->status === 'shipped')
                            <form action="{{ route('points.exchange.order.confirm', $order->order_no) }}" method="POST" class="d-inline">
                                @csrf
                                <button type="submit" class="btn btn-success btn-sm"
                                        onclick="return confirm('确认已收到商品吗？')">
                                    <i class="fas fa-check me-1"></i>确认收货
                                </button>
                            </form>
                        @endif
                    </div>
                </div>
            @empty
                <div class="empty-state">
                    <div class="empty-state-icon">
                        <i class="fas fa-shopping-bag"></i>
                    </div>
                    <p class="empty-state-text">
                        @if(request('status') || request('start_date') || request('end_date'))
                            没有找到符合条件的兑换记录
                        @else
                            您还没有任何积分兑换记录
                        @endif
                    </p>
                    @if(request('status') || request('start_date') || request('end_date'))
                        <a href="{{ route('points.exchange.history') }}" class="btn">
                            <i class="fas fa-list me-1"></i>查看全部记录
                        </a>
                    @else
                        <a href="{{ route('products.index') }}" class="btn">
                            <i class="fas fa-shopping-cart me-1"></i>去兑换商品
                        </a>
                    @endif
                </div>
            @endforelse

            <!-- 分页 -->
            <div class="pagination-container">
                {{ $orders->withQueryString()->links('pagination::bootstrap-5') }}
            </div>

        </div>
    </div>
</div>

@push('scripts')
<script>
$(document).ready(function() {
    // Keep the Sentry and Cancel button logic
    // ... (Existing Sentry and cancel logic remains here) ...

    // 取消订单按钮点击 (AJAX)
    $('.cancel-order').click(function(e) {
        e.preventDefault();
        const orderNo = $(this).data('order');
        const button = $(this);

        if (confirm('确定要取消该订单吗？积分将会返还。')) {
            button.prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 取消中...');

            $.ajax({
                url: "{{ url('points/exchange/order') }}/" + orderNo + "/cancel", // Use url() helper for robustness
                type: 'POST',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    alert(response.message || '订单已成功取消');
                    location.reload(); // Reload page to reflect changes
                },
                error: function(xhr) {
                    const errorMessage = xhr.responseJSON?.message || '取消订单失败，请稍后重试';
                    alert(errorMessage);
                    button.prop('disabled', false).text('取消订单'); // Re-enable button
                }
            });
        }
    });
});
</script>
@endpush
@endsection