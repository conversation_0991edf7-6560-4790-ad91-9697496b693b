@extends('layouts.app')

@section('title', '积分兑换确认')

@section('styles')
<style>
    .exchange-confirm-container {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        padding: 20px;
        margin-bottom: 20px;
    }
    .product-summary {
        display: flex;
        padding-bottom: 20px;
        border-bottom: 1px solid #f2f2f2;
        margin-bottom: 20px;
    }
    .product-image {
        width: 120px;
        height: 120px;
        flex-shrink: 0;
        border-radius: 4px;
        overflow: hidden;
        margin-right: 20px;
    }
    .product-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    .product-info {
        flex-grow: 1;
    }
    .product-name {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 8px;
    }
    .product-points {
        color: #ff5000;
        font-size: 16px;
        margin-bottom: 8px;
    }
    .product-points .icon {
        display: inline-block;
        width: 16px;
        height: 16px;
        background-color: #ffc107;
        border-radius: 50%;
        margin-right: 5px;
        vertical-align: middle;
    }
    .user-points {
        margin-bottom: 20px;
    }
    .user-points-info {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
    }
    .user-points-balance {
        font-size: 16px;
        margin-right: 20px;
    }
    .user-points-balance strong {
        color: #ff5000;
    }
    .user-points-status {
        padding: 4px 10px;
        border-radius: 20px;
        font-size: 12px;
    }
    .status-sufficient {
        background-color: #e6f7e6;
        color: #52c41a;
    }
    .status-insufficient {
        background-color: #fff2f0;
        color: #ff4d4f;
    }
    .exchange-form {
        margin-top: 20px;
    }
    .form-section {
        margin-bottom: 20px;
    }
    .section-title {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 15px;
        color: #333;
    }
    .address-list {
        margin-bottom: 20px;
    }
    .address-item {
        padding: 15px;
        border: 1px solid #ddd;
        border-radius: 4px;
        margin-bottom: 10px;
        cursor: pointer;
        position: relative;
        transition: all 0.3s;
    }
    .address-item:hover {
        border-color: #ff5000;
        box-shadow: 0 2px 8px rgba(255,80,0,0.15);
    }
    .address-item.selected {
        border-color: #ff5000;
        background-color: #fff0e6;
    }
    .address-item.selected:before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        border-style: solid;
        border-width: 0 24px 24px 0;
        border-color: transparent #ff5000 transparent transparent;
    }
    .address-item.selected:after {
        content: '✓';
        position: absolute;
        top: 2px;
        right: 5px;
        color: white;
        font-size: 12px;
        font-weight: bold;
    }
    .address-name {
        font-weight: bold;
    }
    .address-phone {
        color: #666;
        margin-left: 10px;
    }
    .address-detail {
        margin-top: 5px;
        color: #333;
    }
    .exchange-summary {
        background-color: #f9f9f9;
        padding: 15px;
        border-radius: 4px;
    }
    .summary-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
    }
    .summary-label {
        color: #666;
    }
    .summary-value {
        font-weight: bold;
    }
    .summary-total {
        font-size: 18px;
        color: #ff5000;
    }
    .btn-exchange {
        background: linear-gradient(to right, #ff9000, #ff5000);
        color: #fff;
        border: none;
        width: 100%;
        padding: 12px;
        font-size: 16px;
        border-radius: 4px;
        margin-top: 15px;
    }
    .btn-exchange:disabled {
        background: #cccccc;
        cursor: not-allowed;
    }
    .btn-back {
        background: #f2f2f2;
        color: #666;
        border: none;
        width: 100%;
        padding: 12px;
        font-size: 16px;
        border-radius: 4px;
        margin-top: 10px;
    }
</style>
@endsection

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">确认兑换</h5>
                </div>

                <div class="card-body">
                    <!-- 商品信息 -->
                    <div class="card mb-4">
                        <div class="card-body">
                            <h6>商品信息</h6>
                            <div class="d-flex align-items-center">
                                <img src="{{ $product->cover_image }}" alt="{{ $product->name }}"
                                     class="img-thumbnail me-3" style="width: 80px;">
                                <div>
                                    <h6 class="mb-1">{{ $product->name }}</h6>
                                    <p class="text-muted mb-2">{{ $product->description }}</p>
                                    <div class="d-flex align-items-center">
                                        <p class="mb-0 text-danger">消耗积分：{{ $product->pointExchange->points_required ?? $product->points_cost ?? 0 }} × {{ $quantity ?? 1 }}</p>
                                        <div class="input-group input-group-sm ms-3" style="width: 120px;">
                                            <button type="button" class="btn btn-outline-secondary btn-quantity-decrease">-</button>
                                            <input type="number" class="form-control text-center quantity-input"
                                                   value="{{ $quantity ?? 1 }}" min="1" max="{{ $product->stock }}" id="quantity">
                                            <button type="button" class="btn btn-outline-secondary btn-quantity-increase">+</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 收货地址 -->
                    <div class="card mb-4">
                        <div class="card-body">
                            <h6>收货地址</h6>
                            @if($addresses->count() > 0)
                                <div class="list-group">
                                    @foreach($addresses as $index => $address)
                                    <label class="list-group-item">
                                        <input class="form-check-input me-1 address-radio" type="radio" name="address_id"
                                               value="{{ $address->id }}"
                                               {{ $address->is_default || $index === 0 ? 'checked' : '' }}>
                                        <div>
                                            <p class="mb-1">
                                                <span class="fw-bold">{{ $address->contact_name }}</span>
                                                <span class="ms-2">{{ $address->contact_phone }}</span>
                                            </p>
                                            <p class="mb-0 text-muted">{{ $address->full_address }}</p>
                                        </div>
                                    </label>
                                    @endforeach
                                </div>
                                <div class="alert alert-info mt-2 selected-address-info">
                                    已选择地址ID: <span id="selectedAddressDisplay">{{ $addresses->first()->id }}</span>
                                </div>
                            @else
                                <div class="alert alert-warning">
                                    您还没有收货地址，请先
                                    <a href="{{ route('user.addresses.create') }}" class="alert-link">添加收货地址</a>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- 积分信息 -->
                    <div class="card mb-4">
                        <div class="card-body">
                            <h6>积分信息</h6>
                            <p class="mb-1">当前积分：{{ $userPoints }}</p>
                            <p class="mb-1">消耗积分：<span id="totalPointsCost">{{ ($product->pointExchange->points_required ?? $product->points_cost ?? 0) * ($quantity ?? 1) }}</span></p>
                            <p class="mb-0" id="pointsStatusText" class="{{ $canExchange ? 'text-success' : 'text-danger' }}">
                                {{ $canExchange ? '积分充足，可以兑换' : '积分不足，无法兑换' }}
                            </p>
                        </div>
                    </div>

                    <!-- 提交按钮 -->
                    <div class="text-center">
                        @if($addresses->count() > 0)
                        <form action="{{ route('points.exchange.process') }}" method="POST" id="exchangeForm">
                            @csrf
                            <input type="hidden" name="product_id" value="{{ $product->id }}">
                            <input type="hidden" name="quantity" id="formQuantity" value="{{ $quantity ?? 1 }}">
                            <input type="hidden" name="address_id" id="selectedAddressId" value="{{ $addresses->first()->id }}">
                            <button type="submit" class="btn btn-primary btn-lg" id="confirmExchangeBtn" {{ $canExchange ? '' : 'disabled' }}>确认兑换</button>
                            <a href="{{ url()->previous() }}" class="btn btn-secondary btn-lg ms-2">返回</a>
                        </form>
                        @else
                        <button type="button" class="btn btn-primary btn-lg" disabled>
                            请先添加收货地址
                        </button>
                        <a href="{{ url()->previous() }}" class="btn btn-secondary btn-lg ms-2">返回</a>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
$(document).ready(function() {
    // 设置选中的地址ID
    function updateSelectedAddress() {
        const selectedAddress = $('input[name="address_id"]:checked').val();
        // 确保始终有值
        if (selectedAddress) {
            $('#selectedAddressId').val(selectedAddress);
            $('#selectedAddressDisplay').text(selectedAddress);
        }
    }

    // 初始化时确保有地址被选中
    if ($('input[name="address_id"]').length > 0) {
        if (!$('input[name="address_id"]:checked').length) {
            $('input[name="address_id"]').first().prop('checked', true);
        }
        updateSelectedAddress();
    }

    // 监听地址选择变化
    $('input[name="address_id"]').change(updateSelectedAddress);

    // 处理数量变化逻辑
    const quantityInput = $('#quantity');
    const decreaseBtn = $('.btn-quantity-decrease');
    const increaseBtn = $('.btn-quantity-increase');
    const totalPointsCost = $('#totalPointsCost');
    const confirmBtn = $('#confirmExchangeBtn');
    const pointsStatusText = $('#pointsStatusText');
    const formQuantity = $('#formQuantity');

    // 产品单价和库存
    const pointsCost = {{ $product->pointExchange->points_required ?? $product->points_cost ?? 0 }};
    const maxStock = {{ $product->stock }};
    const userPoints = {{ $userPoints }};

    // 更新积分和按钮状态
    function updatePointsInfo() {
        const quantity = parseInt(quantityInput.val()) || 1;
        const totalPoints = pointsCost * quantity;

        // 更新显示和表单中的数量
        totalPointsCost.text(totalPoints);
        formQuantity.val(quantity);

        // 检查用户积分是否足够
        const canExchange = userPoints >= totalPoints;
        confirmBtn.prop('disabled', !canExchange);

        // 更新积分状态文本
        if (canExchange) {
            pointsStatusText.removeClass('text-danger').addClass('text-success').text('积分充足，可以兑换');
        } else {
            pointsStatusText.removeClass('text-success').addClass('text-danger').text('积分不足，无法兑换');
        }
    }

    // 绑定事件
    decreaseBtn.click(function() {
        let value = parseInt(quantityInput.val()) || 1;
        if (value > 1) {
            quantityInput.val(value - 1);
            updatePointsInfo();
        }
    });

    increaseBtn.click(function() {
        let value = parseInt(quantityInput.val()) || 1;
        if (value < maxStock) {
            quantityInput.val(value + 1);
            updatePointsInfo();
        } else {
            alert('已达到最大库存数量: ' + maxStock);
        }
    });

    quantityInput.on('change', function() {
        let value = parseInt($(this).val()) || 1;

        if (value < 1) {
            $(this).val(1);
            value = 1;
        } else if (value > maxStock) {
            $(this).val(maxStock);
            value = maxStock;
            alert('已达到最大库存数量: ' + maxStock);
        }

        updatePointsInfo();
    });

    // 表单提交前验证
    $('#exchangeForm').submit(function(e) {
        updateSelectedAddress(); // 提交前再次确保地址ID已设置

        const addressId = $('#selectedAddressId').val();
        if (!addressId) {
            e.preventDefault();
            alert('请选择收货地址');
            return false;
        }

        // 验证数量和积分
        const quantity = parseInt($('#quantity').val()) || 1;
        const totalPoints = pointsCost * quantity;

        if (quantity < 1) {
            e.preventDefault();
            alert('请选择至少1件商品');
            return false;
        }

        if (quantity > maxStock) {
            e.preventDefault();
            alert('库存不足，最多可兑换' + maxStock + '件');
            return false;
        }

        if (totalPoints > userPoints) {
            e.preventDefault();
            alert('积分不足，无法完成兑换');
            return false;
        }

        // 记录到控制台和Sentry用于调试
        console.log('提交表单，地址ID:', addressId, '数量:', quantity);
        if (typeof trackPointsExchangeEvent === 'function') {
            trackPointsExchangeEvent('submit-exchange-form', {
                address_id: addressId,
                product_id: $('input[name="product_id"]').val(),
                quantity: quantity
            });
        }
    });
});
</script>
@endpush
@endsection