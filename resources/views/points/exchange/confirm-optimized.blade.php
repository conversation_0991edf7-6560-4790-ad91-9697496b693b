@extends('layouts.app')

@section('title', '确认兑换 - 积分商城')

@section('styles')
<style>
    /* 京东风格的页面样式 */
    body {
        background-color: #f5f5f5;
    }

    .jd-container {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        margin-bottom: 15px;
        overflow: hidden;
    }

    .jd-header {
        background: linear-gradient(135deg, #e3001b 0%, #ff6b35 100%);
        color: white;
        padding: 15px 20px;
        position: relative;
    }

    .jd-title {
        font-size: 18px;
        font-weight: 600;
        margin: 0;
        display: flex;
        align-items: center;
    }

    .jd-title i {
        margin-right: 8px;
        font-size: 20px;
    }

    .security-badge {
        position: absolute;
        right: 20px;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(255,255,255,0.2);
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        display: flex;
        align-items: center;
    }

    .security-badge i {
        margin-right: 4px;
    }

    .product-showcase {
        padding: 20px;
        border-bottom: 1px solid #f0f0f0;
    }

    .product-main {
        display: flex;
        gap: 20px;
        align-items: flex-start;
    }

    .product-image-wrapper {
        position: relative;
        flex-shrink: 0;
    }

    .product-image {
        width: 120px;
        height: 120px;
        border-radius: 8px;
        overflow: hidden;
        border: 2px solid #f0f0f0;
        transition: all 0.3s ease;
    }

    .product-image:hover {
        border-color: #e3001b;
        transform: scale(1.02);
    }

    .product-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .product-badge {
        position: absolute;
        top: -5px;
        right: -5px;
        background: #ff6b35;
        color: white;
        border-radius: 50%;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        font-weight: bold;
    }

    .product-details {
        flex: 1;
        min-width: 0;
    }

    .product-name {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin-bottom: 8px;
        line-height: 1.4;
    }

    .product-desc {
        color: #666;
        font-size: 14px;
        margin-bottom: 12px;
        line-height: 1.4;
    }

    .product-points-info {
        display: flex;
        align-items: center;
        gap: 15px;
        margin-bottom: 15px;
        flex-wrap: wrap;
    }

    .points-price {
        display: flex;
        align-items: center;
        background: linear-gradient(135deg, #ff6b35, #e3001b);
        color: white;
        padding: 8px 12px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 16px;
    }

    .points-icon {
        width: 18px;
        height: 18px;
        background: #ffd700;
        border-radius: 50%;
        margin-right: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 10px;
        color: #e3001b;
    }

    .stock-info {
        color: #666;
        font-size: 14px;
        background: #f8f8f8;
        padding: 4px 8px;
        border-radius: 12px;
    }

    .quantity-selector {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-top: 10px;
    }

    .quantity-label {
        font-weight: 600;
        color: #333;
        min-width: 60px;
    }

    .quantity-controls {
        display: flex;
        align-items: center;
        border: 1px solid #ddd;
        border-radius: 4px;
        overflow: hidden;
    }

    .quantity-btn {
        width: 32px;
        height: 32px;
        border: none;
        background: #f8f8f8;
        color: #666;
        cursor: pointer;
        transition: all 0.2s;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
    }

    .quantity-btn:hover:not(:disabled) {
        background: #e3001b;
        color: white;
    }

    .quantity-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    .quantity-input {
        width: 60px;
        height: 32px;
        border: none;
        text-align: center;
        font-weight: 600;
        outline: none;
        background: white;
    }

    .address-section {
        padding: 20px;
        border-bottom: 1px solid #f0f0f0;
    }

    .section-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
    }

    .section-title i {
        margin-right: 8px;
        color: #e3001b;
    }

    .address-card {
        border: 2px solid #f0f0f0;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 10px;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
    }

    .address-card:hover {
        border-color: #e3001b;
        box-shadow: 0 2px 8px rgba(227,0,27,0.1);
    }

    .address-card.selected {
        border-color: #e3001b;
        background: linear-gradient(135deg, #fff5f5, #fff0f0);
    }

    .address-card.selected::after {
        content: '✓';
        position: absolute;
        top: 10px;
        right: 15px;
        color: #e3001b;
        font-weight: bold;
        font-size: 18px;
    }

    .address-info {
        display: flex;
        align-items: flex-start;
        gap: 10px;
    }

    .address-radio {
        margin-top: 2px;
    }

    .address-details h6 {
        margin: 0 0 5px 0;
        font-weight: 600;
        color: #333;
    }

    .address-details p {
        margin: 0;
        color: #666;
        font-size: 14px;
        line-height: 1.4;
    }

    .points-summary {
        padding: 20px;
        background: linear-gradient(135deg, #fff8f0, #fff5f0);
        border-top: 3px solid #e3001b;
    }

    .points-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        font-size: 16px;
    }

    .points-row.total {
        font-size: 18px;
        font-weight: 600;
        color: #e3001b;
        border-top: 1px solid #f0f0f0;
        padding-top: 10px;
        margin-top: 10px;
    }

    .points-status {
        text-align: center;
        padding: 10px;
        border-radius: 8px;
        font-weight: 600;
        margin-bottom: 15px;
    }

    .points-status.sufficient {
        background: #f6ffed;
        color: #52c41a;
        border: 1px solid #b7eb8f;
    }

    .points-status.insufficient {
        background: #fff2f0;
        color: #ff4d4f;
        border: 1px solid #ffccc7;
    }

    .action-buttons {
        padding: 20px;
        text-align: center;
        background: #fafafa;
    }

    .btn-exchange {
        background: linear-gradient(135deg, #e3001b, #ff6b35);
        color: white;
        border: none;
        padding: 12px 40px;
        font-size: 16px;
        font-weight: 600;
        border-radius: 25px;
        cursor: pointer;
        transition: all 0.3s ease;
        margin-right: 15px;
        min-width: 120px;
    }

    .btn-exchange:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(227,0,27,0.3);
    }

    .btn-exchange:disabled {
        background: #ccc;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }

    .btn-back {
        background: #f0f0f0;
        color: #666;
        border: 1px solid #ddd;
        padding: 12px 30px;
        font-size: 16px;
        border-radius: 25px;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
    }

    .btn-back:hover {
        background: #e0e0e0;
        color: #333;
        text-decoration: none;
    }

    .help-info {
        margin-top: 15px;
        text-align: center;
        color: #999;
        font-size: 14px;
    }

    .help-info a {
        color: #e3001b;
        text-decoration: none;
    }

    .help-info a:hover {
        text-decoration: underline;
    }

    @media (max-width: 768px) {
        .product-main {
            flex-direction: column;
            gap: 15px;
        }

        .product-image {
            width: 100px;
            height: 100px;
        }

        .quantity-selector {
            flex-wrap: wrap;
        }

        .points-row {
            font-size: 14px;
        }

        .points-row.total {
            font-size: 16px;
        }

        .action-buttons {
            padding: 15px;
        }

        .btn-exchange, .btn-back {
            width: 100%;
            margin: 5px 0;
        }
    }
</style>
@endsection

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8 col-md-10">
            <!-- 页面标题 -->
            <div class="jd-container">
                <div class="jd-header">
                    <h1 class="jd-title">
                        <i class="fas fa-shopping-cart"></i>
                        确认兑换订单
                    </h1>
                    <div class="security-badge">
                        <i class="fas fa-shield-alt"></i>
                        安全保障
                    </div>
                </div>
            </div>

            <!-- 商品信息 -->
            <div class="jd-container">
                <div class="product-showcase">
                    <div class="section-title">
                        <i class="fas fa-box"></i>
                        商品信息
                    </div>
                    <div class="product-main">
                        <div class="product-image-wrapper">
                            <div class="product-image">
                                <img src="{{ $product->cover_image ?: '/images/placeholder.jpg' }}" alt="{{ $product->name }}"
                                     onerror="this.src='/images/placeholder.jpg'">
                            </div>
                            <div class="product-badge">积分</div>
                        </div>
                        <div class="product-details">
                            <h2 class="product-name">{{ $product->name }}</h2>
                            <p class="product-desc">{{ $product->description ?: '精选优质商品，积分兑换超值享受' }}</p>

                            <div class="product-points-info">
                                <div class="points-price">
                                    <div class="points-icon">积</div>
                                    {{ $product->points_cost ?? 0 }} 积分
                                </div>
                                <div class="stock-info">
                                    <i class="fas fa-warehouse"></i>
                                    库存 {{ $product->stock }} 件
                                </div>
                                @if($product->is_hot)
                                <span class="badge bg-danger">热销</span>
                                @endif
                                @if($product->is_new)
                                <span class="badge bg-success">新品</span>
                                @endif
                            </div>

                            <div class="quantity-selector">
                                <span class="quantity-label">购买数量：</span>
                                <div class="quantity-controls">
                                    <button type="button" class="quantity-btn btn-decrease">-</button>
                                    <input type="number" class="quantity-input" id="quantity"
                                           value="{{ $quantity ?? 1 }}" min="1" max="{{ $product->stock }}" readonly>
                                    <button type="button" class="quantity-btn btn-increase">+</button>
                                </div>
                                <span class="text-muted ms-2">最多可购买 {{ $product->stock }} 件</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 收货地址 -->
            <div class="jd-container">
                <div class="address-section">
                    <div class="section-title">
                        <i class="fas fa-map-marker-alt"></i>
                        收货地址
                    </div>
                    @if($addresses->count() > 0)
                        <div class="address-list">
                            @foreach($addresses as $index => $address)
                            <div class="address-card {{ $address->is_default || $index === 0 ? 'selected' : '' }}"
                                 onclick="selectAddress({{ $address->id }}, this)">
                                <div class="address-info">
                                    <input type="radio" name="address_id" value="{{ $address->id }}"
                                           class="address-radio" {{ $address->is_default || $index === 0 ? 'checked' : '' }}>
                                    <div class="address-details">
                                        <h6>{{ $address->contact_name }} {{ $address->contact_phone }}</h6>
                                        <p>{{ $address->full_address }}</p>
                                        @if($address->is_default)
                                        <span class="badge bg-primary">默认地址</span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>
                        <div class="text-center mt-3">
                            <a href="{{ route('user.addresses.create') }}" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-plus"></i> 添加新地址
                            </a>
                        </div>
                    @else
                        <div class="alert alert-warning text-center">
                            <i class="fas fa-exclamation-triangle"></i>
                            您还没有收货地址，请先添加收货地址
                            <br>
                            <a href="{{ route('user.addresses.create') }}" class="btn btn-primary mt-2">
                                <i class="fas fa-plus"></i> 立即添加
                            </a>
                        </div>
                    @endif
                </div>
            </div>

            <!-- 积分结算 -->
            <div class="jd-container">
                <div class="points-summary">
                    <div class="section-title">
                        <i class="fas fa-calculator"></i>
                        积分结算
                    </div>

                    <div class="points-row">
                        <span>当前积分余额：</span>
                        <span class="text-warning fw-bold">{{ number_format($userPoints) }} 积分</span>
                    </div>

                    <div class="points-row">
                        <span>单价：</span>
                        <span>{{ $product->points_cost ?? 0 }} 积分/件</span>
                    </div>

                    <div class="points-row">
                        <span>数量：</span>
                        <span id="display-quantity">{{ $quantity ?? 1 }} 件</span>
                    </div>

                    <div class="points-row total">
                        <span>需要消耗积分：</span>
                        <span id="total-points">{{ ($product->points_cost ?? 0) * ($quantity ?? 1) }} 积分</span>
                    </div>

                    <div class="points-status {{ $canExchange ? 'sufficient' : 'insufficient' }}" id="points-status">
                        <i class="fas {{ $canExchange ? 'fa-check-circle' : 'fa-times-circle' }}"></i>
                        <span id="status-text">{{ $canExchange ? '积分充足，可以兑换' : '积分不足，无法兑换' }}</span>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="jd-container">
                <div class="action-buttons">
                    @if($addresses->count() > 0)
                    <form action="{{ route('points.exchange.process') }}" method="POST" id="exchange-form">
                        @csrf
                        <input type="hidden" name="product_id" value="{{ $product->id }}">
                        <input type="hidden" name="quantity" id="form-quantity" value="{{ $quantity ?? 1 }}">
                        <input type="hidden" name="address_id" id="selected-address-id" value="{{ $addresses->first()->id ?? '' }}">

                        <button type="submit" class="btn-exchange" id="confirm-btn" {{ $canExchange ? '' : 'disabled' }}>
                            <i class="fas fa-check"></i>
                            确认兑换
                        </button>
                    </form>
                    @else
                    <button type="button" class="btn-exchange" disabled>
                        <i class="fas fa-exclamation-triangle"></i>
                        请先添加收货地址
                    </button>
                    @endif

                    <a href="{{ url()->previous() }}" class="btn-back">
                        <i class="fas fa-arrow-left"></i>
                        返回上页
                    </a>

                    <div class="help-info">
                        <i class="fas fa-info-circle"></i>
                        遇到问题？<a href="#" onclick="showCustomerService()">联系客服</a> |
                        <a href="#" onclick="showExchangeRules()">兑换说明</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@push('scripts')
<script>
$(document).ready(function() {
    // 商品和用户数据
    const productData = {
        pointsCost: {{ $product->points_cost ?? 0 }},
        maxStock: {{ $product->stock }},
        userPoints: {{ $userPoints }}
    };

    // DOM 元素
    const $quantityInput = $('#quantity');
    const $decreaseBtn = $('.btn-decrease');
    const $increaseBtn = $('.btn-increase');
    const $totalPoints = $('#total-points');
    const $displayQuantity = $('#display-quantity');
    const $formQuantity = $('#form-quantity');
    const $confirmBtn = $('#confirm-btn');
    const $pointsStatus = $('#points-status');
    const $statusText = $('#status-text');

    // 更新积分计算
    function updatePointsCalculation() {
        const quantity = parseInt($quantityInput.val()) || 1;
        const totalCost = productData.pointsCost * quantity;

        // 更新显示
        $totalPoints.text(totalCost.toLocaleString() + ' 积分');
        $displayQuantity.text(quantity + ' 件');
        $formQuantity.val(quantity);

        // 检查积分是否足够
        const canExchange = productData.userPoints >= totalCost;

        // 更新按钮状态
        $confirmBtn.prop('disabled', !canExchange);

        // 更新状态显示
        if (canExchange) {
            $pointsStatus.removeClass('insufficient').addClass('sufficient');
            $pointsStatus.find('i').removeClass('fa-times-circle').addClass('fa-check-circle');
            $statusText.text('积分充足，可以兑换');
        } else {
            $pointsStatus.removeClass('sufficient').addClass('insufficient');
            $pointsStatus.find('i').removeClass('fa-check-circle').addClass('fa-times-circle');
            $statusText.text('积分不足，无法兑换');
        }

        // 更新数量按钮状态
        $decreaseBtn.prop('disabled', quantity <= 1);
        $increaseBtn.prop('disabled', quantity >= productData.maxStock);
    }

    // 数量减少
    $decreaseBtn.click(function() {
        const currentValue = parseInt($quantityInput.val()) || 1;
        if (currentValue > 1) {
            $quantityInput.val(currentValue - 1);
            updatePointsCalculation();
        }
    });

    // 数量增加
    $increaseBtn.click(function() {
        const currentValue = parseInt($quantityInput.val()) || 1;
        if (currentValue < productData.maxStock) {
            $quantityInput.val(currentValue + 1);
            updatePointsCalculation();
        } else {
            showToast('已达到最大库存数量：' + productData.maxStock + ' 件', 'warning');
        }
    });

    // 数量输入框变化
    $quantityInput.on('input change', function() {
        let value = parseInt($(this).val()) || 1;

        if (value < 1) {
            value = 1;
            $(this).val(value);
        } else if (value > productData.maxStock) {
            value = productData.maxStock;
            $(this).val(value);
            showToast('已达到最大库存数量：' + productData.maxStock + ' 件', 'warning');
        }

        updatePointsCalculation();
    });

    // 初始化计算
    updatePointsCalculation();

    // 表单提交验证
    $('#exchange-form').submit(function(e) {
        const addressId = $('#selected-address-id').val();
        const quantity = parseInt($quantityInput.val()) || 1;
        const totalCost = productData.pointsCost * quantity;

        // 验证地址
        if (!addressId) {
            e.preventDefault();
            showToast('请选择收货地址', 'error');
            return false;
        }

        // 验证数量
        if (quantity < 1 || quantity > productData.maxStock) {
            e.preventDefault();
            showToast('商品数量无效', 'error');
            return false;
        }

        // 验证积分
        if (totalCost > productData.userPoints) {
            e.preventDefault();
            showToast('积分不足，无法完成兑换', 'error');
            return false;
        }

        // 显示加载状态
        $confirmBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 处理中...');

        // 记录事件
        console.log('提交兑换订单', {
            productId: $('input[name="product_id"]').val(),
            quantity: quantity,
            addressId: addressId,
            totalCost: totalCost
        });
    });
});

// 选择地址
function selectAddress(addressId, element) {
    // 移除所有选中状态
    $('.address-card').removeClass('selected');
    $('.address-radio').prop('checked', false);

    // 设置当前选中
    $(element).addClass('selected');
    $(element).find('.address-radio').prop('checked', true);
    $('#selected-address-id').val(addressId);

    console.log('选择地址:', addressId);
}

// 显示客服
function showCustomerService() {
    showToast('客服功能开发中，如有问题请联系管理员', 'info');
}

// 显示兑换规则
function showExchangeRules() {
    const rules = `
        <div class="exchange-rules">
            <h6><i class="fas fa-info-circle text-primary"></i> 积分兑换说明</h6>
            <ul class="list-unstyled">
                <li><i class="fas fa-check text-success"></i> 积分兑换商品为虚拟交易，一经兑换不可退换</li>
                <li><i class="fas fa-check text-success"></i> 兑换成功后，积分将立即扣除</li>
                <li><i class="fas fa-check text-success"></i> 商品将在1-3个工作日内发货</li>
                <li><i class="fas fa-check text-success"></i> 请确保收货地址准确无误</li>
                <li><i class="fas fa-check text-success"></i> 如有疑问，请及时联系客服</li>
            </ul>
        </div>
    `;

    // 这里可以用模态框显示，暂时用alert
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = rules;
    alert(tempDiv.textContent || tempDiv.innerText);
}

// 通用提示函数
function showToast(message, type = 'info') {
    const toastClass = {
        'success': 'alert-success',
        'error': 'alert-danger',
        'warning': 'alert-warning',
        'info': 'alert-info'
    }[type] || 'alert-info';

    const toast = $(`
        <div class="alert ${toastClass} alert-dismissible fade show position-fixed"
             style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
            <i class="fas fa-${type === 'error' ? 'times' : type === 'success' ? 'check' : type === 'warning' ? 'exclamation-triangle' : 'info'}-circle me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `);

    $('body').append(toast);

    // 3秒后自动消失
    setTimeout(() => {
        toast.alert('close');
    }, 3000);
}
</script>
@endpush

@endsection