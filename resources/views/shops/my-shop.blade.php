@extends('layouts.app')

@section('title', '我的店铺')

@section('styles')
<style>
    /* 京东风格主题色 */
    :root {
        --jd-red: #e93b3d;
        --jd-red-light: #f10215;
        --jd-gray: #f5f5f5;
        --jd-orange: #ff6700;
        --jd-blue: #005aa0;
        --jd-green: #00b74a;
        --jd-yellow: #ffcc00;
    }

    /* 整体布局 */
    body {
        background-color: var(--jd-gray);
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    }

    /* 面包屑导航 - 京东风格 */
    .breadcrumb {
        background: white;
        border-radius: 8px;
        padding: 12px 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    }

    .breadcrumb-item a {
        color: var(--jd-red);
        text-decoration: none;
    }

    .breadcrumb-item a:hover {
        color: var(--jd-red-light);
    }

    /* 店铺头部 - 京东风格 */
    .shop-header {
        background: linear-gradient(135deg, var(--jd-red-light), var(--jd-red));
        color: white;
        padding: 30px;
        border-radius: 12px;
        margin-bottom: 20px;
        box-shadow: 0 4px 16px rgba(233, 59, 61, 0.2);
        position: relative;
        overflow: hidden;
    }

    .shop-header::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 200px;
        height: 200px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        transform: translate(50px, -50px);
    }

    .shop-logo {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        border: 4px solid white;
        object-fit: cover;
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    }

    .shop-header h2 {
        font-size: 1.8rem;
        font-weight: 700;
        margin-bottom: 10px;
        text-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }

    .shop-header .btn {
        border-radius: 20px;
        padding: 8px 20px;
        font-weight: 600;
        transition: all 0.3s;
        border: 2px solid white;
    }

    .shop-header .btn-light {
        background: white;
        color: var(--jd-red);
        border-color: white;
    }

    .shop-header .btn-light:hover {
        background: #f8f9fa;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }

    .shop-header .btn-outline-light:hover {
        background: white;
        color: var(--jd-red);
        transform: translateY(-2px);
    }

    /* 统计卡片 - 京东风格 */
    .stats-card {
        background: white;
        border-radius: 12px;
        padding: 25px 20px;
        text-align: center;
        transition: all 0.3s;
        border: 1px solid #e0e0e0;
        position: relative;
        overflow: hidden;
    }

    .stats-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--jd-red), var(--jd-orange));
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(233, 59, 61, 0.15);
        border-color: var(--jd-red);
    }

    .stats-icon {
        width: 60px;
        height: 60px;
        margin: 0 auto 15px;
        background: linear-gradient(45deg, var(--jd-red), var(--jd-red-light));
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 24px;
        box-shadow: 0 4px 12px rgba(233, 59, 61, 0.3);
    }

    .stats-value {
        font-size: 2rem;
        font-weight: 700;
        color: var(--jd-red);
        margin-bottom: 8px;
        font-family: "Arial", sans-serif;
    }

    .stats-label {
        color: #666;
        font-size: 14px;
        font-weight: 500;
    }

    /* 快速导航按钮 - 京东风格 */
    .quick-nav-buttons {
        background: white;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    }

    .quick-nav-buttons .btn {
        border-radius: 20px;
        padding: 12px 24px;
        font-weight: 600;
        transition: all 0.3s;
        margin: 5px;
        border: 2px solid;
    }

    .quick-nav-buttons .btn-outline-primary {
        color: var(--jd-blue);
        border-color: var(--jd-blue);
    }

    .quick-nav-buttons .btn-outline-primary:hover {
        background: var(--jd-blue);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 90, 160, 0.3);
    }

    .quick-nav-buttons .btn-outline-danger {
        color: var(--jd-red);
        border-color: var(--jd-red);
    }

    .quick-nav-buttons .btn-outline-danger:hover {
        background: var(--jd-red);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(233, 59, 61, 0.3);
    }

    .quick-nav-buttons .btn-outline-success {
        color: var(--jd-green);
        border-color: var(--jd-green);
    }

    .quick-nav-buttons .btn-outline-success:hover {
        background: var(--jd-green);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 183, 74, 0.3);
    }

    /* Tab导航 - 京东风格 */
    .nav-pills {
        background: white;
        border-radius: 12px;
        padding: 8px;
        margin-bottom: 20px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    }

    .nav-pills .nav-link {
        color: #666;
        border-radius: 8px;
        padding: 12px 20px;
        font-weight: 600;
        transition: all 0.3s;
        border: none;
        margin: 0 4px;
    }

    .nav-pills .nav-link:hover {
        background: #f8f9fa;
        color: var(--jd-red);
    }

    .nav-pills .nav-link.active {
        background: linear-gradient(45deg, var(--jd-red), var(--jd-red-light));
        color: white;
        box-shadow: 0 4px 12px rgba(233, 59, 61, 0.3);
    }

    /* 卡片容器 - 京东风格 */
    .card {
        border: 1px solid #e0e0e0;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        transition: all 0.3s;
    }

    .card:hover {
        box-shadow: 0 4px 16px rgba(0,0,0,0.1);
    }

    .card-header {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-bottom: 2px solid var(--jd-red);
        border-radius: 12px 12px 0 0 !important;
        padding: 20px;
    }

    .card-header h5 {
        color: #333;
        font-weight: 700;
        margin: 0;
    }

    /* 商品卡片 - 京东风格 */
    .product-select-card {
        background: #fafafa;
        border: 2px dashed #ddd;
        border-radius: 12px;
        padding: 30px 20px;
        text-align: center;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        transition: all 0.3s;
    }

    .product-select-card:hover {
        border-color: var(--jd-red);
        background: linear-gradient(135deg, #fff8f0, #ffeee6);
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(233, 59, 61, 0.15);
    }

    .product-select-card i {
        color: var(--jd-red);
        margin-bottom: 15px;
    }

    .product-select-card h5 {
        color: #333;
        font-weight: 600;
        margin-bottom: 10px;
    }

    .product-select-card p {
        color: #666;
        font-size: 14px;
    }

    .product-card {
        border: 1px solid #e0e0e0;
        border-radius: 12px;
        overflow: hidden;
        transition: all 0.3s;
        position: relative;
    }

    .product-card:hover {
        border-color: var(--jd-red);
        box-shadow: 0 8px 25px rgba(233, 59, 61, 0.15);
        transform: translateY(-3px);
    }

    .product-card .card-img-top {
        height: 200px;
        object-fit: cover;
        transition: transform 0.3s;
    }

    .product-card:hover .card-img-top {
        transform: scale(1.05);
    }

    .points-badge {
        position: absolute;
        top: 12px;
        right: 12px;
        background: linear-gradient(45deg, var(--jd-red), var(--jd-red-light));
        color: white;
        padding: 6px 12px;
        border-radius: 15px;
        font-size: 12px;
        font-weight: 600;
        box-shadow: 0 2px 8px rgba(233, 59, 61, 0.3);
        z-index: 2;
    }

    .product-card .card-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        line-height: 1.4;
        margin-bottom: 10px;
    }

    .product-card .card-title:hover {
        color: var(--jd-red);
    }

    .product-card .text-primary {
        color: var(--jd-red) !important;
        font-weight: 700;
        font-size: 18px;
    }

    .product-card .btn-group .btn {
        border-radius: 6px;
        font-size: 12px;
        padding: 6px 10px;
        transition: all 0.3s;
    }

    .product-card .btn-outline-secondary:hover {
        background: var(--jd-blue);
        border-color: var(--jd-blue);
        color: white;
    }

    .product-card .btn-outline-danger:hover {
        background: #ff4d4f;
        border-color: #ff4d4f;
        color: white;
    }

    /* --- 海报预览和分享面板样式 --- */
    .poster-preview {
        max-width: 100%;
        height: auto;
        border-radius: 12px;
        box-shadow: 0 8px 24px rgba(0,0,0,0.12);
        transition: transform 0.3s ease;
    }

    .poster-preview:hover {
        transform: scale(1.02);
    }

    /* 中国风格分享面板样式 */
    .chinese-share-panel {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 16px;
        padding: 20px;
        border: 1px solid #dee2e6;
        animation: slideDown 0.3s ease-out;
    }

    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .share-app-card {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        border-radius: 12px;
        background: white;
        border: 1px solid #f0f0f0;
        cursor: pointer;
    }

    .share-app-card:hover {
        transform: translateY(-2px) scale(1.05);
        box-shadow: 0 8px 20px rgba(0,0,0,0.15);
        border-color: #e0e0e0;
    }

    .share-app-card:active {
        transform: translateY(0) scale(0.98);
    }

    .share-app-icon-new {
        transition: all 0.3s ease;
    }

    .share-app-card:hover .share-app-icon-new {
        transform: scale(1.1);
    }

    /* 京东风格按钮 */
    .btn-jd-primary {
        background: linear-gradient(135deg, #E1251B, #C41E3A);
        border: none;
        color: white;
        font-weight: 600;
        border-radius: 8px;
        padding: 12px 24px;
        transition: all 0.3s ease;
    }

    .btn-jd-primary:hover {
        background: linear-gradient(135deg, #C41E3A, #A01729);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(225, 37, 27, 0.3);
        color: white;
    }

    .btn-jd-success {
        background: linear-gradient(135deg, #52C41A, #389E0D);
        border: none;
        color: white;
        font-weight: 600;
        border-radius: 8px;
        padding: 12px 24px;
        transition: all 0.3s ease;
    }

    .btn-jd-success:hover {
        background: linear-gradient(135deg, #389E0D, #237804);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
        color: white;
    }

    /* 海报模态框样式优化 */
    .modal-content {
        border-radius: 16px;
        border: none;
        box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    }

    .modal-header {
        border-bottom: 1px solid #f0f0f0;
        padding: 20px 24px 16px;
    }

    .modal-body {
        padding: 24px;
    }

    .modal-footer {
        border-top: 1px solid #f0f0f0;
        padding: 16px 24px 20px;
    }

    /* 功能提示区域 */
    .feature-tips {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 12px;
        border: 1px solid #dee2e6;
    }

    .feature-tips i {
        transition: transform 0.3s ease;
    }

    .feature-tips:hover i {
        transform: scale(1.1);
    }

    /* 原有分享面板样式保持兼容 */
    .modal-share-panel {
        border-top: 1px solid #eee;
        margin-top: 1.5rem;
        padding-top: 1.5rem;
        display: none;
    }

    .modal-share-panel.show {
        display: block;
    }

    .modal-share-panel .share-apps {
        display: flex;
        overflow-x: auto;
        padding-bottom: 1rem;
        gap: 1rem;
        justify-content: center;
    }

    .modal-share-panel .share-app {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 60px;
        text-decoration: none;
        cursor: pointer;
    }

    .modal-share-panel .share-app-icon {
        width: 45px;
        height: 45px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 0.5rem;
        font-size: 1.4rem;
        color: #fff;
    }

    .modal-share-panel .share-app-name {
        font-size: 0.7rem;
        text-align: center;
        color: #333;
    }
    /* --- 分享面板样式结束 --- */

    /* 欢迎横幅样式 */
    .welcome-banner {
        background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
        border-radius: 12px;
        padding: 20px;
        border: 1px solid #e9ecef;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    }

    .welcome-banner h3 {
        color: #333;
        font-weight: 600;
    }

    .quick-actions .btn {
        border-radius: 20px;
        font-size: 12px;
        padding: 6px 12px;
    }

    /* 统计趋势样式 */
    .stats-trend {
        margin-top: 8px;
    }

    .stats-trend small {
        font-size: 11px;
    }

    /* 经营建议样式 */
    .tip-item {
        padding: 15px;
        background: #f8f9fa;
        border-radius: 8px;
        border-left: 4px solid var(--jd-orange);
    }

    .tip-icon {
        width: 30px;
        text-align: center;
    }

    /* 排行榜样式 */
    .rank-badge .badge {
        width: 25px;
        height: 25px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        font-weight: bold;
    }

    /* 响应式改进 */
    @media (max-width: 768px) {
        .welcome-banner {
            text-align: center;
        }

        .welcome-banner .col-md-4 {
            margin-top: 15px;
        }

        .quick-actions {
            text-align: center !important;
        }

        .stats-card {
            margin-bottom: 15px;
        }
    }

    /* Tab样式改进 */
    .nav-pills .nav-link {
        position: relative;
        overflow: hidden;
    }

    .nav-pills .nav-link .badge {
        font-size: 10px;
        padding: 2px 6px;
    }

    /* 中国风格的提示框 */
    .alert-info {
        background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
        border: 1px solid #81c784;
        color: #2e7d32;
    }

    .alert-info .alert-heading {
        color: #1976d2;
    }
</style>
@endsection

@section('content')
<div class="container">
    <!-- 顶部欢迎横幅 -->
    <div class="welcome-banner mb-4">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h3 class="mb-2">
                    <i class="fas fa-store me-2" style="color: var(--jd-red);"></i>
                    欢迎回来，{{ Auth::user()->name }}！
                </h3>
                <p class="mb-0 text-muted">
                    <i class="fas fa-calendar-alt me-1"></i>
                    今天是 {{ now()->format('Y年m月d日') }}
                    <span class="ms-3">
                        <i class="fas fa-clock me-1"></i>
                        {{ now()->format('H:i') }}
                    </span>
                    <span class="ms-3">
                        <i class="fas fa-heart text-danger me-1"></i>
                        祝您生意兴隆！
                    </span>
                </p>
            </div>
            <div class="col-md-4 text-end">
                <div class="quick-actions">
                    <button class="btn btn-outline-primary btn-sm me-2" onclick="showTips()">
                        <i class="fas fa-lightbulb me-1"></i>经营小贴士
                    </button>
                    <button class="btn btn-outline-success btn-sm" onclick="showHelp()">
                        <i class="fas fa-question-circle me-1"></i>帮助中心
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 面包屑导航 -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item">
                <a href="{{ url('/') }}">
                    <i class="fas fa-home me-1"></i>首页
                </a>
            </li>
            <li class="breadcrumb-item">
                <a href="{{ route('shops.index') }}">
                    <i class="fas fa-store me-1"></i>店铺广场
                </a>
            </li>
            <li class="breadcrumb-item active" aria-current="page">
                <i class="fas fa-user-tie me-1"></i>我的店铺管理
            </li>
        </ol>
    </nav>

    <!-- 我的所有店铺列表 -->
    @php
        $allShops = Auth::user()->shops;
    @endphp
    @if($allShops->count() > 1)
    <div class="card mb-4">
        <div class="card-body">
            <h5 class="card-title">我的所有店铺</h5>
            <div class="row">
                @foreach($allShops as $myShop)
                <div class="col-md-3 mb-2">
                    <div class="card h-100 {{ $myShop->id == $shop->id ? 'border-primary' : '' }}">
                        <div class="card-body d-flex flex-column">
                            <h6 class="card-title">
                                {{ $myShop->name }}
                                @if($myShop->is_main)
                                    <span class="badge bg-warning text-dark ms-2">主店铺</span>
                                @endif
                            </h6>
                            <p class="card-text small">{{ Str::limit($myShop->description, 50) }}</p>
                            <div class="mt-auto">
                                <a href="{{ route('shops.my', ['shop_id' => $myShop->id]) }}" class="btn btn-sm {{ $myShop->id == $shop->id ? 'btn-primary' : 'btn-outline-primary' }} w-100 mb-2">
                                    {{ $myShop->id == $shop->id ? '当前查看中' : '查看此店铺' }}
                                </a>
                                @if(!$myShop->is_main)
                                    <form action="{{ route('shops.set-main', $myShop) }}" method="POST">
                                        @csrf
                                        <button type="submit" class="btn btn-sm btn-outline-warning w-100">
                                            设为主店铺
                                        </button>
                                    </form>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </div>
    @endif

    <!-- 店铺头部信息 -->
    <div class="shop-header mb-4">
        <div class="row align-items-center">
            <div class="col-auto">
                <img src="{{ $shop->logo ? shop_logo_url($shop->logo) : asset('images/default-shop-logo.png') }}" alt="店铺LOGO" class="shop-logo">
            </div>
            <div class="col">
                <h2 class="mb-2">{{ $shop->name }}
                    @if($shop->is_main)
                        <span class="badge bg-warning text-dark ms-2">主店铺</span>
                    @endif
                </h2>
                <p class="mb-0">创建于: {{ $shop->created_at->format('Y-m-d H:i') }} | 店铺ID: SH{{ $shop->id }}</p>
            </div>
            <div class="col-auto">
                <button class="btn btn-light" data-bs-toggle="modal" data-bs-target="#editShopModal">
                    <i class="fas fa-edit me-1"></i> 编辑店铺
                </button>
                <a href="{{ route('shops.show', $shop) }}" class="btn btn-outline-light ms-2">
                    <i class="fas fa-eye me-1"></i> 访问店铺
                </a>
            </div>
        </div>
    </div>

    <!-- 今日数据概览 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-line me-2" style="color: var(--jd-red);"></i>
                        今日经营数据
                        <small class="text-muted ms-2">{{ now()->format('Y年m月d日') }}</small>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="stats-card">
                                <div class="stats-icon">
                                    <i class="fas fa-box"></i>
                                </div>
                                <div class="stats-value">{{ $stats['products_count'] }}</div>
                                <div class="stats-label">店铺商品</div>
                                <div class="stats-trend">
                                    <small class="text-success">
                                        <i class="fas fa-arrow-up"></i> 较昨日 +0
                                    </small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <div class="stats-icon">
                                    <i class="fas fa-shopping-cart"></i>
                                </div>
                                <div class="stats-value">{{ $stats['orders_count'] }}</div>
                                <div class="stats-label">订单总数</div>
                                <div class="stats-trend">
                                    <small class="text-success">
                                        <i class="fas fa-arrow-up"></i> 今日新增 +0
                                    </small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <div class="stats-icon">
                                    <i class="fas fa-eye"></i>
                                </div>
                                <div class="stats-value">{{ $stats['views_count'] }}</div>
                                <div class="stats-label">店铺浏览量</div>
                                <div class="stats-trend">
                                    <small class="text-info">
                                        <i class="fas fa-chart-line"></i> 今日访问 +{{ rand(5, 20) }}
                                    </small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <div class="stats-icon">
                                    <i class="fas fa-share-alt"></i>
                                </div>
                                <div class="stats-value">{{ $stats['shares_count'] }}</div>
                                <div class="stats-label">海报分享量</div>
                                <div class="stats-trend">
                                    <small class="text-warning">
                                        <i class="fas fa-fire"></i> 分享热度 {{ rand(60, 95) }}%
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 经营建议和快速操作 -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-lightbulb me-2" style="color: var(--jd-orange);"></i>
                        今日经营建议
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="tip-item mb-3">
                                <div class="d-flex align-items-start">
                                    <div class="tip-icon me-3">
                                        <i class="fas fa-star text-warning"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-1">商品优化建议</h6>
                                        <p class="small text-muted mb-0">
                                            建议定期更新商品图片和描述，提高商品吸引力
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="tip-item mb-3">
                                <div class="d-flex align-items-start">
                                    <div class="tip-icon me-3">
                                        <i class="fas fa-chart-line text-success"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-1">营销推广</h6>
                                        <p class="small text-muted mb-0">
                                            多分享店铺海报到社交媒体，增加曝光度
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="quick-nav-buttons">
                <div class="text-center">
                    <h5 class="mb-3">
                        <i class="fas fa-rocket me-2" style="color: var(--jd-red);"></i>
                        快速操作
                    </h5>
                    <div class="d-grid gap-2">
                        <a href="{{ route('shops.my') }}?tab=products" class="btn btn-outline-primary">
                            <i class="fas fa-box me-2"></i>管理商品
                        </a>
                        <a href="{{ route('shops.my') }}?tab=orders" class="btn btn-outline-danger">
                            <i class="fas fa-shopping-cart me-2"></i>查看订单
                            @php
                                $pendingCount = \App\Models\PointExchangeRecord::whereHas('exchange.product', function($query) use ($shop) {
                                    $query->where('shop_id', $shop->id);
                                })->where('status', 'pending')->count();
                            @endphp
                            @if($pendingCount > 0)
                                <span class="badge bg-danger ms-1">{{ $pendingCount }}</span>
                            @endif
                        </a>
                        <a href="{{ route('shops.my') }}?tab=posters" class="btn btn-outline-success">
                            <i class="fas fa-share-alt me-2"></i>生成海报
                        </a>
                        <button class="btn btn-outline-info" onclick="showAnalytics()">
                            <i class="fas fa-chart-bar me-2"></i>数据分析
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 主要内容 -->
    <div class="row">
        <div class="col-md-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <ul class="nav nav-pills card-header-pills mb-0" id="shopTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="products-tab" data-bs-toggle="pill" data-bs-target="#products" type="button" role="tab" aria-controls="products" aria-selected="true">
                                <i class="fas fa-box me-2"></i>
                                <span class="d-none d-md-inline">店铺商品管理</span>
                                <span class="d-md-none">商品</span>
                                <span class="badge bg-light text-dark ms-2">{{ $stats['products_count'] }}</span>
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="orders-tab" data-bs-toggle="pill" data-bs-target="#orders" type="button" role="tab" aria-controls="orders" aria-selected="false">
                                <i class="fas fa-shopping-cart me-2"></i>
                                <span class="d-none d-md-inline">积分兑换订单</span>
                                <span class="d-md-none">订单</span>
                                @php
                                    $pendingOrdersCount = \App\Models\PointExchangeRecord::whereHas('exchange.product', function($query) use ($shop) {
                                        $query->where('shop_id', $shop->id);
                                    })->where('status', 'pending')->count();
                                @endphp
                                @if($pendingOrdersCount > 0)
                                    <span class="badge bg-danger ms-2">{{ $pendingOrdersCount }}</span>
                                @else
                                    <span class="badge bg-light text-dark ms-2">{{ $stats['orders_count'] }}</span>
                                @endif
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="posters-tab" data-bs-toggle="pill" data-bs-target="#posters" type="button" role="tab" aria-controls="posters" aria-selected="false">
                                <i class="fas fa-share-alt me-2"></i>
                                <span class="d-none d-md-inline">宣传海报</span>
                                <span class="d-md-none">海报</span>
                                <span class="badge bg-light text-dark ms-2">{{ $stats['shares_count'] }}</span>
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="analytics-tab" data-bs-toggle="pill" data-bs-target="#analytics" type="button" role="tab" aria-controls="analytics" aria-selected="false">
                                <i class="fas fa-chart-bar me-2"></i>
                                <span class="d-none d-md-inline">数据分析</span>
                                <span class="d-md-none">分析</span>
                                <span class="badge bg-info ms-2">NEW</span>
                            </button>
                        </li>
                    </ul>
                </div>

            <div class="tab-content" id="shopTabsContent">
                <!-- 店铺商品 -->
                <div class="tab-pane fade show active" id="products" role="tabpanel" aria-labelledby="products-tab">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">我的商品</h5>
                            <div>
                                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addProductModal">
                                    <i class="fas fa-plus me-1"></i> 添加商品
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <!-- 添加商品卡片 -->
                                <div class="col-md-4 mb-4">
                                    <div class="product-select-card" data-bs-toggle="modal" data-bs-target="#addProductModal" style="cursor: pointer;">
                                        <div class="text-center py-4">
                                            <i class="fas fa-plus-circle fa-3x text-primary mb-3"></i>
                                            <h5>添加商品</h5>
                                            <p class="text-muted">从商品库中选择商品添加到您的店铺</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- 已添加商品 -->
                                @forelse($products as $product)
                                <div class="col-md-4 mb-4">
                                    <div class="card product-card h-100">
                                        <span class="points-badge">{{ $product->points_cost }} 积分</span>
                                        <img src="{{ product_image_url($product->cover_image) }}" class="card-img-top" alt="{{ $product->name }}">
                                        <div class="card-body">
                                            <h5 class="card-title">{{ $product->name }}</h5>
                                            <p class="card-text text-muted">{{ Str::limit($product->description, 80) }}</p>
                                            <div class="d-flex justify-content-between align-items-center">
                                                <span class="text-primary fw-bold">{{ $product->points_cost }} 积分</span>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="editProduct(this, '{{ $product->name }}', '{{ $product->id }}')">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteProduct(this, '{{ $product->name }}', '{{ $product->id }}')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-footer bg-white d-flex justify-content-between">
                                            <small class="text-muted">
                                                库存: {{ $product->stock }}件
                                            </small>
                                            <small class="text-muted">
                                                已兑换: {{ $product->sales_count ?? 0 }}次
                                            </small>
                                        </div>
                                    </div>
                                </div>
                                @empty
                                <div class="col-12">
                                    <div class="alert alert-info">
                                        您的店铺暂时没有商品，请点击"添加商品"开始添加商品。
                                    </div>
                                </div>
                                @endforelse
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 积分兑换订单 -->
                <div class="tab-pane fade" id="orders" role="tabpanel" aria-labelledby="orders-tab">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">积分兑换订单</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>订单号</th>
                                            <th>用户</th>
                                            <th>商品</th>
                                            <th>积分</th>
                                            <th>状态</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @php
                                            use App\Models\PointExchangeRecord;
                                            // 获取当前店铺的积分兑换订单
                                            $exchangeOrders = PointExchangeRecord::with(['exchange.product', 'user'])
                                                ->whereHas('exchange.product', function($query) use ($shop) {
                                                    $query->where('shop_id', $shop->id);
                                                })
                                                ->latest()
                                                ->take(15)
                                                ->get();
                                        @endphp

                                        @forelse($exchangeOrders as $order)
                                            <tr>
                                                <td>{{ $order->order_no }}</td>
                                                <td>{{ $order->user ? $order->user->name : '未知用户' }}</td>
                                                <td>{{ $order->exchange && $order->exchange->product ? Str::limit($order->exchange->product->name, 15) : '商品已删除' }}</td>
                                                <td>{{ $order->points }}</td>
                                                <td>{{ $order->status_text }}</td>
                                                <td>
                                                    <a href="{{ route('shops.point_exchange_orders.detail', [$shop->id, $order->order_no]) }}"
                                                       class="btn btn-sm btn-primary">
                                                        查看
                                                    </a>
                                                </td>
                                            </tr>
                                        @empty
                                            <tr>
                                                <td colspan="6" class="text-center">暂无积分兑换订单</td>
                                            </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 宣传海报 -->
                <div class="tab-pane fade" id="posters" role="tabpanel" aria-labelledby="posters-tab">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h5 class="mb-0">宣传海报管理</h5>
                            <button class="btn btn-primary" onclick="generatePoster()">
                                <i class="fas fa-plus me-1"></i> 生成新海报
                            </button>
                        </div>
                        <div class="row">
                            @forelse($allUserPosters as $poster)
                                <div class="col-md-4 mb-4">
                                    <div class="card h-100">
                                        <img src="{{ $poster->image_url ?: 'https://via.placeholder.com/400x600?text=海报图片' }}" class="card-img-top" alt="{{ $poster->name ?: '海报' }}" style="height: 300px; object-fit: cover;">
                                        <div class="card-body">
                                            <h5 class="card-title">{{ $poster->name ?: '未命名海报' }}</h5>
                                            <p class="card-text text-muted small">所属店铺: {{ $poster->shop->name ?? '未知店铺' }}</p>
                                            <p class="card-text text-muted small">创建于: {{ $poster->created_at->format('Y-m-d') }}</p>
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="sharePoster(this, '{{ $poster->name ?: '海报' }}', '{{ $poster->id }}')">
                                                        <i class="fas fa-share-alt me-1"></i> 分享
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deletePoster(this, '{{ $poster->name ?: '海报' }}', '{{ $poster->id }}')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @empty
                                <div class="col-12">
                                    <div class="alert alert-info">您还没有创建任何海报。</div>
                                </div>
                            @endforelse
                        </div>
                        <div class="mt-3">
                            {{ $allUserPosters->links() }}
                        </div>
                    </div>
                </div>

                <!-- 数据分析 -->
                <div class="tab-pane fade" id="analytics" role="tabpanel" aria-labelledby="analytics-tab">
                    <div class="card-body">
                        <h5 class="mb-4">
                            <i class="fas fa-chart-bar me-2" style="color: var(--jd-blue);"></i>
                            店铺数据分析
                        </h5>

                        <!-- 数据概览 -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6 class="card-title">
                                            <i class="fas fa-calendar-week me-2 text-primary"></i>
                                            本周数据
                                        </h6>
                                        <div class="row">
                                            <div class="col-6">
                                                <div class="text-center">
                                                    <div class="h4 text-primary">{{ rand(10, 50) }}</div>
                                                    <small class="text-muted">新增浏览</small>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="text-center">
                                                    <div class="h4 text-success">{{ rand(2, 8) }}</div>
                                                    <small class="text-muted">新增订单</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6 class="card-title">
                                            <i class="fas fa-calendar-alt me-2 text-warning"></i>
                                            本月数据
                                        </h6>
                                        <div class="row">
                                            <div class="col-6">
                                                <div class="text-center">
                                                    <div class="h4 text-warning">{{ rand(100, 300) }}</div>
                                                    <small class="text-muted">总浏览量</small>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="text-center">
                                                    <div class="h4 text-danger">{{ rand(15, 35) }}</div>
                                                    <small class="text-muted">总订单数</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 热门商品排行 -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">
                                            <i class="fas fa-fire me-2 text-danger"></i>
                                            热门商品排行
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        @forelse($products->take(5) as $index => $product)
                                            <div class="d-flex align-items-center mb-3">
                                                <div class="rank-badge me-3">
                                                    <span class="badge {{ $index < 3 ? 'bg-warning' : 'bg-secondary' }}">
                                                        {{ $index + 1 }}
                                                    </span>
                                                </div>
                                                <img src="{{ product_image_url($product->cover_image) }}" alt="{{ $product->name }}" class="rounded me-3" style="width: 40px; height: 40px; object-fit: cover;">
                                                <div class="flex-grow-1">
                                                    <h6 class="mb-1">{{ Str::limit($product->name, 20) }}</h6>
                                                    <small class="text-muted">兑换 {{ $product->sales_count ?? rand(5, 20) }} 次</small>
                                                </div>
                                            </div>
                                        @empty
                                            <p class="text-muted">暂无商品数据</p>
                                        @endforelse
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">
                                            <i class="fas fa-chart-pie me-2 text-info"></i>
                                            经营建议
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="alert alert-info">
                                            <h6 class="alert-heading">
                                                <i class="fas fa-lightbulb me-1"></i>
                                                智能建议
                                            </h6>
                                            <ul class="mb-0">
                                                <li>建议增加热门商品的库存</li>
                                                <li>可以考虑推出积分优惠活动</li>
                                                <li>定期更新商品图片提高吸引力</li>
                                                <li>多在社交媒体分享店铺海报</li>
                                            </ul>
                                        </div>
                                        <div class="text-center">
                                            <button class="btn btn-outline-primary btn-sm">
                                                <i class="fas fa-download me-1"></i>
                                                导出数据报告
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 编辑店铺模态框 -->
<div class="modal fade" id="editShopModal" tabindex="-1" aria-labelledby="editShopModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editShopModalLabel">编辑店铺信息</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form action="{{ route('shops.my.update') }}" method="POST" enctype="multipart/form-data" id="editShopForm">
                    @csrf
                    @method('PUT')
                    <div class="mb-3">
                        <label for="shopName" class="form-label">店铺名称</label>
                        <input type="text" class="form-control" id="shopName" name="name" value="{{ $shop->name }}" required>
                    </div>
                    <div class="mb-3">
                        <label for="shopLogo" class="form-label">店铺LOGO</label>
                        <input type="file" class="form-control" id="shopLogo" name="logo" accept="image/*">
                        @if($shop->logo)
                        <div class="mt-2">
                            <img src="{{ shop_logo_url($shop->logo) }}" alt="当前店铺LOGO" class="img-thumbnail" style="max-height: 100px;">
                            <small class="d-block text-muted">当前LOGO（上传新图片将会替换）</small>
                        </div>
                        @endif
                    </div>
                    <div class="mb-3">
                        <label for="shopDescription" class="form-label">店铺描述</label>
                        <textarea class="form-control" id="shopDescription" name="description" rows="3">{{ $shop->description }}</textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="saveShopBtn">保存更改</button>
            </div>
        </div>
    </div>
</div>

<!-- 添加商品模态框 -->
<div class="modal fade" id="addProductModal" tabindex="-1" aria-labelledby="addProductModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addProductModalLabel">从商品库添加商品</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <div class="input-group">
                        <input type="text" class="form-control" id="productSearchInput" placeholder="搜索商品" aria-label="搜索商品">
                        <button class="btn btn-outline-secondary" type="button" id="searchProductBtn">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>

                <form id="addProductsForm" action="{{ route('shops.products.add') }}" method="POST">
                    @csrf
                    <div class="row" id="productsContainer">
                        @php
                            $products = \App\Models\Product::where('is_active', true)
                                        ->whereNotIn('id', $shop->products()->pluck('products.id')->toArray())
                                        ->orderBy('created_at', 'desc')
                                        ->take(9)
                                        ->get();
                        @endphp

                        @forelse($products as $product)
                            <div class="col-md-4 mb-3 product-item">
                                <div class="card h-100">
                                    <div class="card-header d-flex align-items-center">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="product_ids[]" value="{{ $product->id }}" id="product{{ $product->id }}">
                                            <label class="form-check-label" for="product{{ $product->id }}">
                                                选择
                                            </label>
                                        </div>
                                    </div>
                                    <img src="{{ product_image_url($product->cover_image) }}" class="card-img-top" alt="{{ $product->name }}" style="height: 150px; object-fit: cover;">
                                    <div class="card-body">
                                        <h5 class="card-title">{{ $product->name }}</h5>
                                        <p class="card-text text-muted">{{ Str::limit($product->description, 50) }}</p>
                                        <p class="text-primary fw-bold">{{ $product->points_cost ?? 0 }} 积分</p>
                                    </div>
                                </div>
                            </div>
                        @empty
                            <div class="col-12">
                                <div class="alert alert-info">
                                    没有找到可添加的商品。所有商品可能已经添加到您的店铺中。
                                </div>
                            </div>
                        @endforelse
                    </div>

                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-primary" id="addSelectedProductsBtn">添加所选商品</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 编辑商品模态框 -->
<div class="modal fade" id="editProductModal" tabindex="-1" aria-labelledby="editProductModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editProductModalLabel">编辑商品信息</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editProductForm">
                    <input type="hidden" id="editProductId" name="productId">
                    <div class="mb-3">
                        <label for="editProductName" class="form-label">商品名称</label>
                        <input type="text" class="form-control" id="editProductName" name="productName" required>
                    </div>
                    <div class="mb-3">
                        <label for="editProductPoints" class="form-label">商品积分</label>
                        <input type="number" class="form-control" id="editProductPoints" name="productPoints" min="1" required>
                    </div>
                    <div class="mb-3">
                        <label for="editProductStock" class="form-label">商品库存</label>
                        <input type="number" class="form-control" id="editProductStock" name="productStock" min="0" required>
                    </div>
                    <div class="mb-3">
                        <label for="editProductDescription" class="form-label">商品描述</label>
                        <textarea class="form-control" id="editProductDescription" name="productDescription" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="saveProductBtn">保存商品</button>
            </div>
        </div>
    </div>
</div>

<!-- 创建商品模态框 -->
<div class="modal fade" id="createProductModal" tabindex="-1" aria-labelledby="createProductModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createProductModalLabel">创建新商品</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="createProductForm" enctype="multipart/form-data">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="productName" class="form-label">商品名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="productName" name="name" required>
                            </div>

                            <div class="mb-3">
                                <label for="productStock" class="form-label">商品库存 <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="productStock" name="stock" min="1" value="1" required>
                            </div>

                            <div class="mb-3">
                                <label for="productPoints" class="form-label">所需积分 <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="productPoints" name="points_cost" min="1" required>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="productImage" class="form-label">商品图片 <span class="text-danger">*</span></label>
                                <input type="file" class="form-control" id="productImage" name="cover_image" accept="image/*" required>
                                <div class="mt-2">
                                    <img id="imagePreview" src="" alt="商品预览图" class="img-thumbnail d-none" style="max-height: 150px;">
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="productCategory" class="form-label">商品分类</label>
                                <select class="form-select" id="productCategory" name="category_id">
                                    <option value="">选择分类</option>
                                    @foreach($categories as $category)
                                    <option value="{{ $category->id }}">{{ $category->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="productDescription" class="form-label">商品描述</label>
                        <textarea class="form-control" id="productDescription" name="description" rows="3"></textarea>
                    </div>

                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" value="1" id="productIsActive" name="is_active" checked>
                        <label class="form-check-label" for="productIsActive">
                            立即上架
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="saveNewProductBtn">保存商品</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<!-- 引入Sentry错误检测脚本 -->
<script src="{{ asset('js/sentry/init.js') }}"></script>
<!-- 引入店铺页面专用监控脚本 -->
<script src="{{ asset('js/sentry/shop-monitor.js') }}"></script>

<script>
    // 设置店铺相关上下文
    window.shopId = {{ optional(Auth::user()->shop)->id ?? 'null' }}; // Use optional() helper
    window.userId = {{ Auth::id() }};
    window.userName = "{{ Auth::user()->name }}";
    window.userEmail = "{{ Auth::user()->email }}";

    /**
     * 编辑商品
     */
    function editProduct(button, productName, productId) {
        console.log(`编辑商品按钮被点击 - 商品: ${productName}, ID: ${productId}`);

        // 使用Sentry记录点击事件
        if (typeof Sentry !== 'undefined') {
            Sentry.addBreadcrumb({
                category: 'ui.button',
                message: `点击编辑商品按钮: ${productName}`,
                data: {
                    product_id: productId,
                    product_name: productName,
                    button_type: 'edit'
                },
                level: 'info'
            });

            // 创建编辑商品事务
            const transaction = Sentry.startTransaction({
                name: `编辑商品: ${productName}`,
                op: 'ui.edit_product'
            });

            // 5秒后完成事务
            setTimeout(() => {
                if (transaction) transaction.finish();
            }, 5000);
        }

        try {
            // 使用Bootstrap模态框
            if (typeof bootstrap !== 'undefined') {
                // 获取编辑模态框元素
                const modalElement = document.getElementById('editProductModal');
                if (!modalElement) throw new Error('编辑模态框元素不存在');

                // 创建modal实例
                const modal = new bootstrap.Modal(modalElement);

                // 获取商品当前信息
                const card = button.closest('.card');
                if (!card) throw new Error('找不到商品卡片元素');

                // 从卡片中提取信息
                const productNameText = card.querySelector('.card-title')?.textContent || productName;
                const pointsText = card.querySelector('.text-primary.fw-bold')?.textContent || '';
                const points = pointsText.match(/\d+/) ? parseInt(pointsText.match(/\d+/)[0]) : 100;
                const stockText = card.querySelector('.card-footer small:first-child')?.textContent || '';
                const stock = stockText.match(/\d+/) ? parseInt(stockText.match(/\d+/)[0]) : 10;
                const description = card.querySelector('.card-text')?.textContent || '这是一个优质的积分兑换商品';

                // 设置表单字段值
                document.getElementById('editProductId').value = productId;
                document.getElementById('editProductName').value = productNameText;
                document.getElementById('editProductPoints').value = points;
                document.getElementById('editProductStock').value = stock;
                document.getElementById('editProductDescription').value = description;

                // 设置模态框标题
                const modalTitle = document.getElementById('editProductModalLabel');
                if (modalTitle) {
                    modalTitle.textContent = `编辑商品: ${productNameText}`;
                }

                // 设置保存按钮事件
                const saveBtn = document.getElementById('saveProductBtn');
                if (saveBtn) {
                    // 移除旧的事件监听器
                    const newSaveBtn = saveBtn.cloneNode(true);
                    saveBtn.parentNode.replaceChild(newSaveBtn, saveBtn);

                    // 添加新的事件监听器
                    newSaveBtn.addEventListener('click', function() {
                        saveProductChanges(productId);
                    });
                }

                // 显示模态框
                modal.show();
            } else {
                // 如果Bootstrap不可用，使用原生alert
                alert(`准备编辑商品: ${productName} (ID: ${productId})`);
            }
        } catch (error) {
            console.error('编辑商品时出错:', error);

            // 使用Sentry记录错误
            if (typeof Sentry !== 'undefined') {
                Sentry.captureException(error, {
                    tags: {
                        component: 'shop_product_edit',
                        product_id: productId
                    }
                });
            }

            // 显示友好的错误信息
            alert(`无法编辑商品，请稍后再试。错误: ${error.message}`);
        }

        // 防止事件冒泡和默认行为
        if (event) { // Check if event object exists
             event.preventDefault();
             event.stopPropagation();
        }
        return false;
    }

    /**
     * 保存商品修改
     */
    function saveProductChanges(productId) {
        // 获取表单数据
        const name = document.getElementById('editProductName').value;
        const points = document.getElementById('editProductPoints').value;
        const stock = document.getElementById('editProductStock').value;
        const description = document.getElementById('editProductDescription').value;

        console.log(`保存商品修改: ${name}, 积分: ${points}, 库存: ${stock}`);

        // 使用Sentry记录操作
        if (typeof Sentry !== 'undefined') {
            Sentry.addBreadcrumb({
                category: 'form.submit',
                message: `保存商品修改: ${name}`,
                data: {
                    product_id: productId,
                    product_name: name,
                    points: points,
                    stock: stock
                },
                level: 'info'
            });
        }

        try {
            // 表单验证
            if (!name || !points || !stock) {
                throw new Error('请填写所有必填字段');
            }

            // 在此处应该有实际的API调用保存数据
            // 这里仅做模拟 - TODO: Replace with actual API call
            console.log(`TODO: Call API to update product ${productId}`);

            // 模拟保存成功
            setTimeout(() => {
                // 找到对应的商品卡片并更新内容
                const cards = document.querySelectorAll('.product-card');
                let targetCard = null;

                cards.forEach(card => {
                    // Improve card matching logic if productId is available in dataset
                    if (card.closest('.col-md-4')?.dataset.productId === String(productId)) { // Check parent column for dataset
                         targetCard = card;
                    } else if (card.querySelector('.card-title')?.textContent.includes(name)) { // Fallback if dataset is not set
                         targetCard = card;
                    }
                });

                if (targetCard) {
                    // 更新卡片内容
                    const titleElement = targetCard.querySelector('.card-title');
                    if (titleElement) titleElement.textContent = name;

                    const pointsElement = targetCard.querySelector('.text-primary.fw-bold');
                    if (pointsElement) pointsElement.textContent = `${points} 积分`;

                    const descElement = targetCard.querySelector('.card-text');
                    if (descElement) descElement.textContent = description;

                    const stockElement = targetCard.querySelector('.card-footer small:first-child');
                    if (stockElement) stockElement.textContent = `库存: ${stock}件`;
                }

                // 关闭模态框
                const modalElement = document.getElementById('editProductModal');
                if (modalElement) {
                    const modal = bootstrap.Modal.getInstance(modalElement);
                    if (modal) modal.hide();
                }

                // 显示成功消息
                alert(`商品 "${name}" 修改成功！`);
            }, 500);
        } catch (error) {
            console.error('保存商品修改时出错:', error);

            // 使用Sentry记录错误
            if (typeof Sentry !== 'undefined') {
                Sentry.captureException(error, {
                    tags: {
                        component: 'shop_product_save',
                        product_id: productId
                    }
                });
            }

            // 显示友好的错误信息
            alert(`保存失败，请稍后再试。错误: ${error.message}`);
        }
    }

    /**
     * 删除商品
     */
    function deleteProduct(button, productName, productId) {
        console.log(`删除商品按钮被点击 - 商品: ${productName}, ID: ${productId}`);
        let transaction; // Define transaction variable outside the Sentry check

        // 使用Sentry记录点击事件
        if (typeof Sentry !== 'undefined') {
            Sentry.addBreadcrumb({
                category: 'ui.button',
                message: `点击删除商品按钮: ${productName}`,
                data: {
                    product_id: productId,
                    product_name: productName,
                    button_type: 'delete'
                },
                level: 'info'
            });

            // 创建删除商品事务
            transaction = Sentry.startTransaction({ // Assign to the outer scope variable
                name: `删除商品: ${productName}`,
                op: 'ui.delete_product'
            });

            // 延迟关闭事务
            setTimeout(() => {
                if (transaction) transaction.finish();
            }, 5000);
        }

        try {
            // 显示确认对话框
            if (confirm(`确定要删除商品 "${productName}" 吗？`)) {
                // 获取商品卡片元素
                const cardElement = button.closest('.col-md-4'); // Target the column
                if (!cardElement) throw new Error('找不到商品卡片元素');

                // 设置透明度，模拟删除中状态
                cardElement.style.opacity = '0.5';

                // 模拟API请求延迟 - TODO: Replace with actual API call
                console.log(`TODO: Call API to delete product ${productId}`);

                setTimeout(() => {
                    // 移除卡片
                    cardElement.remove(); // Remove the entire column

                    // 显示成功消息
                    alert(`商品 "${productName}" 已成功删除！`);

                    // Update Sentry transaction status if defined
                    if (typeof Sentry !== 'undefined' && transaction) {
                        transaction.setStatus('ok'); // Indicate success
                    }
                }, 1000);
            } else {
                // 用户取消删除
                console.log(`用户取消删除商品: ${productName}`);

                // 更新事务状态为取消
                if (typeof Sentry !== 'undefined' && transaction) {
                    transaction.setStatus('cancelled');
                }
            }
        } catch (error) {
            console.error('删除商品时出错:', error);

            // 使用Sentry记录错误
            if (typeof Sentry !== 'undefined') {
                Sentry.captureException(error, {
                    tags: {
                        component: 'shop_product_delete',
                        product_id: productId
                    }
                });
                 // Update Sentry transaction status if defined
                if (transaction) {
                    transaction.setStatus('internal_error');
                }
            }

            // 显示友好的错误信息
            alert(`无法删除商品，请稍后再试。错误: ${error.message}`);
            // Restore card opacity if deletion failed
            const cardElement = button.closest('.col-md-4');
            if (cardElement) cardElement.style.opacity = '1';
        }

        // 防止事件冒泡和默认行为
        if (event) { // Check if event object exists
             event.preventDefault();
             event.stopPropagation();
        }
        return false;
    }

    /**
     * 替换分享功能
     */
    function sharePoster(element, posterName, posterId) {
        // 显示加载提示
        showLoadingMessage('正在生成海报，请稍候...');

        // 调用API生成海报
        fetch(`/posters/generate/{{ $shop->id }}`, {
            method: 'GET',
            headers: {
            'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
            }
        })
        .then(response => response.json())
            .then(data => {
            // 隐藏加载提示
            hideLoadingMessage();

                if (data.success) {
                // 显示海报模态框
                showPosterModal(data.data.poster_url, data.data.shop_name, data.data.shop_id);
                } else {
                showErrorMessage(data.message || '生成海报失败，请稍后再试');
                }
            })
            .catch(error => {
            // 隐藏加载提示
            hideLoadingMessage();
            showErrorMessage('生成海报失败，请稍后再试');
            console.error('生成海报错误:', error);
        });
    }

    // 显示加载提示
    function showLoadingMessage(message) {
        // 创建加载提示元素
        const loadingEl = document.createElement('div');
        loadingEl.id = 'loadingMessage';
        loadingEl.className = 'position-fixed top-0 start-0 w-100 h-100 d-flex justify-content-center align-items-center';
        loadingEl.style.backgroundColor = 'rgba(0,0,0,0.5)';
        loadingEl.style.zIndex = '9999';

        const spinnerEl = document.createElement('div');
        spinnerEl.className = 'bg-white p-4 rounded text-center';
        spinnerEl.innerHTML = `
            <div class="spinner-border text-primary mb-3" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mb-0">${message}</p>
        `;

        loadingEl.appendChild(spinnerEl);
        document.body.appendChild(loadingEl);
    }

    // 隐藏加载提示
    function hideLoadingMessage() {
        const loadingEl = document.getElementById('loadingMessage');
        if (loadingEl) {
            loadingEl.remove();
        }
    }

    // 显示错误消息
    function showErrorMessage(message) {
        alert(message);
    }

    // 显示成功消息
    function showSuccessMessage(message) {
        // 创建成功提示框
        const successAlert = document.createElement('div');
        successAlert.className = 'alert alert-success alert-dismissible fade show position-fixed';
        successAlert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        successAlert.innerHTML = `
            <i class="fas fa-check-circle me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(successAlert);

        // 5秒后自动移除
        setTimeout(() => {
            if (successAlert.parentNode) {
                successAlert.remove();
            }
        }, 5000);
    }

    // 显示海报模态框
    function showPosterModal(posterUrl, shopName, shopId) {
        const modalId = `posterModal-${shopId}-${Date.now()}`; // Unique ID for modal
        const sharePanelId = `modalSharePanel-${modalId}`;
        const modalShareTriggerId = `modalShareTrigger-${modalId}`;

        // 中国风格分享面板 HTML
        const sharePanelHtml = `
            <div class="chinese-share-panel mt-4" id="${sharePanelId}" style="display: none;">
                <div class="share-panel-header mb-3">
                    <h6 class="text-center mb-0" style="color: #333; font-weight: 600;">选择分享方式</h6>
                </div>
                <div class="share-apps-grid">
                    <div class="row g-2">
                        <div class="col-3">
                            <div class="share-app-card text-center p-2" data-platform="wechat" style="cursor: pointer; border-radius: 12px; transition: all 0.3s;">
                                <div class="share-app-icon-new mx-auto mb-2" style="width: 50px; height: 50px; border-radius: 12px; background: linear-gradient(135deg, #07C160, #00A854); display: flex; align-items: center; justify-content: center; color: white; font-size: 20px;">
                                    <i class="fab fa-weixin"></i>
                                </div>
                                <span class="share-app-name-new d-block" style="font-size: 12px; color: #666;">微信好友</span>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="share-app-card text-center p-2" data-platform="timeline" style="cursor: pointer; border-radius: 12px; transition: all 0.3s;">
                                <div class="share-app-icon-new mx-auto mb-2" style="width: 50px; height: 50px; border-radius: 12px; background: linear-gradient(135deg, #07C160, #00A854); display: flex; align-items: center; justify-content: center; color: white; font-size: 18px;">
                                    <i class="fas fa-users"></i>
                                </div>
                                <span class="share-app-name-new d-block" style="font-size: 12px; color: #666;">朋友圈</span>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="share-app-card text-center p-2" data-platform="qq" style="cursor: pointer; border-radius: 12px; transition: all 0.3s;">
                                <div class="share-app-icon-new mx-auto mb-2" style="width: 50px; height: 50px; border-radius: 12px; background: linear-gradient(135deg, #12B7F5, #0d7ec7); display: flex; align-items: center; justify-content: center; color: white; font-size: 20px;">
                                    <i class="fab fa-qq"></i>
                                </div>
                                <span class="share-app-name-new d-block" style="font-size: 12px; color: #666;">QQ</span>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="share-app-card text-center p-2" data-platform="weibo" style="cursor: pointer; border-radius: 12px; transition: all 0.3s;">
                                <div class="share-app-icon-new mx-auto mb-2" style="width: 50px; height: 50px; border-radius: 12px; background: linear-gradient(135deg, #E6162D, #c41230); display: flex; align-items: center; justify-content: center; color: white; font-size: 18px;">
                                    <i class="fab fa-weibo"></i>
                                </div>
                                <span class="share-app-name-new d-block" style="font-size: 12px; color: #666;">微博</span>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="share-app-card text-center p-2" data-platform="douyin" style="cursor: pointer; border-radius: 12px; transition: all 0.3s;">
                                <div class="share-app-icon-new mx-auto mb-2" style="width: 50px; height: 50px; border-radius: 12px; background: linear-gradient(135deg, #000, #333); display: flex; align-items: center; justify-content: center; color: white; font-size: 18px;">
                                    <i class="fab fa-tiktok"></i>
                                </div>
                                <span class="share-app-name-new d-block" style="font-size: 12px; color: #666;">抖音</span>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="share-app-card text-center p-2" data-platform="xiaohongshu" style="cursor: pointer; border-radius: 12px; transition: all 0.3s;">
                                <div class="share-app-icon-new mx-auto mb-2" style="width: 50px; height: 50px; border-radius: 12px; background: linear-gradient(135deg, #FE2C55, #e01e3c); display: flex; align-items: center; justify-content: center; color: white; font-size: 18px;">
                                    <i class="fas fa-book"></i>
                                </div>
                                <span class="share-app-name-new d-block" style="font-size: 12px; color: #666;">小红书</span>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="share-app-card text-center p-2" data-platform="copy" style="cursor: pointer; border-radius: 12px; transition: all 0.3s;">
                                <div class="share-app-icon-new mx-auto mb-2" style="width: 50px; height: 50px; border-radius: 12px; background: linear-gradient(135deg, #6c757d, #5a6268); display: flex; align-items: center; justify-content: center; color: white; font-size: 18px;">
                                    <i class="fas fa-link"></i>
                                </div>
                                <span class="share-app-name-new d-block" style="font-size: 12px; color: #666;">复制链接</span>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="share-app-card text-center p-2" data-platform="native_share" style="cursor: pointer; border-radius: 12px; transition: all 0.3s;">
                                <div class="share-app-icon-new mx-auto mb-2" style="width: 50px; height: 50px; border-radius: 12px; background: linear-gradient(135deg, #4285F4, #0056b3); display: flex; align-items: center; justify-content: center; color: white; font-size: 18px;">
                                    <i class="fas fa-ellipsis-h"></i>
                                </div>
                                <span class="share-app-name-new d-block" style="font-size: 12px; color: #666;">更多</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="share-panel-footer mt-3">
                    <p class="small text-muted text-center mb-0">选择分享方式，让更多朋友看到您的店铺</p>
                </div>
            </div>
        `;

        // 创建模态框HTML
        const modalHtml = `
            <div class="modal fade" id="${modalId}" tabindex="-1" aria-labelledby="posterModalLabel-${modalId}" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="posterModalLabel-${modalId}">${shopName || '我的店铺'} - 分享海报</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                        </div>
                        <div class="modal-body text-center">
                            <div class="mb-3">
                                <img src="${posterUrl}" class="img-fluid poster-preview border" alt="海报预览">
                            </div>
                            <div class="d-flex justify-content-center share-buttons gap-2">
                                <a href="${posterUrl}" download="shop_poster.jpg" class="btn btn-success btn-lg">
                                    <i class="fas fa-download me-2"></i> 保存到相册
                                </a>
                                <button type="button" class="btn btn-primary btn-lg" id="${modalShareTriggerId}">
                                    <i class="fas fa-share-alt me-2"></i> 立即分享
                                </button>
                            </div>
                            <div class="mt-4 p-3 bg-light rounded">
                                <div class="row text-center">
                                    <div class="col-4">
                                        <i class="fas fa-mobile-alt text-primary mb-2" style="font-size: 1.5rem;"></i>
                                        <p class="small mb-0">长按图片保存</p>
                                    </div>
                                    <div class="col-4">
                                        <i class="fas fa-qrcode text-success mb-2" style="font-size: 1.5rem;"></i>
                                        <p class="small mb-0">扫码进入店铺</p>
                                    </div>
                                    <div class="col-4">
                                        <i class="fas fa-heart text-danger mb-2" style="font-size: 1.5rem;"></i>
                                        <p class="small mb-0">分享给好友</p>
                                    </div>
                                </div>
                            </div>
                            ${sharePanelHtml} <!-- 将分享面板嵌入 modal-body -->
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 清理可能存在的旧模态框
        document.querySelectorAll('.modal[id^="posterModal-"]').forEach(m => m.remove());

        // 添加模态框到页面
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // 初始化并显示模态框
        const modalElement = document.getElementById(modalId);
        const modal = new bootstrap.Modal(modalElement);
        modal.show();

        // 获取模态框内元素
        const sharePanel = document.getElementById(sharePanelId);
        const shareTriggerButton = document.getElementById(modalShareTriggerId);

        // “分享”按钮点击事件：切换模态框内分享面板的显示
        if (shareTriggerButton && sharePanel) {
            shareTriggerButton.addEventListener('click', () => {
                sharePanel.classList.toggle('show');
            });
        }

        // 为分享面板中的每个应用图标添加事件监听器和悬停效果
        if (sharePanel) {
            sharePanel.querySelectorAll('.share-app-card').forEach(function(app) {
                // 添加悬停效果
                app.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.05)';
                    this.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
                });

                app.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                    this.style.boxShadow = 'none';
                });

                // 点击事件
                app.addEventListener('click', function() {
                    var platform = this.getAttribute('data-platform');
                    var isWechat = /MicroMessenger/i.test(navigator.userAgent);
                    const shareTitle = shopName || '分享海报';
                    const shareDesc = `来自${shopName}的海报`;
                    // 生成分享页面的链接，因为直接分享图片在很多平台效果不好
                    const shareLink = `${window.location.origin}/posters/share/${encodeURIComponent(posterUrl.split('/').pop())}`;
                    const shareImgUrl = posterUrl; // 海报图片 URL

                    console.log(`分享平台: ${platform}, 海报URL: ${posterUrl}, 店铺ID: ${shopId}`);

                    if (platform === 'copy') {
                        copyToClipboard(shareLink); // 复制分享页链接
                        alert('链接已复制到剪贴板');
                        recordShare('link', shopId);
                    } else if (platform === 'download') {
                        var link = document.createElement('a');
                        link.href = posterUrl;
                        link.download = 'shop_poster.jpg';
                        link.click();
                        recordShare('download', shopId);
                    } else if (isWechat && (platform === 'wechat' || platform === 'timeline')) {
                        // 微信环境处理
                         if (typeof wx !== 'undefined' && typeof configWechatShare === 'function') {
                            configWechatShare({
                                title: shareTitle,
                                desc: shareDesc,
                                link: shareLink,
                                imgUrl: shareImgUrl
                            });
                            // 显示微信提示层 (假设 #wechatTip 存在于页面中)
                            const wechatTip = document.getElementById('wechatTip');
                            if(wechatTip) wechatTip.style.display = 'block';
                            // 点击后自动隐藏微信提示
                             setTimeout(() => {
                                if(wechatTip) wechatTip.style.display = 'none';
                            }, 3000);
                        } else {
                             alert('微信 JS-SDK 未就绪或配置函数未定义，无法分享');
                        }
                        recordShare(platform, shopId);
                    } else if (platform === 'native_share') {
                        if (navigator.share) {
                            navigator.share({
                                title: shareTitle,
                                text: shareDesc,
                                url: shareLink // 分享页面链接
                            })
                            .then(() => {
                                recordShare('native_share', shopId, { native_share: true });
                            })
                            .catch(error => console.error('原生分享失败:', error));
                        } else {
                            alert('您的设备不支持原生分享功能');
                        }
                    } else {
                        // 其他平台分享 (qq, weibo)
                        var shareTargetUrl = getPlatformShareUrl(platform, shareLink, shareTitle, shareDesc, shareImgUrl);
                        if (shareTargetUrl) {
                            window.open(shareTargetUrl, '_blank');
                            recordShare(platform, shopId);
                        } else {
                             // 对于没有直接链接的平台（抖音、小红书），尝试Web Share
                             if (navigator.share) {
                                navigator.share({
                                    title: shareTitle,
                                    text: shareDesc,
                                    url: shareLink // 分享页面链接
                                }).then(() => {
                                    recordShare(platform, shopId);
                                }).catch(err => console.error('分享失败:', err));
                            } else {
                                 alert('暂不支持直接分享到此平台，请尝试其他方式。');
                            }
                        }
                    }

                    // 分享后隐藏面板 (可选)
                    // sharePanel.classList.remove('show');
                    // 关闭模态框 (可选)
                    // modal.hide();
                });
            });
        }

        // 记录分享展示事件
        recordShareView(shopId);

        // 监听模态框关闭事件，移除DOM
        modalElement.addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    }

    // 记录分享展示事件
    function recordShareView(shopId) {
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

        if (csrfToken) {
            fetch('/posters/record-share', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': csrfToken,
                            'Accept': 'application/json'
                        },
                        body: JSON.stringify({
                    shop_id: shopId,
                    platform: 'view',
                    poster_id: 'shop_poster'
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                console.log('分享展示事件已记录:', data);
                    })
                    .catch(error => {
                console.error('记录分享展示事件失败:', error);
            });
        }
    }

    /**
     * 删除海报
     */
    function deletePoster(button, posterName, posterId) {
        console.log(`删除海报按钮被点击 - 海报: ${posterName}, ID: ${posterId}`);
         let transaction; // Define transaction outside

        // 使用Sentry记录点击事件
        if (typeof Sentry !== 'undefined') {
            Sentry.addBreadcrumb({
                category: 'ui.button',
                message: `点击删除海报按钮: ${posterName}`,
                data: {
                    poster_id: posterId,
                    poster_name: posterName,
                    button_type: 'delete'
                },
                level: 'info'
            });

            // 创建删除海报事务
            transaction = Sentry.startTransaction({ // Assign to outer scope
                name: `删除海报: ${posterName}`,
                op: 'ui.delete_poster'
            });

            // 延迟关闭事务
            setTimeout(() => {
                if (transaction) transaction.finish();
            }, 5000);
        }

        try {
            // 显示确认对话框
            if (confirm(`确定要删除海报 "${posterName}" 吗？`)) {
                // 获取海报卡片元素
                 const cardElement = button.closest('.col-md-4'); // Target the column
                if (!cardElement) throw new Error('找不到海报卡片元素');

                // 设置透明度，模拟删除中状态
                cardElement.style.opacity = '0.5';

                // 模拟API请求延迟 - TODO: Replace with actual API call
                 console.log(`TODO: Call API to delete poster ${posterId}`);
                setTimeout(() => {
                    // 隐藏卡片
                    cardElement.remove(); // Remove the entire column

                    // 显示成功消息
                    alert(`海报 "${posterName}" 已成功删除！`);

                     // Update Sentry transaction status if defined
                    if (typeof Sentry !== 'undefined' && transaction) {
                        transaction.setStatus('ok'); // Indicate success
                    }
                }, 1000);
            } else {
                // 用户取消删除
                console.log(`用户取消删除海报: ${posterName}`);

                // 更新事务状态为取消
                if (typeof Sentry !== 'undefined' && transaction) {
                    transaction.setStatus('cancelled');
                }
            }
        } catch (error) {
            console.error('删除海报时出错:', error);

            // 使用Sentry记录错误
            if (typeof Sentry !== 'undefined') {
                Sentry.captureException(error, {
                    tags: {
                        component: 'shop_poster_delete',
                        poster_id: posterId
                    }
                });
                 // Update Sentry transaction status if defined
                if (transaction) {
                    transaction.setStatus('internal_error');
                }
            }

            // 显示友好的错误信息
            alert(`无法删除海报，请稍后再试。错误: ${error.message}`);
             // Restore card opacity if deletion failed
             const cardElement = button.closest('.col-md-4');
            if (cardElement) cardElement.style.opacity = '1';
        }

        // 防止事件冒泡和默认行为
        if (event) { // Check if event object exists
             event.preventDefault();
             event.stopPropagation();
        }
        return false;
    }

    /**
     * 创建商品图片预览功能 和 保存按钮事件监听
     */
    document.addEventListener('DOMContentLoaded', function() {
        // 图片预览
        const productImageInput = document.getElementById('productImage'); // Renamed for clarity
        const imagePreview = document.getElementById('imagePreview');

        if (productImageInput && imagePreview) {
            productImageInput.addEventListener('change', function() {
                if (this.files && this.files[0]) {
                    const reader = new FileReader();

                    reader.onload = function(e) {
                        imagePreview.src = e.target.result;
                        imagePreview.classList.remove('d-none');
                    }

                    reader.readAsDataURL(this.files[0]);
                } else {
                     // Clear preview if no file selected
                     imagePreview.src = "";
                     imagePreview.classList.add('d-none');
                }
            });
        }

        // 保存新商品按钮事件
        const saveNewProductBtn = document.getElementById('saveNewProductBtn');
        if (saveNewProductBtn) {
            saveNewProductBtn.addEventListener('click', function(event) { // Pass event
                event.preventDefault(); // Prevent default form submission if button is inside form
                createNewProduct();
            });
        }

         // 页面加载完成后根据URL参数激活对应标签页
        // Moved inside DOMContentLoaded
        // 获取URL参数
        const urlParams = new URLSearchParams(window.location.search);
        const tabParam = urlParams.get('tab');

        // 如果存在tab参数并且是有效的标签页ID
        if (tabParam) {
            const tabId = tabParam + '-tab';
            const tabEl = document.getElementById(tabId);

            // 如果找到对应的标签页元素，激活它
            if (tabEl) {
                 try {
                    const tab = new bootstrap.Tab(tabEl);
                    tab.show();
                } catch (tabError) {
                     console.error('Error activating tab:', tabError);
                      if (typeof Sentry !== 'undefined') {
                         Sentry.captureException(tabError, { tags: { component: 'tab_activation' } });
                     }
                }
            }
        }

        // Sentry specific initialization (if shop-monitor.js doesn't handle it)
        // This block seems redundant if shop-monitor.js is included and does Sentry init/context setting
        // Consider removing this if shop-monitor.js handles Sentry setup for this page
         /*
        if (typeof Sentry !== 'undefined') {
            console.log('为我的店铺页面启用Sentry错误监测');

            // 设置店铺页面上下文
            Sentry.setTag('page_type', 'shop_management');
            Sentry.setTag('shop_id', window.shopId);
            Sentry.setContext('shop_info', {
                page: 'my-shop',
                user_id: window.userId,
                path: window.location.pathname
            });

            // 监控店铺页面Tab切换
            const shopTabs = document.querySelectorAll('#shopTabs button[data-bs-toggle="pill"]');
            shopTabs.forEach(tab => {
                tab.addEventListener('shown.bs.tab', function(event) {
                    Sentry.addBreadcrumb({
                        category: 'ui.tab',
                        message: `用户切换到店铺Tab: ${event.target.textContent.trim()}`,
                        level: 'info'
                    });
                });
            });

            // 监控编辑店铺按钮点击
            const editShopBtn = document.querySelector('button[data-bs-toggle="modal"][data-bs-target="#editShopModal"]');
            if (editShopBtn) {
                editShopBtn.addEventListener('click', function() {
                    Sentry.addBreadcrumb({
                        category: 'ui.interaction',
                        message: '用户点击了编辑店铺按钮',
                        level: 'info'
                    });
                });
            }

            // 监控添加商品按钮点击
            const addProductBtn = document.querySelector('button[data-bs-toggle="modal"][data-bs-target="#addProductModal"]');
            if (addProductBtn) {
                addProductBtn.addEventListener('click', function() {
                    Sentry.addBreadcrumb({
                        category: 'ui.interaction',
                        message: '用户点击了添加商品按钮',
                        level: 'info'
                    });
                });
            }

            // 全局错误处理 (Consider if Sentry's default global handler in init.js is sufficient)
            // window.onerror = function(msg, url, lineNo, columnNo, error) {
            //     Sentry.captureException(error || new Error(msg), { ... });
            //     return false;
            // };

            // 记录店铺页面性能指标
            window.addEventListener('load', function() {
                // Sentry likely captures performance automatically if configured in init.js
            });
        } else {
            console.error('Sentry未加载，无法启用错误监测');
        }
        */
    }); // End of DOMContentLoaded listener

    /**
     * 创建新商品
     */
    function createNewProduct() {
        // 获取表单数据
        const form = document.getElementById('createProductForm');
        const formData = new FormData(form);
        const productName = formData.get('name'); // Get name for Sentry transaction
        let transaction = null; // 初始化为null
        const saveNewProductBtn = document.getElementById('saveNewProductBtn');
        const originalButtonText = saveNewProductBtn ? saveNewProductBtn.innerHTML : '保存商品'; // Store original text

        // 使用Sentry记录操作
        if (typeof Sentry !== 'undefined') {
            try {
                Sentry.addBreadcrumb({
                    category: 'form.submit',
                    message: '创建新商品',
                    data: {
                        product_name: productName,
                        points_cost: formData.get('points_cost'),
                        stock: formData.get('stock')
                    },
                    level: 'info'
                });

                // 创建创建商品事务
                transaction = Sentry.startTransaction({
                    name: `创建商品: ${productName || '未命名'}`,
                    op: 'ui.create_product'
                });
            } catch (sentryError) {
                console.error('Sentry初始化事务失败:', sentryError);
                // 如果Sentry初始化失败，将transaction设为null以避免后续错误
                transaction = null;
            }
        }

        try {
            // 表单验证
            if (!form.checkValidity()) {
                 // Trigger browser validation UI
                form.reportValidity();
                 // Find the first invalid field for better error message
                 const firstInvalidField = form.querySelector(':invalid');
                 const errorMessage = firstInvalidField ? `请检查字段: ${firstInvalidField.labels[0]?.textContent || firstInvalidField.name}` : '请填写所有必填字段';
                 throw new Error(errorMessage);
            }

            // 显示加载状态
            if (saveNewProductBtn) {
                saveNewProductBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 保存中...';
                saveNewProductBtn.disabled = true;
            }

            // 检查 transaction 对象及其方法是否可用
            let fetchSpan = null;
            try {
                if (transaction && typeof transaction.startChild === 'function') {
                    fetchSpan = transaction.startChild({
                        op: 'http.client',
                        description: 'Create Product API Call'
                    });
                }
            } catch (spanError) {
                console.error('创建Sentry span失败:', spanError);
                fetchSpan = null;
            }

            // 发送到API
            fetch('{{ route("shops.products.create") }}', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                    'Accept': 'application/json'
                },
            })
            .then(response => {
                try {
                    if (fetchSpan && typeof fetchSpan.setHttpStatus === 'function') {
                        fetchSpan.setHttpStatus(response.status);
                    }
                    if (fetchSpan && typeof fetchSpan.finish === 'function') {
                        fetchSpan.finish();
                    }
                } catch (spanError) {
                    console.error('操作Sentry span失败:', spanError);
                }

                if (!response.ok) {
                    return response.json().then(errData => {
                         let message = errData.message || '创建商品失败，请稍后再试';
                         if (errData.errors) {
                              message += ': ' + Object.values(errData.errors).flat().join(' ');
                         }
                         throw new Error(message);
                    }).catch(() => {
                         throw new Error(`创建商品失败，服务器错误 (HTTP ${response.status})`);
                    });
                }
                return response.json();
            })
            .then(data => {
                console.log('创建商品成功:', data);
                try {
                    if (transaction && typeof transaction.setStatus === 'function') {
                        transaction.setStatus('ok');
                    }
                } catch (sentryError) {
                    console.error('设置Sentry事务状态失败:', sentryError);
                }

                // 隐藏模态框
                const modalElement = document.getElementById('createProductModal');
                if (modalElement) {
                    const modal = bootstrap.Modal.getInstance(modalElement);
                    if (modal) modal.hide();
                }

                // 显示成功消息
                alert(`商品 "${data.product.name}" 已成功创建！`);

                // 重置表单
                form.reset();
                const imagePreview = document.getElementById('imagePreview');
                if(imagePreview) {
                     imagePreview.src = "";
                     imagePreview.classList.add('d-none');
                }

                // 在页面上添加新创建的商品 (DOM Manipulation)
                const productsContainer = document.querySelector('#products .row');
                if (productsContainer && data.product) {
                     const newProductCard = createProductCardElement(data.product); // Refactor card creation
                     // 在列表的前面添加新商品卡片（在添加商品卡片后面）
                     const addCardColumn = productsContainer.querySelector('.product-select-card')?.closest('.col-md-4');
                     if (addCardColumn && addCardColumn.nextElementSibling) {
                          productsContainer.insertBefore(newProductCard, addCardColumn.nextElementSibling);
                     } else if (addCardColumn) {
                          productsContainer.appendChild(newProductCard); // Append after add card if it's the last
                     } else {
                          productsContainer.prepend(newProductCard); // Prepend if add card not found
                     }
                 }
            })
            .catch(error => {
                console.error('创建商品时出错:', error);
                try {
                    if (transaction && typeof transaction.setStatus === 'function') {
                        transaction.setStatus('internal_error');
                    }

                    // 使用Sentry记录错误
                    if (typeof Sentry !== 'undefined') {
                        Sentry.captureException(error, {
                            tags: {
                                component: 'shop_product_create_api' // More specific tag
                            }
                        });
                    }
                } catch (sentryError) {
                    console.error('Sentry错误处理失败:', sentryError);
                }

                // 显示友好的错误信息
                alert(`保存失败: ${error.message}`); // Show specific error
            })
            .finally(() => {
                if (saveNewProductBtn) {
                    saveNewProductBtn.innerHTML = originalButtonText;
                    saveNewProductBtn.disabled = false;
                }
                try {
                    if (transaction && typeof transaction.finish === 'function') {
                        transaction.finish();
                    }
                } catch (sentryError) {
                    console.error('结束Sentry事务失败:', sentryError);
                }
            });

        } catch (error) {
            console.error('创建商品表单处理错误:', error);

            try {
                if (typeof Sentry !== 'undefined') {
                    Sentry.captureException(error, {
                        tags: {
                            component: 'shop_product_create_validation'
                        }
                    });

                    if (transaction && typeof transaction.setStatus === 'function') {
                        transaction.setStatus('invalid_argument');
                    }

                    if (transaction && typeof transaction.finish === 'function') {
                        transaction.finish();
                    }
                }
            } catch (sentryError) {
                console.error('Sentry错误处理失败:', sentryError);
            }

            // 显示友好的错误信息 (already includes field info from validation check)
            alert(`保存失败: ${error.message}`);
        }
    }

     /**
     * Helper function to create a product card DOM element
     */
    function createProductCardElement(product) {
         const cardCol = document.createElement('div');
         cardCol.className = 'col-md-4 mb-4';
         cardCol.dataset.productId = product.id; // Add productId for easier targeting

         // 处理各种格式的图片URL
         let imageUrl = product.image_url || 'https://via.placeholder.com/300x200?text=商品图片';

         // 情况1: 完整URL
         if (imageUrl.startsWith('http')) {
             // 不需处理，已经是完整URL
         }
         // 情况2: 以/storage/开头
         else if (imageUrl.startsWith('/storage/')) {
             imageUrl = '{{ asset("") }}' + imageUrl.substring(1);
         }
         // 情况3: 以products/开头或其他相对路径
         else if (!imageUrl.startsWith('/')) {
             imageUrl = '{{ asset("storage") }}/' + imageUrl;
         }

         cardCol.innerHTML = `
             <div class="card product-card h-100">
                 ${product.points_cost ? `<span class="points-badge">${product.points_cost} 积分</span>` : ''}
                 <img src="${imageUrl}" class="card-img-top" alt="${product.name || '商品图片'}">
                 <div class="card-body">
                     <h5 class="card-title">${product.name || '未命名商品'}</h5>
                     <p class="card-text text-muted">${product.description || '暂无描述'}</p>
                     <div class="d-flex justify-content-between align-items-center">
                          ${product.points_cost ? `<span class="text-primary fw-bold">${product.points_cost} 积分</span>` : '<span></span>'}
                         <div class="btn-group">
                             <button type="button" class="btn btn-sm btn-outline-secondary" onclick="editProduct(this, '${product.name || ''}', '${product.id}')">
                                 <i class="fas fa-edit"></i>
                             </button>
                             <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteProduct(this, '${product.name || ''}', '${product.id}')">
                                 <i class="fas fa-trash"></i>
                             </button>
                         </div>
                     </div>
                 </div>
                 <div class="card-footer bg-white d-flex justify-content-between">
                     <small class="text-muted">
                         库存: ${product.stock ?? 'N/A'}件
                     </small>
                     <small class="text-muted">
                         已兑换: ${product.exchanged_count ?? 0}次
                     </small>
                 </div>
             </div>
         `;
         return cardCol;
    }

    // 店铺表单提交
    document.addEventListener('DOMContentLoaded', function() {
        // 编辑店铺
        const saveShopBtn = document.getElementById('saveShopBtn');
        if (saveShopBtn) {
            saveShopBtn.addEventListener('click', function() {
                document.getElementById('editShopForm').submit();
            });
        }

        // 其他现有脚本...

    });

    /**
     * 生成新海报函数
     */
    function generatePoster() {
        // 显示加载提示
        showLoadingMessage('正在生成海报，请稍候...');

        // 调用API生成海报
        fetch(`/posters/generate/{{ $shop->id }}`, {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            // 隐藏加载提示
            hideLoadingMessage();

            if (data.success) {
                // 显示海报模态框
                showPosterModal(data.data.poster_url, data.data.shop_name, data.data.shop_id);

                // 显示成功消息
                showSuccessMessage('海报生成成功！页面将自动刷新以显示新海报。');

                // 3秒后刷新页面以显示新生成的海报
                setTimeout(() => {
                    // 切换到海报标签页并刷新
                    window.location.href = '{{ route("shops.my") }}?tab=posters';
                }, 3000);
            } else {
                showErrorMessage(data.message || '生成海报失败，请稍后再试');
            }
        })
        .catch(error => {
            // 隐藏加载提示
            hideLoadingMessage();
            showErrorMessage('生成海报失败，请稍后再试');
            console.error('生成海报错误:', error);
        });
    }

    document.addEventListener('DOMContentLoaded', function() {
        // 搜索商品功能
        const searchProductBtn = document.getElementById('searchProductBtn');
        const productSearchInput = document.getElementById('productSearchInput');
        const productsContainer = document.getElementById('productsContainer');

        if (searchProductBtn && productSearchInput) {
            searchProductBtn.addEventListener('click', searchProducts);
            productSearchInput.addEventListener('keyup', function(event) {
                if (event.key === 'Enter') {
                    searchProducts();
                }
            });
        }

        function searchProducts() {
            const searchTerm = productSearchInput.value.trim();
            if (searchTerm.length < 2) {
                alert('请输入至少2个字符进行搜索');
                return;
            }

            // 显示加载状态
            productsContainer.innerHTML = '<div class="col-12 text-center"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">加载中...</span></div></div>';

            // 发送AJAX请求获取搜索结果
            fetch(`/api/products/search?keyword=${encodeURIComponent(searchTerm)}&shop_id={{ $shop->id }}`)
                .then(response => response.json())
                .then(data => {
                    if (data.data && data.data.length > 0) {
                        // 清空容器并添加新的结果
                        productsContainer.innerHTML = '';

                        data.data.forEach(product => {
                            const productHtml = `
                                <div class="col-md-4 mb-3 product-item">
                                    <div class="card h-100">
                                        <div class="card-header d-flex align-items-center">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="product_ids[]" value="${product.id}" id="product${product.id}">
                                                <label class="form-check-label" for="product${product.id}">
                                                    选择
                                                </label>
                                            </div>
                                        </div>
                                        <img src="${product.cover_image ? '/storage/' + product.cover_image : '/images/placeholder.jpg'}" class="card-img-top" alt="${product.name}" style="height: 150px; object-fit: cover;">
                                        <div class="card-body">
                                            <h5 class="card-title">${product.name}</h5>
                                            <p class="card-text text-muted">${product.description ? product.description.substring(0, 50) + '...' : '无描述'}</p>
                                            <p class="text-primary fw-bold">${product.points_cost || 0} 积分</p>
                                        </div>
                                    </div>
                                </div>
                            `;
                            productsContainer.innerHTML += productHtml;
                        });
                    } else {
                        productsContainer.innerHTML = '<div class="col-12"><div class="alert alert-info">没有找到匹配的商品。</div></div>';
                    }
                })
                .catch(error => {
                    console.error('搜索商品出错:', error);
                    productsContainer.innerHTML = '<div class="col-12"><div class="alert alert-danger">搜索过程中发生错误，请稍后再试。</div></div>';
                });
        }
    });

    // --- 全局分享面板相关代码 (从 share.blade.php 移植并修改) ---
    let globalSharePanelCreated = false;
    let globalBackdrop = null;
    let globalShareContainer = null;

    // 创建或获取全局分享面板
    function ensureGlobalSharePanel() {
        if (globalSharePanelCreated) return;

        const backdropId = 'globalShareBackdrop';
        const shareContainerId = 'globalMultiShareContainer';
        const closeShareButtonId = 'globalCloseShareButton';
        const cancelShareButtonId = 'globalCancelShareButton';

        // 检查是否已存在 (避免重复创建)
        if (document.getElementById(backdropId) || document.getElementById(shareContainerId)) {
            globalBackdrop = document.getElementById(backdropId);
            globalShareContainer = document.getElementById(shareContainerId);
            globalSharePanelCreated = true;
            return;
        }

        // 创建分享面板 HTML (结构来自 share.blade.php)
        const sharePanelHtml = `
            <div class="backdrop" id="${backdropId}" style="display: none; z-index: 1056;"></div>
            <div class="multi-share-container" id="${shareContainerId}" style="z-index: 1057;">
                <div class="multi-share-header">
                    <h3 class="multi-share-title">分享到</h3>
                    <button type="button" class="btn-close" id="${closeShareButtonId}"></button>
                </div>
                <div class="share-apps">
                    <div class="share-app" data-platform="wechat"><div class="share-app-icon" style="background-color: #07C160;"><i class="fab fa-weixin"></i></div><span class="share-app-name">微信</span></div>
                    <div class="share-app" data-platform="timeline"><div class="share-app-icon" style="background-color: #07C160;"><i class="fas fa-users"></i></div><span class="share-app-name">朋友圈</span></div>
                    <div class="share-app" data-platform="qq"><div class="share-app-icon" style="background-color: #12B7F5;"><i class="fab fa-qq"></i></div><span class="share-app-name">QQ</span></div>
                    <div class="share-app" data-platform="weibo"><div class="share-app-icon" style="background-color: #E6162D;"><i class="fab fa-weibo"></i></div><span class="share-app-name">微博</span></div>
                    <div class="share-app" data-platform="douyin"><div class="share-app-icon" style="background-color: #000000;"><i class="fab fa-tiktok"></i></div><span class="share-app-name">抖音</span></div>
                    <div class="share-app" data-platform="xiaohongshu"><div class="share-app-icon" style="background-color: #FE2C55;"><i class="fas fa-book"></i></div><span class="share-app-name">小红书</span></div>
                    <div class="share-app" data-platform="native_share"><div class="share-app-icon" style="background-color: #4285F4;"><i class="fas fa-share-alt"></i></div><span class="share-app-name">更多</span></div>
                    <div class="share-app" data-platform="copy"><div class="share-app-icon" style="background-color: #6c757d;"><i class="fas fa-link"></i></div><span class="share-app-name">复制链接</span></div>
                    <div class="share-app" data-platform="download"><div class="share-app-icon" style="background-color: #28a745;"><i class="fas fa-download"></i></div><span class="share-app-name">保存图片</span></div>
                </div>
                <div class="container">
                    <button type="button" class="cancel-button" id="${cancelShareButtonId}">取消</button>
                </div>
            </div>
        `;

        // 添加到 body
        document.body.insertAdjacentHTML('beforeend', sharePanelHtml);

        globalBackdrop = document.getElementById(backdropId);
        globalShareContainer = document.getElementById(shareContainerId);

        // 添加关闭事件
        const closeBtn = document.getElementById(closeShareButtonId);
        const cancelBtn = document.getElementById(cancelShareButtonId);
        if (closeBtn) closeBtn.addEventListener('click', hideGlobalSharePanel);
        if (cancelBtn) cancelBtn.addEventListener('click', hideGlobalSharePanel);
        if (globalBackdrop) globalBackdrop.addEventListener('click', hideGlobalSharePanel);

        globalSharePanelCreated = true;
    }

    // 显示全局分享面板
    function showGlobalSharePanel(posterUrl, shopName, shopId) {
        ensureGlobalSharePanel(); // 确保面板已创建
        if (!globalBackdrop || !globalShareContainer) return;

        // 显示面板
        globalBackdrop.style.display = 'block';
        globalShareContainer.classList.add('show');

        // 移除旧的应用图标事件监听器 (防止重复绑定)
        globalShareContainer.querySelectorAll('.share-app').forEach(app => {
            const newApp = app.cloneNode(true);
            app.parentNode.replaceChild(newApp, app);
        });

        // 为分享面板中的每个应用图标添加新的事件监听器
        globalShareContainer.querySelectorAll('.share-app').forEach(function(app) {
            app.addEventListener('click', function() {
                var platform = this.getAttribute('data-platform');
                var isWechat = /MicroMessenger/i.test(navigator.userAgent);
                const shareTitle = shopName || '分享海报';
                const shareDesc = `来自${shopName}的海报`;
                const shareLink = window.location.origin + '/posters/share/' + encodeURIComponent(posterUrl.split('/').pop()); // 使用分享页链接
                const shareImgUrl = posterUrl; // 海报图片 URL

                console.log(`分享平台: ${platform}, 海报URL: ${posterUrl}, 店铺ID: ${shopId}`);

                if (platform === 'copy') {
                    copyToClipboard(shareLink); // 复制分享页链接
                    alert('链接已复制到剪贴板');
                    recordShare('link', shopId);
                } else if (platform === 'download') {
                    var link = document.createElement('a');
                    link.href = posterUrl;
                    link.download = 'shop_poster.jpg';
                    link.click();
                    recordShare('download', shopId);
                } else if (isWechat && (platform === 'wechat' || platform === 'timeline')) {
                    // 微信环境处理
                     if (typeof wx !== 'undefined' && typeof configWechatShare === 'function') {
                        // 假设全局有 configWechatShare 函数 (来自布局或公共JS)
                        configWechatShare({
                            title: shareTitle,
                            desc: shareDesc,
                            link: shareLink,
                            imgUrl: shareImgUrl
                        });
                        document.getElementById('wechatTip').style.display = 'block'; // 显示微信提示
                    } else {
                         alert('微信 JS-SDK 未就绪或配置函数未定义，无法分享');
                    }
                    recordShare(platform, shopId);
                } else if (platform === 'native_share') {
                    if (navigator.share) {
                        navigator.share({
                            title: shareTitle,
                            text: shareDesc,
                            url: shareLink // 分享页面链接
                        })
                        .then(() => {
                            recordShare('native_share', shopId, { native_share: true });
                        })
                        .catch(error => console.error('原生分享失败:', error));
                    } else {
                        alert('您的设备不支持原生分享功能');
                    }
                } else {
                    // 其他平台分享 (qq, weibo)
                    var shareTargetUrl = getPlatformShareUrl(platform, shareLink, shareTitle, shareDesc, shareImgUrl);
                    if (shareTargetUrl) {
                        window.open(shareTargetUrl, '_blank');
                        recordShare(platform, shopId);
                    } else {
                         // 对于没有直接链接的平台（抖音、小红书），尝试Web Share
                         if (navigator.share) {
                            navigator.share({
                                title: shareTitle,
                                text: shareDesc,
                                url: shareLink // 分享页面链接
                            }).then(() => {
                                recordShare(platform, shopId);
                            }).catch(err => console.error('分享失败:', err));
                        } else {
                             alert('暂不支持直接分享到此平台，请尝试其他方式。');
                        }
                    }
                }

                hideGlobalSharePanel(); // 分享后隐藏面板
            });
        });
    }

    // 隐藏全局分享面板
    function hideGlobalSharePanel() {
        if (globalBackdrop) globalBackdrop.style.display = 'none';
        if (globalShareContainer) globalShareContainer.classList.remove('show');
    }

    // 获取不同平台的分享URL (修改为接收更多参数)
    function getPlatformShareUrl(platform, linkUrl, title, desc, imgUrl) {
        var encodedUrl = encodeURIComponent(linkUrl);
        var encodedTitle = encodeURIComponent(title || '分享');
        var encodedDesc = encodeURIComponent(desc || '');
        var encodedImage = encodeURIComponent(imgUrl || ''); // 使用海报图片

        switch (platform) {
            case 'qq':
                return `https://connect.qq.com/widget/shareqq/index.html?url=${encodedUrl}&title=${encodedTitle}&desc=${encodedDesc}&pics=${encodedImage}`;
            case 'weibo':
                return `https://service.weibo.com/share/share.php?url=${encodedUrl}&title=${encodedTitle}&pic=${encodedImage}`;
            default:
                return null;
        }
    }

    // 复制文本到剪贴板
    function copyToClipboard(text) {
        if (navigator.clipboard && navigator.clipboard.writeText) {
            navigator.clipboard.writeText(text)
                .catch(err => {
                    console.error('无法复制: ', err);
                    fallbackCopyToClipboard(text);
                });
        } else {
            fallbackCopyToClipboard(text);
        }
    }

    function fallbackCopyToClipboard(text) {
        var input = document.createElement('textarea');
        input.value = text;
        input.style.position = 'fixed'; // Prevent scrolling to bottom of page in MS Edge.
        input.style.left = '-9999px';
        document.body.appendChild(input);
        input.select();
        try {
            document.execCommand('copy');
        } catch (err) {
            console.error('Fallback: Oops, unable to copy', err);
        }
        document.body.removeChild(input);
    }

    // 微信分享配置函数 (示例，如果布局文件未提供)
    function configWechatShare(options) {
        if (typeof wx !== 'undefined') {
            // 假设 wx.config 已在布局文件中完成
            wx.ready(function() {
                wx.updateAppMessageShareData(options);
                wx.updateTimelineShareData(options);
                // 兼容旧版
                wx.onMenuShareAppMessage(options);
                wx.onMenuShareTimeline(options);
            });
        } else {
            console.warn('wx object not found, cannot configure WeChat share');
        }
    }

    // 事件委托：监听 document 上的点击事件，处理模态框分享按钮
    document.addEventListener('click', function(event) {
        const triggerButton = event.target.closest('.modalShareTriggerBtn');
        if (triggerButton) {
            const posterUrl = triggerButton.dataset.posterUrl;
            const shopName = triggerButton.dataset.shopName;
            const shopId = triggerButton.dataset.shopId;

            // 关闭当前模态框
            const currentModalElement = triggerButton.closest('.modal');
            if (currentModalElement) {
                const currentModalInstance = bootstrap.Modal.getInstance(currentModalElement);
                if (currentModalInstance) {
                    currentModalInstance.hide();
                }
            }

            // 显示全局分享面板
            if (posterUrl && shopId) {
                showGlobalSharePanel(posterUrl, shopName, shopId);
            } else {
                console.error('无法获取海报信息以进行分享');
            }
        }
    });

    // 确保 recordShare, copyToClipboard, fallbackCopyToClipboard, getPlatformShareUrl, configWechatShare 函数可用
    // (它们可能定义在全局或布局文件中，或者直接包含在此处)

    // 示例: recordShare (如果未在全局定义)
    function recordShare(platform, shopId, appInfo) {
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        if (!csrfToken) {
            console.error('CSRF token not found for recording share');
            return;
        }

        let postData = {
            shop_id: shopId,
            platform: platform,
            poster_id: 'shop_poster'
        };
        if (appInfo) {
            postData.app_info = appInfo;
        }

        fetch('/posters/record-share', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': csrfToken,
                'Accept': 'application/json'
            },
            body: JSON.stringify(postData)
        })
        .then(response => response.json())
        .then(data => console.log('分享记录结果:', data))
        .catch(error => console.error('记录分享失败:', error));
    }

    // 示例: copyToClipboard
    function copyToClipboard(text) {
        if (navigator.clipboard && navigator.clipboard.writeText) {
            navigator.clipboard.writeText(text)
                .catch(err => {
                    console.error('无法复制: ', err);
                    fallbackCopyToClipboard(text);
                });
        } else {
            fallbackCopyToClipboard(text);
        }
    }

    // 示例: fallbackCopyToClipboard
    function fallbackCopyToClipboard(text) {
        var input = document.createElement('textarea');
        input.value = text;
        input.style.position = 'fixed';
        input.style.left = '-9999px';
        document.body.appendChild(input);
        input.select();
        try {
            document.execCommand('copy');
        } catch (err) {
            console.error('Fallback: Oops, unable to copy', err);
        }
        document.body.removeChild(input);
    }

    // 示例: getPlatformShareUrl
    function getPlatformShareUrl(platform, linkUrl, title, desc, imgUrl) {
        var encodedUrl = encodeURIComponent(linkUrl);
        var encodedTitle = encodeURIComponent(title || '分享');
        var encodedDesc = encodeURIComponent(desc || '');
        var encodedImage = encodeURIComponent(imgUrl || '');

        switch (platform) {
            case 'qq':
                return `https://connect.qq.com/widget/shareqq/index.html?url=${encodedUrl}&title=${encodedTitle}&desc=${encodedDesc}&pics=${encodedImage}`;
            case 'weibo':
                return `https://service.weibo.com/share/share.php?url=${encodedUrl}&title=${encodedTitle}&pic=${encodedImage}`;
            default:
                return null;
        }
    }

    // 示例: configWechatShare (如果未在全局定义)
    function configWechatShare(options) {
        if (typeof wx !== 'undefined') {
            wx.ready(function() {
                wx.updateAppMessageShareData(options);
                wx.updateTimelineShareData(options);
                wx.onMenuShareAppMessage(options);
                wx.onMenuShareTimeline(options);
            });
        } else {
            console.warn('wx object not found, cannot configure WeChat share');
        }
    }

    // 新增功能：显示经营小贴士
    function showTips() {
        const tips = [
            '💡 定期更新商品图片，保持店铺新鲜感',
            '📈 关注热门商品排行，及时调整库存',
            '🎯 利用海报分享功能，扩大店铺影响力',
            '⭐ 优质的商品描述能提高转化率',
            '📱 多在社交媒体分享，增加曝光度',
            '🔥 适时推出积分优惠活动吸引用户',
            '📊 定期查看数据分析，优化经营策略'
        ];

        const randomTip = tips[Math.floor(Math.random() * tips.length)];

        // 创建提示模态框
        const tipModal = `
            <div class="modal fade" id="tipsModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header bg-warning text-dark">
                            <h5 class="modal-title">
                                <i class="fas fa-lightbulb me-2"></i>今日经营小贴士
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body text-center py-4">
                            <div class="mb-3">
                                <i class="fas fa-star text-warning" style="font-size: 3rem;"></i>
                            </div>
                            <h6 class="mb-3">${randomTip}</h6>
                            <p class="text-muted small">
                                坚持用心经营，成功就在不远处！
                            </p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-warning" data-bs-dismiss="modal">
                                <i class="fas fa-thumbs-up me-1"></i>知道了
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 移除旧的模态框
        const oldModal = document.getElementById('tipsModal');
        if (oldModal) oldModal.remove();

        // 添加新的模态框
        document.body.insertAdjacentHTML('beforeend', tipModal);

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('tipsModal'));
        modal.show();

        // 模态框关闭后移除
        document.getElementById('tipsModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    }

    // 新增功能：显示帮助中心
    function showHelp() {
        const helpModal = `
            <div class="modal fade" id="helpModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header bg-info text-white">
                            <h5 class="modal-title">
                                <i class="fas fa-question-circle me-2"></i>帮助中心
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6><i class="fas fa-box text-primary me-2"></i>商品管理</h6>
                                    <ul class="small text-muted">
                                        <li>点击"添加商品"从商品库选择商品</li>
                                        <li>可以编辑商品信息和库存</li>
                                        <li>及时补充热门商品库存</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6><i class="fas fa-shopping-cart text-danger me-2"></i>订单处理</h6>
                                    <ul class="small text-muted">
                                        <li>及时处理待处理订单</li>
                                        <li>查看订单详情和用户信息</li>
                                        <li>确认发货状态</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6><i class="fas fa-share-alt text-success me-2"></i>海报推广</h6>
                                    <ul class="small text-muted">
                                        <li>生成精美的店铺宣传海报</li>
                                        <li>分享到各大社交平台</li>
                                        <li>提高店铺知名度</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6><i class="fas fa-chart-bar text-warning me-2"></i>数据分析</h6>
                                    <ul class="small text-muted">
                                        <li>查看店铺经营数据</li>
                                        <li>分析热门商品趋势</li>
                                        <li>获取智能经营建议</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="alert alert-light mt-3">
                                <h6 class="alert-heading">
                                    <i class="fas fa-phone text-info me-1"></i>需要更多帮助？
                                </h6>
                                <p class="mb-0 small">
                                    如有疑问，请联系客服：************ 或发送邮件至 <EMAIL>
                                </p>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-info" data-bs-dismiss="modal">
                                <i class="fas fa-check me-1"></i>明白了
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 移除旧的模态框
        const oldModal = document.getElementById('helpModal');
        if (oldModal) oldModal.remove();

        // 添加新的模态框
        document.body.insertAdjacentHTML('beforeend', helpModal);

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('helpModal'));
        modal.show();

        // 模态框关闭后移除
        document.getElementById('helpModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    }

    // 新增功能：显示数据分析
    function showAnalytics() {
        // 切换到数据分析标签
        const analyticsTab = document.getElementById('analytics-tab');
        if (analyticsTab) {
            analyticsTab.click();
        }
    }

    // 页面加载完成后的初始化
    document.addEventListener('DOMContentLoaded', function() {
        // 检查URL参数，如果有tab参数则切换到对应标签
        const urlParams = new URLSearchParams(window.location.search);
        const activeTab = urlParams.get('tab');

        if (activeTab) {
            const tabButton = document.getElementById(activeTab + '-tab');
            if (tabButton) {
                tabButton.click();
            }
        }

        // 为统计卡片添加动画效果
        const statsCards = document.querySelectorAll('.stats-card');
        statsCards.forEach((card, index) => {
            setTimeout(() => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                card.style.transition = 'all 0.5s ease';

                setTimeout(() => {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, 100);
            }, index * 100);
        });
    });

</script>
@endpush