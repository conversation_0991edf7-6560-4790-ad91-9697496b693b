@php
use Illuminate\Support\Facades\Storage;
@endphp

<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- CSRF Token -->
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@yield('title', config('app.name', '积分商城'))</title>

    <!-- SEO 元标签 -->
    <meta name="description" content="@yield('meta_description', '积分商城 - 让您的积分物尽其用')">
    <meta name="keywords" content="@yield('meta_keywords', '积分,商城,兑换,购物')">
    <meta name="author" content="积分商城团队">
    <meta name="app-env" content="{{ app()->environment() }}">
    <meta name="app-version" content="{{ config('app.version', '1.0.0') }}">
    <!-- 添加登录状态标记，用于前端检测 -->
    <meta name="auth-status" content="{{ Auth::check() ? 'logged-in' : 'guest' }}">
    <meta name="auth-timestamp" content="{{ time() }}">
    @if(Auth::check())
    <meta name="auth-user" content="{{ Auth::user()->name }}">
    @endif

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#0d6efd">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="积分商城">
    <meta name="application-name" content="积分商城">
    <link rel="manifest" href="{{ asset('manifest.json') }}">
    <link rel="apple-touch-icon" href="{{ asset('icons/icon-192x192.png') }}">
    <link rel="apple-touch-icon" sizes="152x152" href="{{ asset('icons/icon-152x152.png') }}">
    <link rel="apple-touch-icon" sizes="180x180" href="{{ asset('icons/icon-192x192.png') }}">
    <link rel="apple-touch-icon" sizes="167x167" href="{{ asset('icons/icon-152x152.png') }}">

    <!-- Favicon -->
    <link rel="icon" href="{{ asset('favicon.ico') }}" type="image/x-icon">

    <!-- Fonts -->
    <link rel="dns-prefetch" href="//fonts.gstatic.com">
    <link href="https://fonts.googleapis.com/css?family=Nunito" rel="stylesheet">

    <!-- Styles -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <link href="{{ asset('css/style.css') }}" rel="stylesheet">

    <style>
        /* 左侧弹出通知样式 */
        .toast-container {
            position: fixed;
            top: 15px;
            left: 15px;
            z-index: 9999;
            max-width: 350px;
        }

        .toast {
            background-color: white;
            border-left: 4px solid #0d6efd;
            box-shadow: 0 5px 15px rgba(0,0,0,.1);
            opacity: 0;
            transform: translateX(-100%);
            transition: all 0.3s ease-in-out;
            margin-bottom: 10px;
        }

        .toast.show {
            opacity: 1;
            transform: translateX(0);
        }

        .toast.success {
            border-left-color: #28a745;
        }

        .toast.error {
            border-left-color: #dc3545;
        }

        .toast.warning {
            border-left-color: #ffc107;
        }

        .toast.info {
            border-left-color: #17a2b8;
        }

        .toast-header {
            background-color: rgba(255,255,255,.85);
            border-bottom: 1px solid rgba(0,0,0,.05);
        }

        .toast-body {
            padding: 15px;
        }

        /* 通知铃铛动画 */
        @keyframes bellRing {
            0% { transform: rotate(0); }
            20% { transform: rotate(15deg); }
            40% { transform: rotate(-15deg); }
            60% { transform: rotate(7deg); }
            80% { transform: rotate(-7deg); }
            100% { transform: rotate(0); }
        }

        .notification-bell-animate {
            animation: bellRing 1.5s ease-in-out infinite;
            transform-origin: 50% 0;
        }

        /* 自定义导航按钮样式 */
        @media (max-width: 991.98px) {
            .navbar-toggler {
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: #f8f9fa;
                transition: all 0.3s ease;
            }

            .navbar-toggler:hover, .navbar-toggler:focus {
                background-color: #e9ecef;
                box-shadow: 0 0 0 0.25rem rgba(0,0,0,0.1);
            }

            .navbar-toggler[aria-expanded="true"] {
                background-color: #e9ecef;
                border-color: #ced4da;
            }
        }

        /* 九宫格导航模态框样式 */
        .modal-content {
            border-radius: 16px;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        }

        .grid-nav-container {
            width: 100%;
        }

        .grid-nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 1.2rem 0.5rem;
            text-decoration: none;
            color: #495057;
            border-radius: 12px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            min-height: 90px;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border: 2px solid #e9ecef;
            position: relative;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        }

        .grid-nav-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(0,123,255,0.1) 0%, rgba(0,123,255,0.05) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .grid-nav-item:hover {
            color: #007bff;
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            transform: translateY(-3px) scale(1.02);
            box-shadow: 0 8px 25px rgba(0,123,255,0.25);
            border-color: #007bff;
        }

        .grid-nav-item:hover::before {
            opacity: 1;
        }

        .grid-nav-item.active {
            color: #007bff;
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-color: #007bff;
            box-shadow: 0 4px 15px rgba(0,123,255,0.3);
            transform: scale(1.02);
        }

        .grid-nav-item.active::before {
            opacity: 1;
        }

        .grid-nav-icon {
            font-size: 1.8rem;
            margin-bottom: 0.6rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            z-index: 1;
        }

        .grid-nav-item:hover .grid-nav-icon {
            transform: scale(1.15) rotate(5deg);
        }

        .grid-nav-text {
            font-size: 0.9rem;
            font-weight: 600;
            text-align: center;
            line-height: 1.2;
            position: relative;
            z-index: 1;
        }

        /* 模态框动画效果 */
        .modal.fade .modal-dialog {
            transform: scale(0.8) translateY(-50px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .modal.show .modal-dialog {
            transform: scale(1) translateY(0);
        }

        /* 响应式调整 */
        @media (max-width: 576px) {
            .modal-dialog {
                margin: 1rem;
                max-width: calc(100% - 2rem);
            }

            .grid-nav-item {
                padding: 1rem 0.3rem;
                min-height: 80px;
            }

            .grid-nav-icon {
                font-size: 1.5rem;
                margin-bottom: 0.5rem;
            }

            .grid-nav-text {
                font-size: 0.8rem;
            }
        }

        /* 导航按钮样式 */
        .btn-outline-primary {
            border-width: 2px;
            font-weight: 600;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .btn-outline-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,123,255,0.3);
        }

        /* 底部导航栏样式 */
        .mobile-bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: 1030; /* Ensure it's above most content */
            background-color: #ffffff;
            border-top: 1px solid #dee2e6;
            box-shadow: 0 -2px 5px rgba(0,0,0,0.1);
            padding: 0.5rem 0;
        }
        .mobile-bottom-nav .nav-link {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #6c757d; /* Default icon/text color */
            font-size: 0.75rem; /* Smaller text */
            text-decoration: none;
            padding: 0.25rem 0;
            flex-grow: 1; /* Distribute space evenly */
            text-align: center;
        }
        .mobile-bottom-nav .nav-link i {
            font-size: 1.25rem; /* Icon size */
            margin-bottom: 0.1rem;
        }
        .mobile-bottom-nav .nav-link.active {
            color: #0d6efd; /* Active color - Bootstrap primary */
            font-weight: 600;
        }
        /* Add padding to body bottom to prevent content overlap */
        body {
            padding-bottom: 70px; /* Adjust based on nav height */
        }
        @media (min-width: 768px) { /* Hide padding on larger screens */
            body {
                padding-bottom: 0;
            }
        }
    </style>

    @php
    // 检查当前路由是否为首页或商品列表页
    $isHomePage = request()->is('/') || request()->routeIs('home');
    $isProductsPage = request()->is('products') || request()->is('products/*');
    @endphp

    @yield('styles')

    <!-- 在head部分添加 -->
    <script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 检查是否在微信浏览器中
            var isWechat = /MicroMessenger/i.test(navigator.userAgent);
            if (isWechat) {
                // 获取微信JS-SDK配置
                fetch('{{ route("wechat.js-config") }}?url=' + encodeURIComponent(window.location.href.split('#')[0]))
                    .then(response => response.json())
                    .then(result => {
                        if (result.code === 0) {
                            var config = result.data;
                            wx.config({
                                debug: false,
                                appId: config.appId,
                                timestamp: config.timestamp,
                                nonceStr: config.nonceStr,
                                signature: config.signature,
                                jsApiList: config.jsApiList
                            });

                            wx.ready(function() {
                                console.log('微信JS-SDK配置成功');
                            });

                            wx.error(function(res) {
                                console.error('微信JS-SDK配置失败', res);
                            });
                        } else {
                            console.error('获取微信JS-SDK配置失败', result);
                        }
                    })
                    .catch(error => {
                        console.error('获取微信JS-SDK配置出错', error);
                    });
            }
        });
    </script>

    <!-- 登录状态检查脚本 -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // 检查用户登录状态是否与服务器一致
        const checkLoginStatus = async () => {
            try {
                const response = await fetch('/check-auth-status', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    credentials: 'same-origin'
                });

                if (response.ok) {
                    const data = await response.json();
                    const metaAuthStatus = document.querySelector('meta[name="auth-status"]');
                    const currentStatus = metaAuthStatus ? metaAuthStatus.getAttribute('content') : 'guest';

                    // 如果页面显示的登录状态与服务器不一致，刷新页面
                    if ((data.logged_in && currentStatus !== 'logged-in') ||
                        (!data.logged_in && currentStatus !== 'guest')) {
                        console.log('登录状态不一致，刷新页面');
                        window.location.reload();
                    }
                }
            } catch (error) {
                console.error('检查登录状态失败:', error);
            }
        };

        // 在首页执行登录状态检查
        if (window.location.pathname === '/' || window.location.pathname === '') {
            checkLoginStatus();
        }
    });
    </script>

    <!-- PWA Service Worker Registration -->
    <script src="{{ asset('js/pwa.js') }}" defer></script>

    <!-- PWA认证和Token自动刷新 -->
    <script src="{{ asset('js/pwa-auth.js') }}" defer></script>

    @stack('modals')

    <!-- 底部导航栏 (仅移动端) -->
    <nav class="mobile-bottom-nav d-block d-md-none">
        <div class="d-flex justify-content-around">
            <a class="nav-link" href="{{ url('/') }}" data-path="/">
                <i class="fas fa-home"></i>
                <span>首页</span>
            </a>
            <a class="nav-link" href="{{ url('/products') }}" data-path="/products">
                <i class="fas fa-store"></i>
                <span>商品</span>
            </a>
            <a class="nav-link" href="{{ route('points.exchange.history') }}" data-path="/points/exchange/history">
                <i class="fas fa-receipt"></i>
                <span>订单</span>
            </a>
            @auth
                <a class="nav-link" href="{{ route('shops.my') }}" data-path="/shops/my">
                    <i class="fas fa-store-alt"></i>
                    <span>店铺</span>
                </a>
                <a class="nav-link" href="{{ route('user.profile') }}" data-path="/user/profile">
                    <i class="fas fa-user"></i>
                    <span>我的</span>
                </a>
            @else
                <a class="nav-link" href="{{ route('login') }}" data-path="/login">
                    <i class="fas fa-user"></i>
                    <span>我的</span>
                </a>
            @endguest
        </div>
    </nav>
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
            <div class="container">
                <!-- 移动端头部通知图标 -->
                @auth
                <div class="d-lg-none me-2">
                    @php
                        if (!isset($unreadCount)) {
                            try {
                                $unreadCount = Auth::user()->unreadNotifications->count();
                            } catch (\Exception $e) {
                                \Log::error("获取移动端通知数时出错: user_id=" . Auth::id() . ", error=" . $e->getMessage());
                                $unreadCount = 0;
                            }
                        }
                    @endphp
                    <a href="{{ route('user.notifications') }}" class="text-decoration-none position-relative">
                        <i class="fas fa-bell @if($unreadCount > 0) notification-bell-animate @endif" style="font-size: 1.1rem; color: #495057;"></i>
                        @if($unreadCount > 0)
                        <span class="badge rounded-pill bg-danger position-absolute top-0 start-100 translate-middle"
                              style="font-size: 0.6em; padding: 2px 5px; border: 1px solid white; margin-left: -5px; margin-top: -3px;">
                            {{ $unreadCount > 9 ? '9+' : $unreadCount }}
                        </span>
                        @endif
                    </a>
                </div>
                @endauth

                <a class="navbar-brand" href="{{ url('/') }}">
                    <i class="fas fa-gift me-2"></i>积分商城
                </a>
                <!-- 九宫格导航按钮 -->
                <button class="btn btn-outline-primary d-md-none" type="button" data-bs-toggle="modal" data-bs-target="#gridNavModal">
                    <i class="fas fa-th me-1"></i><span class="fw-bold">导航</span>
                </button>

                <!-- 桌面端导航切换按钮 -->
                <button class="navbar-toggler px-3 py-2 d-none d-md-block" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="text-dark fw-bold">菜单</span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link {{ request()->is('/') ? 'active' : '' }}" href="{{ url('/') }}">首页</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ request()->is('products*') ? 'active' : '' }}" href="{{ url('/products') }}">商品列表</a>
                        </li>
                        <!-- 隐藏的菜单项 -->
                        <!-- <li class="nav-item">
                            <a class="nav-link" href="{{ route('sso.to-crm') }}">CRM系统</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ route('sso.to-pcrm') }}">新CRM系统</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ route('exam.redirect') }}">考试培训系统</a>
                        </li> -->

                    </ul>
                    <ul class="navbar-nav">
                        @guest
                            <li class="nav-item">
                                <a class="nav-link" href="{{ route('login') }}">登录</a>
                            </li>
                            @if (Route::has('register'))
                                <li class="nav-item">
                                    <a class="nav-link" href="{{ route('register') }}">注册</a>
                                </li>
                            @endif
                        @else
                            <li class="nav-item dropdown me-2">
                                @php
                                    // Use the existing $unreadCount calculated earlier
                                    // If not available, recalculate (ensure it's done only once)
                                    if (!isset($unreadCount)) {
                                        try {
                                            $unreadCount = Auth::user()->unreadNotifications->count();
                                        } catch (\Exception $e) {
                                            \Log::error("重复获取导航栏未读通知数时出错: user_id=" . Auth::id() . ", error=" . $e->getMessage());
                                            $unreadCount = 0;
                                        }
                                    }
                                @endphp
                                <a id="notificationDropdownToggle" class="nav-link" href="#" role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    <span style="position: relative; display: inline-flex; align-items: center;">
                                        <i class="fas fa-bell @if($unreadCount > 0) notification-bell-animate @endif" style="font-size: 1.1rem;"></i>
                                        @if($unreadCount > 0)
                                        <span id="notificationBadge" class="badge rounded-pill bg-danger position-absolute top-0 start-100 translate-middle"
                                              style="font-size: 0.6em; padding: 2px 5px; border: 1px solid white; margin-left: -5px; margin-top: -3px;">
                                            {{ $unreadCount > 9 ? '9+' : $unreadCount }}
                                        </span>
                                        @endif
                                    </span>
                                </a>
                                <ul id="notificationDropdownMenu" class="dropdown-menu dropdown-menu-end shadow border-0 mt-2" aria-labelledby="notificationDropdownToggle" style="min-width: 300px;">
                                    <li><h6 class="dropdown-header">未读通知</h6></li>
                                    <li><hr class="dropdown-divider my-1"></li>
                                    <div id="notificationList" style="max-height: 300px; overflow-y: auto;">
                                        @if($unreadCount > 0)
                                            <li class="px-3 py-2 text-muted small">
                                                您有 {{ $unreadCount }} 条未读消息。
                                            </li>
                                        @else
                                            <li><p class="dropdown-item disabled small text-center py-3">暂无未读通知</p></li>
                                        @endif
                                    </div>
                                    <li><hr class="dropdown-divider my-1"></li>
                                    <li class="d-flex justify-content-between align-items-center px-3 py-2">
                                        <a href="{{ route('user.notifications') }}" class="btn btn-sm btn-outline-primary">查看全部</a>
                                        <button type="button" id="markAllReadBtn" class="btn btn-sm btn-primary" @if($unreadCount == 0) disabled @endif>
                                            <i class="fas fa-check-double me-1"></i> 全部标为已读
                                        </button>
                                    </li>
                                </ul>
                            </li>
                            <li class="nav-item dropdown">
                                <a id="navbarUserDropdown" class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false" v-pre>
                                    <img src="{{ avatar_url(Auth::user()->avatar) }}" alt="{{ Auth::user()->name }} Avatar" class="rounded-circle me-2" style="width: 28px; height: 28px; object-fit: cover;">
                                    {{ Auth::user()->name }}
                                    <span class="badge bg-primary ms-1">{{ Auth::user()->points ?? 0 }} 积分</span>
                                </a>
                                <div class="dropdown-menu dropdown-menu-end shadow border-0 mt-2" aria-labelledby="navbarUserDropdown">
                                    <a class="dropdown-item" href="{{ url('/user/profile') }}">
                                        <i class="fas fa-user me-1"></i> 个人中心
                                    </a>
                                    <a class="dropdown-item" href="{{ route('wechat.bind') }}">
                                        <i class="fab fa-weixin me-1"></i> 微信绑定
                                        @if(Auth::user()->wechat_id)
                                        <span class="badge bg-success float-end">已绑定</span>
                                        @endif
                                    </a>
                                    <a class="dropdown-item" href="{{ url('/user/notifications') }}">
                                        <i class="fas fa-bell me-1"></i> 我的通知
                                        @if(Auth::user()->unreadNotifications->count() > 0)
                                        <span class="badge bg-danger float-end">{{ Auth::user()->unreadNotifications->count() }}</span>
                                        @endif
                                    </a>
                                    <!-- 隐藏考试培训系统 -->
                                    <!-- <a class="dropdown-item" href="{{ route('exam.redirect') }}">
                                        <i class="fas fa-book me-1"></i> 考试培训系统
                                    </a> -->
                                    <a class="dropdown-item" href="{{ url('/user/orders') }}">
                                        <i class="fas fa-shopping-bag me-1"></i> 我自己店铺的订单
                                    </a>
                                    <a class="dropdown-item" href="{{ url('/user/points') }}">
                                        <i class="fas fa-coins me-1"></i> 积分明细
                                    </a>
                                    <a class="dropdown-item" href="{{ route('points.exchange.history') }}">
                                        <i class="fas fa-exchange-alt me-1"></i> 兑换记录（兑换的商品订单）
                                    </a>
                                    <div class="dropdown-divider"></div>
                                    <a class="dropdown-item" href="{{ route('logout') }}" onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                                        <i class="fas fa-sign-out-alt me-1"></i> 退出登录
                                    </a>
                                    <form id="logout-form" action="{{ route('logout') }}" method="POST" class="d-none">
                                        @csrf
                                    </form>
                                </div>
                            </li>
                        @endguest
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <main class="py-4">
        <!-- 消息提醒容器 -->
        <div class="toast-container">
            @if(session('success'))
            <div class="toast success" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="toast-header">
                    <i class="fas fa-check-circle me-2 text-success"></i>
                    <strong class="me-auto">成功</strong>
                    <small>刚刚</small>
                    <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
                <div class="toast-body">
                    {{ session('success') }}
                </div>
            </div>
            @endif

            @if(session('error'))
            <div class="toast error" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="toast-header">
                    <i class="fas fa-exclamation-circle me-2 text-danger"></i>
                    <strong class="me-auto">错误</strong>
                    <small>刚刚</small>
                    <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
                <div class="toast-body">
                    {{ session('error') }}
                </div>
            </div>
            @endif

            @if(session('warning'))
            <div class="toast warning" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="toast-header">
                    <i class="fas fa-exclamation-triangle me-2 text-warning"></i>
                    <strong class="me-auto">警告</strong>
                    <small>刚刚</small>
                    <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
                <div class="toast-body">
                    {{ session('warning') }}
                </div>
            </div>
            @endif

            @if(session('info'))
            <div class="toast info" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="toast-header">
                    <i class="fas fa-info-circle me-2 text-info"></i>
                    <strong class="me-auto">信息</strong>
                    <small>刚刚</small>
                    <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
                <div class="toast-body">
                    {{ session('info') }}
                </div>
            </div>
            @endif

            {{-- 处理登录后的未读通知 --}}
            @if(session('unread_info'))
            <div class="toast info" role="alert" aria-live="assertive" aria-atomic="true"> {{-- 使用 info 样式 --}}
                <div class="toast-header">
                    <i class="fas fa-bell me-2 text-info"></i>
                    <strong class="me-auto">通知提醒</strong>
                    <small>刚刚</small>
                    <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
                <div class="toast-body">
                    {{ session('unread_info') }}
                    <a href="{{ route('user.notifications') }}" class="ms-2 btn btn-sm btn-outline-info">查看</a>
                </div>
            </div>
            @endif

        </div>

        @yield('content')
    </main>

    <footer class="bg-light text-center py-3 mt-auto">
        <div class="container">
            <div class="text-center text-muted">
                © {{ date('Y') }} {{ config('app.name', '积分商城系统') }} 保留所有权利
            </div>
        </div>
    </footer>

    <!-- 九宫格导航模态框 -->
    <div class="modal fade" id="gridNavModal" tabindex="-1" aria-labelledby="gridNavModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content border-0 shadow-lg">
                <div class="modal-header border-0 pb-2">
                    <h5 class="modal-title fw-bold" id="gridNavModalLabel">
                        <i class="fas fa-th me-2 text-primary"></i>功能导航
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body px-4 py-3">
                    <div class="grid-nav-container">
                        <div class="row g-3">
                            <div class="col-4">
                                <a href="{{ route('points.activities.index') }}" class="grid-nav-item {{ request()->is('points/activities*') ? 'active' : '' }}">
                                    <div class="grid-nav-icon">
                                        <i class="fas fa-gift"></i>
                                    </div>
                                    <div class="grid-nav-text">积分活动</div>
                                </a>
                            </div>
                            <div class="col-4">
                                <a href="{{ route('shops.create') }}" class="grid-nav-item {{ request()->is('shops/create') ? 'active' : '' }}">
                                    <div class="grid-nav-icon">
                                        <i class="fas fa-plus-circle"></i>
                                    </div>
                                    <div class="grid-nav-text">创建店铺</div>
                                </a>
                            </div>
                            <div class="col-4">
                                <a href="{{ route('shops.index') }}" class="grid-nav-item {{ request()->is('shops') && !request()->is('shops/create') && !request()->is('shops/my') ? 'active' : '' }}">
                                    <div class="grid-nav-icon">
                                        <i class="fas fa-store"></i>
                                    </div>
                                    <div class="grid-nav-text">店铺列表</div>
                                </a>
                            </div>
                            @auth
                            <div class="col-4">
                                <a href="{{ route('shops.my') }}" class="grid-nav-item {{ request()->is('shops/my') ? 'active' : '' }}">
                                    <div class="grid-nav-icon">
                                        <i class="fas fa-store-alt"></i>
                                    </div>
                                    <div class="grid-nav-text">我的店铺</div>
                                </a>
                            </div>
                            <div class="col-4">
                                <a href="{{ route('points.exchange.history') }}" class="grid-nav-item {{ request()->is('points/exchange/history') ? 'active' : '' }}">
                                    <div class="grid-nav-icon">
                                        <i class="fas fa-exchange-alt"></i>
                                    </div>
                                    <div class="grid-nav-text">兑换记录</div>
                                </a>
                            </div>
                            <div class="col-4">
                                <a href="{{ route('user.points') }}" class="grid-nav-item {{ request()->is('user/points') ? 'active' : '' }}">
                                    <div class="grid-nav-icon">
                                        <i class="fas fa-coins"></i>
                                    </div>
                                    <div class="grid-nav-text">积分明细</div>
                                </a>
                            </div>
                            <div class="col-4">
                                <a href="{{ route('user.profile') }}" class="grid-nav-item {{ request()->is('user/profile') ? 'active' : '' }}">
                                    <div class="grid-nav-icon">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <div class="grid-nav-text">个人中心</div>
                                </a>
                            </div>
                            <div class="col-4">
                                <a href="{{ route('wechat.bind') }}" class="grid-nav-item {{ request()->is('wechat/bind') ? 'active' : '' }}">
                                    <div class="grid-nav-icon">
                                        <i class="fab fa-weixin text-success"></i>
                                    </div>
                                    <div class="grid-nav-text">微信绑定</div>
                                </a>
                            </div>
                            <div class="col-4">
                                <a href="{{ route('user.notifications') }}" class="grid-nav-item {{ request()->is('user/notifications') ? 'active' : '' }}">
                                    <div class="grid-nav-icon">
                                        <i class="fas fa-bell"></i>
                                    </div>
                                    <div class="grid-nav-text">我的通知</div>
                                </a>
                            </div>
                            <div class="col-4">
                                <a href="#" id="pwa-install-grid-btn" class="grid-nav-item" onclick="event.preventDefault(); if(window.triggerPWAInstall) window.triggerPWAInstall();">
                                    <div class="grid-nav-icon">
                                        <i class="fas fa-download text-danger"></i>
                                    </div>
                                    <div class="grid-nav-text">安装APP</div>
                                </a>
                            </div>
                            @else
                            <div class="col-4">
                                <a href="{{ route('login') }}" class="grid-nav-item">
                                    <div class="grid-nav-icon">
                                        <i class="fas fa-sign-in-alt"></i>
                                    </div>
                                    <div class="grid-nav-text">登录</div>
                                </a>
                            </div>
                            <div class="col-4">
                                <a href="{{ route('register') }}" class="grid-nav-item">
                                    <div class="grid-nav-icon">
                                        <i class="fas fa-user-plus"></i>
                                    </div>
                                    <div class="grid-nav-text">注册</div>
                                </a>
                            </div>
                            <div class="col-4">
                                <a href="#" id="pwa-install-grid-btn-guest" class="grid-nav-item" onclick="event.preventDefault(); if(window.triggerPWAInstall) window.triggerPWAInstall();">
                                    <div class="grid-nav-icon">
                                        <i class="fas fa-download text-danger"></i>
                                    </div>
                                    <div class="grid-nav-text">安装APP</div>
                                </a>
                            </div>
                            @endauth
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery (必要的JavaScript库) -->
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>

    <!-- Bootstrap JS with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js" defer></script>

    <!-- 通知弹窗脚本 -->
    <script>
    document.addEventListener('DOMContentLoaded', function () {
        var toastElList = [].slice.call(document.querySelectorAll('.toast')); // Find all toast elements
        var toastList = toastElList.map(function (toastEl) {
            // Create a new Bootstrap Toast instance for each element
            var toast = new bootstrap.Toast(toastEl, {
                // autohide: true, // Default is true
                // delay: 5000 // Default is 5000ms
            });
            toast.show(); // Show the toast

            // Optional: Remove the element after it's hidden (Bootstrap event)
            toastEl.addEventListener('hidden.bs.toast', function () {
                toastEl.remove();
            });

            return toast;
        });
    });

    // 处理"全部标为已读"按钮点击事件
    document.addEventListener('DOMContentLoaded', function() {
        const markAllReadBtn = document.getElementById('markAllReadBtn');
        if (markAllReadBtn) {
            markAllReadBtn.addEventListener('click', function() {
                // 可选：添加确认步骤
                // if (!confirm('确定要将所有通知标记为已读吗？')) {
                //     return;
                // }

                this.disabled = true; // 禁用按钮防止重复点击
                this.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 处理中...';

                const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

                fetch('{{ route("user.notifications.mark-all-read") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken,
                        'Accept': 'application/json'
                    },
                    // 不需要 body，后端知道是当前用户
                })
                .then(response => {
                    if (!response.ok) {
                        return response.json().then(err => { throw new Error(err.message || '标记已读失败'); });
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        // 更新UI
                        const badge = document.getElementById('notificationBadge');
                        if (badge) badge.style.display = 'none'; // 隐藏角标

                        const bellIcon = document.querySelector('#notificationDropdownToggle i.fa-bell');
                        if (bellIcon) bellIcon.classList.remove('notification-bell-animate'); // 停止动画

                        const notificationList = document.getElementById('notificationList');
                        if (notificationList) notificationList.innerHTML = '<li><p class="dropdown-item disabled small text-center py-3">暂无未读通知</p></li>'; // 更新列表

                        markAllReadBtn.innerHTML = '<i class="fas fa-check me-1"></i> 已完成'; // 更新按钮文本
                        // 不需要再次禁用，因为没有未读消息了

                        // 可选：显示成功 toast
                        // if (typeof bootstrap !== 'undefined') { /* ... create and show success toast ... */ }
                    } else {
                        throw new Error(data.message || '标记已读失败');
                    }
                })
                .catch(error => {
                    console.error('标记全部已读时出错:', error);
                    alert('标记全部已读时出错: ' + error.message);
                    // 恢复按钮状态
                    markAllReadBtn.disabled = false;
                    markAllReadBtn.innerHTML = '<i class="fas fa-check-double me-1"></i> 全部标为已读';
                });
            });
        }
    });
    </script>

    <!-- 自定义脚本 -->
    @yield('scripts')
    @stack('scripts')

    <!-- CSRF错误检测和处理 -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // 监听所有 AJAX 请求
        $(document).ajaxError(function(event, jqxhr, settings, thrownError) {
            if (jqxhr.status === 419) {
                // CSRF 令牌失效
                console.error('CSRF token mismatch detected.');

                // 显示一个友好的提示
                if(typeof showToast === 'function') {
                    showToast('您的页面已过期，请刷新页面后重试。', 'error', '会话过期');
                } else {
                    alert('您的页面已过期，请刷新页面后重试。');
                }

                // 可以选择自动刷新页面
                // setTimeout(() => { window.location.reload(); }, 3000);
            }
        });

        // 针对表单提交（非 AJAX）的简单提示
        // 这不是完美的解决方案，因为无法直接捕获非AJAX的419响应
        // 但可以在某些情况下（如使用Turbolinks或类似库）起作用
        // 或者至少在下次加载页面时，如果session中有错误，可以显示
        @if (session('status') === 'csrf_error')
            if(typeof showToast === 'function') {
                showToast('之前的操作因页面过期失败，请重试。', 'error', '会话过期');
            } else {
                alert('之前的操作因页面过期失败，请重试。');
            }
        @endif
    });
    </script>

    <!-- PWA环境检测和微信功能隐藏 -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // 检测是否在PWA环境中
        const isPwa = () => {
            // iOS上的PWA
            if (window.navigator.standalone) {
                return true;
            }

            // Android/Chrome上的PWA
            if (window.matchMedia('(display-mode: standalone)').matches) {
                return true;
            }

            // 其他可能的PWA模式
            if (window.matchMedia('(display-mode: fullscreen), (display-mode: minimal-ui)').matches) {
                return true;
            }

            return false;
        };

        // 如果在PWA环境中，隐藏微信相关功能
        if (isPwa()) {
            console.log('检测到PWA环境，隐藏微信相关功能');

            // 登录页面
            const wechatLoginTab = document.getElementById('wechat-tab');
            const wechatLoginPane = document.getElementById('wechat-login');
            const phoneTab = document.getElementById('phone-tab');
            const phoneLoginPane = document.getElementById('phone-login');

            if (wechatLoginTab && phoneTab) {
                // 隐藏微信登录选项卡
                wechatLoginTab.style.display = 'none';

                // 激活手机验证码登录选项卡
                if (phoneTab && phoneLoginPane) {
                    phoneTab.classList.add('active');
                    phoneTab.setAttribute('aria-selected', 'true');
                    phoneLoginPane.classList.add('show', 'active');

                    // 停用微信登录选项卡
                    if (wechatLoginTab && wechatLoginPane) {
                        wechatLoginTab.classList.remove('active');
                        wechatLoginTab.setAttribute('aria-selected', 'false');
                        wechatLoginPane.classList.remove('show', 'active');
                    }
                }
            }

            // 注册页面
            const wechatRegTab = document.getElementById('wechat-reg-tab');
            const wechatRegPane = document.getElementById('wechat-register');
            const phoneRegTab = document.getElementById('phone-code-reg-tab');
            const phoneRegPane = document.getElementById('phone-code-register');

            if (wechatRegTab && phoneRegTab) {
                // 隐藏微信注册选项卡
                wechatRegTab.style.display = 'none';

                // 激活手机验证码注册选项卡
                if (phoneRegTab && phoneRegPane) {
                    phoneRegTab.classList.add('active');
                    phoneRegTab.setAttribute('aria-selected', 'true');
                    phoneRegPane.classList.add('show', 'active');

                    // 停用微信注册选项卡
                    if (wechatRegTab && wechatRegPane) {
                        wechatRegTab.classList.remove('active');
                        wechatRegTab.setAttribute('aria-selected', 'false');
                        wechatRegPane.classList.remove('show', 'active');
                    }
                }
            }

            // 微信绑定页面 - 隐藏整个页面或提示
            if (window.location.pathname.includes('/wechat/bind')) {
                const mainContent = document.querySelector('.container');
                if (mainContent) {
                    const alertDiv = document.createElement('div');
                    alertDiv.className = 'alert alert-info';
                    alertDiv.innerHTML = '<strong>提示：</strong> 在PWA环境下，微信绑定功能不可用。请使用浏览器打开此页面。';

                    // 插入在页面内容之前
                    mainContent.insertBefore(alertDiv, mainContent.firstChild);

                    // 隐藏绑定/解绑按钮
                    const bindButtons = document.querySelectorAll('form button[type="submit"]');
                    bindButtons.forEach(button => {
                        button.disabled = true;
                        button.title = 'PWA环境下不可用';
                    });
                }
            }
        }
    });
    </script>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // 九宫格导航模态框无障碍访问修复
        const gridNavModal = document.getElementById('gridNavModal');
        if (gridNavModal) {
            // 监听模态框关闭事件
            gridNavModal.addEventListener('hidden.bs.modal', function () {
                // 模态框关闭后，将焦点返回到触发按钮
                const triggerButton = document.querySelector('[data-bs-target="#gridNavModal"]');
                if (triggerButton) {
                    triggerButton.focus();
                }
            });

            // 监听模态框显示事件
            gridNavModal.addEventListener('shown.bs.modal', function () {
                // 模态框显示后，将焦点设置到第一个可聚焦元素
                const firstFocusableElement = gridNavModal.querySelector('.grid-nav-item');
                if (firstFocusableElement) {
                    firstFocusableElement.focus();
                }
            });

            // 处理九宫格导航项点击事件
            const gridNavItems = gridNavModal.querySelectorAll('.grid-nav-item');
            gridNavItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    // 在导航之前移除焦点，避免aria-hidden冲突
                    this.blur();

                    // 立即关闭模态框
                    const modal = bootstrap.Modal.getInstance(gridNavModal);
                    if (modal) {
                        modal.hide();
                    }

                    // 短暂延迟后进行导航，确保模态框完全关闭
                    setTimeout(() => {
                        window.location.href = this.href;
                    }, 100);

                    // 阻止默认行为，我们手动处理导航
                    e.preventDefault();
                });
            });
        }

        // 底部导航高亮
        const bottomNavLinks = document.querySelectorAll('.mobile-bottom-nav .nav-link');
        const currentPath = window.location.pathname;

        let bestMatch = null;
        let maxMatchLength = 0;

        bottomNavLinks.forEach(link => {
            const linkPath = link.getAttribute('data-path');
            // 完全匹配优先
            if (currentPath === linkPath) {
                bestMatch = link;
                maxMatchLength = linkPath.length; // Set max length to prevent override by shorter paths
            }
            // 如果是商品或用户相关页面，并且尚未完全匹配
            else if (maxMatchLength < linkPath.length && currentPath.startsWith(linkPath) && (linkPath === '/products' || linkPath === '/user/profile')) {
                // 检查是否有更精确的匹配已经存在
                let betterMatchExists = false;
                bottomNavLinks.forEach(otherLink => {
                    const otherPath = otherLink.getAttribute('data-path');
                    if (currentPath === otherPath && otherPath.length > linkPath.length) {
                         betterMatchExists = true;
                    }
                });

                if (!betterMatchExists) {
                    bestMatch = link;
                    maxMatchLength = linkPath.length;
                }
            }
        });

        // 如果找不到精确或起始匹配，则首页为默认
        if (!bestMatch && currentPath === '/') {
             document.querySelector('.mobile-bottom-nav .nav-link[data-path="/"]')?.classList.add('active');
        } else if (bestMatch) {
            bestMatch.classList.add('active');
        }

        // 示例：显示Toast
        // showToast('欢迎回来！', 'success', 5000);
        // showToast('警告信息示例', 'warning', 0); // 0 表示不自动关闭
        // showToast('错误信息示例', 'error');
        // showToast('普通信息示例', 'info');

        // 检查并处理全局通知
        const globalNotification = document.getElementById('globalNotification');
        if (globalNotification) {
            const message = globalNotification.getAttribute('data-message');
            const type = globalNotification.getAttribute('data-type');
            if (message && type) {
                showToast(message, type);
            }
        }
    });

    // Toast Functionality
    function showToast(message, type = 'info', duration = 3000) {
        const toastContainer = document.getElementById('toast-container');
        if (!toastContainer) return;

        const toastId = 'toast-' + Date.now();
        const toastHtml = `
            <div class="toast align-items-center ${type}" role="alert" aria-live="assertive" aria-atomic="true" id="${toastId}">
                <div class="d-flex">
                    <div class="toast-body">
                        ${message}
                    </div>
                    <button type="button" class="btn-close me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            </div>
        `;

        toastContainer.insertAdjacentHTML('beforeend', toastHtml);

        const toastElement = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastElement, {
            delay: duration > 0 ? duration : false, // 如果duration为0，则不自动隐藏
            autohide: duration > 0
        });

        // 强制显示（避免因CSS动画导致未显示）
        requestAnimationFrame(() => {
             toastElement.classList.add('show');
        });

        // 完全显示后再启动计时器（如果需要自动隐藏）
        // if(duration > 0) {
        //     setTimeout(() => toast.show(), 100); // 延迟一点点确保动画完成
        // } else {
        //     toast.show();
        // }
         toast.show(); // 直接显示

        // 隐藏后移除DOM
        toastElement.addEventListener('hidden.bs.toast', function () {
            toastElement.remove();
        });
    }
    </script>
</body>
</html>