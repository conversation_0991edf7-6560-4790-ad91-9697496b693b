@extends('layouts.app')

@section('title', '积分商城首页')

@section('scripts')
<!-- 首页不使用Sentry -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 生成海报按钮点击事件
        const generatePosterBtn = document.getElementById('generatePosterBtn');
        if (generatePosterBtn) {
            generatePosterBtn.addEventListener('click', function(e) {
                e.preventDefault();

                // 显示加载提示
                showLoadingMessage('正在生成海报，请稍候...');

                // 调用API生成海报
                fetch('/api/posters/generate', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    // 隐藏加载提示
                    hideLoadingMessage();

                    if (data.success) {
                        // 显示海报模态框
                        showPosterModal(data.data.poster_url, data.data.shop_name, data.data.shop_id);
                    } else {
                        showErrorMessage(data.message || '生成海报失败，请稍后再试');
                    }
                })
                .catch(error => {
                    // 隐藏加载提示
                    hideLoadingMessage();
                    showErrorMessage('生成海报失败，请稍后再试');
                    console.error('生成海报错误:', error);
                });
            });
        }

        // 显示加载提示
        function showLoadingMessage(message) {
            // 创建加载提示元素
            const loadingEl = document.createElement('div');
            loadingEl.id = 'loadingMessage';
            loadingEl.className = 'position-fixed top-0 start-0 w-100 h-100 d-flex justify-content-center align-items-center';
            loadingEl.style.backgroundColor = 'rgba(0,0,0,0.5)';
            loadingEl.style.zIndex = '9999';

            const spinnerEl = document.createElement('div');
            spinnerEl.className = 'bg-white p-4 rounded text-center';
            spinnerEl.innerHTML = `
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mb-0">${message}</p>
            `;

            loadingEl.appendChild(spinnerEl);
            document.body.appendChild(loadingEl);
        }

        // 隐藏加载提示
        function hideLoadingMessage() {
            const loadingEl = document.getElementById('loadingMessage');
            if (loadingEl) {
                loadingEl.remove();
            }
        }

        // 显示错误消息
        function showErrorMessage(message) {
            alert(message);
        }

        // 显示海报模态框
        function showPosterModal(posterUrl, shopName, shopId) {
            // 创建模态框HTML
            const modalHtml = `
                <div class="modal fade" id="posterModal" tabindex="-1" aria-labelledby="posterModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="posterModalLabel">${shopName || '我的店铺'} - 分享海报</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                            </div>
                            <div class="modal-body text-center">
                                <div class="mb-3">
                                    <img src="${posterUrl}" class="img-fluid poster-preview border" alt="海报预览">
                                </div>
                                <div class="d-flex justify-content-center share-buttons">
                                    <a href="${posterUrl}" download="shop_poster.png" class="btn btn-primary mx-1">
                                        <i class="fas fa-download"></i> 下载海报
                                    </a>
                                    <a href="/posters/share/${encodeURIComponent(posterUrl.split('/').pop())}" target="_blank" class="btn btn-success mx-1">
                                        <i class="fas fa-share-alt"></i> 分享到微信
                                    </a>
                                </div>
                                <div class="mt-3 small text-muted">
                                    <p>长按图片可保存或分享给好友</p>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 添加模态框到页面
            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // 初始化并显示模态框
            const modal = new bootstrap.Modal(document.getElementById('posterModal'));
            modal.show();

            // 监听模态框关闭事件，移除DOM
            document.getElementById('posterModal').addEventListener('hidden.bs.modal', function() {
                this.remove();
            });
        }
    });
</script>
@endsection

@section('styles')
<style>
    /* 京东风格主题色 */
    :root {
        --jd-red: #e93b3d;
        --jd-red-light: #f10215;
        --jd-gray: #f5f5f5;
        --jd-orange: #ff6700;
        --jd-blue: #005aa0;
        --jd-green: #00b74a;
        --jd-yellow: #ffcc00;
    }

    /* 整体布局优化 */
    body {
        background-color: var(--jd-gray);
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        color: #333;
    }

    /* 顶部轮播横幅 - 京东风格 */
    .hero-banner {
        background: linear-gradient(135deg, var(--jd-red-light), var(--jd-red));
        border-radius: 8px;
        overflow: hidden;
        margin-bottom: 20px;
        box-shadow: 0 4px 12px rgba(233, 59, 61, 0.2);
    }

    .banner-slide {
        position: relative;
        height: 200px;
        background: linear-gradient(135deg, var(--jd-red-light), var(--jd-red));
        display: flex;
        align-items: center;
        color: white;
        padding: 0 30px;
    }

    .banner-content h1 {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 10px;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .banner-content p {
        font-size: 1.1rem;
        margin-bottom: 20px;
        opacity: 0.9;
    }

    .banner-buttons .btn {
        margin-right: 10px;
        margin-bottom: 10px;
        border-radius: 20px;
        padding: 8px 20px;
        font-weight: 600;
        transition: all 0.3s;
    }

    .banner-buttons .btn-light {
        background: white;
        color: var(--jd-red);
        border: none;
    }

    .banner-buttons .btn-light:hover {
        background: #f8f9fa;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }

    .banner-buttons .btn-outline-light {
        border: 2px solid white;
        color: white;
        background: transparent;
    }

    .banner-buttons .btn-outline-light:hover {
        background: white;
        color: var(--jd-red);
        transform: translateY(-2px);
    }

    /* 快捷入口 - 京东风格九宫格 */
    .quick-nav {
        background: white;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    }

    .quick-nav-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 15px;
    }

    .quick-nav-item {
        text-align: center;
        padding: 15px 10px;
        border-radius: 8px;
        transition: all 0.3s;
        text-decoration: none;
        color: #333;
        background: #fafafa;
    }

    .quick-nav-item:hover {
        background: var(--jd-red);
        color: white;
        transform: translateY(-3px);
        box-shadow: 0 4px 12px rgba(233, 59, 61, 0.3);
        text-decoration: none;
    }

    .quick-nav-icon {
        width: 40px;
        height: 40px;
        margin: 0 auto 8px;
        background: var(--jd-red);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 18px;
        transition: all 0.3s;
    }

    .quick-nav-item:hover .quick-nav-icon {
        background: white;
        color: var(--jd-red);
    }

    .quick-nav-text {
        font-size: 13px;
        font-weight: 600;
    }

    /* 分类标题 - 京东风格 */
    .section-header {
        background: white;
        border-radius: 8px 8px 0 0;
        padding: 15px 20px;
        margin-bottom: 0;
        border-bottom: 2px solid var(--jd-red);
    }

    .section-title {
        font-size: 18px;
        font-weight: 700;
        color: #333;
        margin: 0;
        display: flex;
        align-items: center;
    }

    .section-title .title-icon {
        width: 24px;
        height: 24px;
        background: var(--jd-red);
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        margin-right: 10px;
        font-size: 12px;
    }

    .section-more {
        color: var(--jd-red);
        text-decoration: none;
        font-size: 14px;
        font-weight: 500;
        display: flex;
        align-items: center;
    }

    .section-more:hover {
        color: var(--jd-red-light);
        text-decoration: none;
    }

    /* 商品卡片 - 京东风格 */
    .section-content {
        background: white;
        border-radius: 0 0 8px 8px;
        padding: 20px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    }

    .product-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
        gap: 15px;
    }

    .product-card {
        background: white;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        overflow: hidden;
        transition: all 0.3s;
        position: relative;
        cursor: pointer;
    }

    .product-card:hover {
        border-color: var(--jd-red);
        box-shadow: 0 4px 16px rgba(233, 59, 61, 0.15);
        transform: translateY(-2px);
    }

    .product-image {
        position: relative;
        width: 100%;
        height: 160px;
        overflow: hidden;
    }

    .product-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s;
    }

    .product-card:hover .product-image img {
        transform: scale(1.05);
    }

    .product-badge {
        position: absolute;
        top: 8px;
        right: 8px;
        background: linear-gradient(45deg, var(--jd-red), var(--jd-red-light));
        color: white;
        padding: 4px 8px;
        border-radius: 10px;
        font-size: 11px;
        font-weight: 600;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }

    .product-tag {
        position: absolute;
        top: 8px;
        left: 8px;
        background: var(--jd-orange);
        color: white;
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 10px;
        font-weight: 600;
    }

    .product-info {
        padding: 12px;
    }

    .product-title {
        font-size: 14px;
        font-weight: 600;
        color: #333;
        line-height: 1.4;
        height: 2.8em;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        margin-bottom: 8px;
    }

    .product-title:hover {
        color: var(--jd-red);
    }

    .product-price {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 10px;
    }

    .price-points {
        color: var(--jd-red);
        font-size: 16px;
        font-weight: 700;
        font-family: "Arial", sans-serif;
    }

    .price-points::before {
        content: "￥";
        font-size: 12px;
        margin-right: 2px;
    }

    .price-unit {
        color: #999;
        font-size: 12px;
        margin-left: 2px;
    }

    .product-action {
        width: 100%;
    }

    .btn-exchange {
        width: 100%;
        background: linear-gradient(45deg, var(--jd-red), var(--jd-red-light));
        border: none;
        color: white;
        padding: 8px 12px;
        border-radius: 4px;
        font-size: 13px;
        font-weight: 600;
        transition: all 0.3s;
    }

    .btn-exchange:hover {
        background: linear-gradient(45deg, var(--jd-red-light), #d73027);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(233, 59, 61, 0.3);
        color: white;
    }

    /* 店铺卡片 - 京东风格 */
    .shop-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 15px;
    }

    .shop-card {
        background: white;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 20px;
        transition: all 0.3s;
        position: relative;
    }

    .shop-card:hover {
        border-color: var(--jd-red);
        box-shadow: 0 4px 16px rgba(233, 59, 61, 0.15);
        transform: translateY(-2px);
    }

    .shop-header {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
    }

    .shop-logo {
        width: 50px;
        height: 50px;
        border-radius: 8px;
        object-fit: cover;
        margin-right: 12px;
        border: 2px solid #f0f0f0;
    }

    .shop-info h5 {
        font-size: 16px;
        font-weight: 600;
        margin: 0 0 5px 0;
        color: #333;
    }

    .shop-badge {
        background: var(--jd-green);
        color: white;
        padding: 2px 8px;
        border-radius: 10px;
        font-size: 11px;
        font-weight: 600;
    }

    .shop-desc {
        color: #666;
        font-size: 13px;
        line-height: 1.4;
        margin-bottom: 15px;
        height: 2.8em;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
    }

    .shop-stats {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .shop-products {
        color: #999;
        font-size: 13px;
    }

    .btn-visit-shop {
        background: white;
        color: var(--jd-red);
        border: 1px solid var(--jd-red);
        padding: 6px 16px;
        border-radius: 4px;
        font-size: 13px;
        font-weight: 600;
        transition: all 0.3s;
        text-decoration: none;
    }

    .btn-visit-shop:hover {
        background: var(--jd-red);
        color: white;
        text-decoration: none;
    }

    /* 活动卡片 - 京东风格 */
    .activity-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 15px;
    }

    .activity-card {
        background: white;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 20px;
        transition: all 0.3s;
        position: relative;
        overflow: hidden;
    }

    .activity-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--jd-red), var(--jd-orange));
    }

    .activity-card:hover {
        border-color: var(--jd-red);
        box-shadow: 0 4px 16px rgba(233, 59, 61, 0.15);
        transform: translateY(-2px);
    }

    .activity-header {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
    }

    .activity-icon {
        width: 40px;
        height: 40px;
        background: linear-gradient(45deg, var(--jd-red), var(--jd-red-light));
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 18px;
        margin-right: 12px;
    }

    .activity-info h5 {
        font-size: 16px;
        font-weight: 600;
        margin: 0 0 5px 0;
        color: #333;
    }

    .activity-desc {
        color: #666;
        font-size: 13px;
        line-height: 1.4;
        margin-bottom: 15px;
        height: 2.8em;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
    }

    .activity-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .activity-points {
        background: linear-gradient(45deg, var(--jd-orange), var(--jd-yellow));
        color: white;
        padding: 4px 12px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 600;
        box-shadow: 0 2px 4px rgba(255, 102, 0, 0.3);
    }

    .btn-join-activity {
        background: linear-gradient(45deg, var(--jd-blue), #0066cc);
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        font-size: 13px;
        font-weight: 600;
        transition: all 0.3s;
        text-decoration: none;
    }

    .btn-join-activity:hover {
        background: linear-gradient(45deg, #0066cc, #004499);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 102, 170, 0.3);
        color: white;
        text-decoration: none;
    }

    /* 空状态 */
    .empty-state {
        text-align: center;
        padding: 40px 20px;
        color: #999;
    }

    .empty-state i {
        font-size: 48px;
        margin-bottom: 15px;
        opacity: 0.5;
    }

    .empty-state p {
        font-size: 16px;
        margin: 0;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .banner-slide {
            height: 150px;
            padding: 0 20px;
        }

        .banner-content h1 {
            font-size: 1.5rem;
        }

        .banner-content p {
            font-size: 1rem;
        }

        .quick-nav-grid {
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
        }

        .quick-nav-item {
            padding: 10px 5px;
        }

        .quick-nav-icon {
            width: 35px;
            height: 35px;
            font-size: 16px;
        }

        .quick-nav-text {
            font-size: 12px;
        }

        .product-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
        }

        .product-image {
            height: 140px;
        }

        .shop-grid {
            grid-template-columns: 1fr;
        }

        .activity-grid {
            grid-template-columns: 1fr;
        }

        .section-header {
            padding: 12px 15px;
        }

        .section-content {
            padding: 15px;
        }
    }

    @media (max-width: 480px) {
        .quick-nav-grid {
            grid-template-columns: repeat(4, 1fr);
            gap: 8px;
        }

        .product-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 8px;
        }

        .product-info {
            padding: 8px;
        }

        .product-title {
            font-size: 13px;
        }

        .price-points {
            font-size: 14px;
        }
    }
</style>
@endsection

@section('content')
<div class="container">
    <!-- 顶部横幅 -->
    <div class="hero-banner">
        <div class="banner-slide">
            <div class="banner-content">
                <h1><i class="fas fa-coins me-2"></i>欢迎来到积分商城</h1>
                <p>用积分兑换心仪商品，享受购物新体验</p>
                <div class="banner-buttons">
                    <a href="{{ url('/products') }}" class="btn btn-light">
                        <i class="fas fa-shopping-cart me-2"></i>立即购物
                    </a>
                    @guest
                    <a href="{{ route('login') }}" class="btn btn-outline-light">
                        <i class="fas fa-user me-2"></i>登录账户
                    </a>
                    @else
                    <a href="{{ route('shops.my') }}" class="btn btn-outline-light">
                        <i class="fas fa-store me-2"></i>我的店铺
                    </a>
                    @endguest
                </div>
            </div>
        </div>
    </div>

    <!-- 快捷导航 -->
    <div class="quick-nav">
        <div class="quick-nav-grid">
            <a href="{{ url('/products') }}" class="quick-nav-item">
                <div class="quick-nav-icon">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <div class="quick-nav-text">商品兑换</div>
            </a>
            <a href="{{ route('points.exchange.history') }}" class="quick-nav-item">
                <div class="quick-nav-icon">
                    <i class="fas fa-history"></i>
                </div>
                <div class="quick-nav-text">兑换记录</div>
            </a>
            <a href="{{ route('shops.index') }}" class="quick-nav-item">
                <div class="quick-nav-icon">
                    <i class="fas fa-store"></i>
                </div>
                <div class="quick-nav-text">店铺大全</div>
            </a>
            <a href="{{ route('points.activities.index') }}" class="quick-nav-item">
                <div class="quick-nav-icon">
                    <i class="fas fa-gift"></i>
                </div>
                <div class="quick-nav-text">积分活动</div>
            </a>
        </div>
    </div>
</div>

<!-- 热门商品区域 -->
<div class="container">
    <div class="section-header">
        <div class="d-flex justify-content-between align-items-center">
            <h2 class="section-title">
                <span class="title-icon">
                    <i class="fas fa-fire"></i>
                </span>
                热门商品
            </h2>
            <a href="{{ url('/products') }}" class="section-more">
                查看全部 <i class="fas fa-angle-right ms-1"></i>
            </a>
        </div>
    </div>
    <div class="section-content">
        <div class="product-grid">
            @forelse ($hotProducts as $product)
                <div class="product-card">
                    @if($product->is_hot)
                        <div class="product-tag">HOT</div>
                    @endif
                    <div class="product-image">
                        <a href="{{ route('points.exchange.confirm', $product->id) }}">
                            <img src="{{ $product->cover_image ? product_image_url($product->cover_image) : 'https://via.placeholder.com/300x200?text=商品图片' }}"
                                 alt="{{ $product->name }}" loading="lazy">
                        </a>
                        <div class="product-badge">
                            {{ $product->pointExchange ? $product->pointExchange->points_required : '0' }} 积分
                        </div>
                    </div>
                    <div class="product-info">
                        <div class="product-title" title="{{ $product->name }}">
                            {{ $product->name }}
                        </div>
                        <div class="product-price">
                            <span class="price-points">{{ $product->pointExchange ? $product->pointExchange->points_required : '0' }}</span>
                            <span class="price-unit">积分</span>
                        </div>
                        <div class="product-action">
                            @auth
                                <a href="{{ route('points.exchange.confirm', $product->id) }}" class="btn-exchange">
                                    <i class="fas fa-exchange-alt me-1"></i>立即兑换
                                </a>
                            @else
                                <a href="{{ route('login') }}?redirect_to={{ urlencode(route('points.exchange.confirm', $product->id)) }}" class="btn-exchange">
                                    <i class="fas fa-sign-in-alt me-1"></i>登录兑换
                                </a>
                            @endauth
                        </div>
                    </div>
                </div>
            @empty
                <div class="empty-state" style="grid-column: 1 / -1;">
                    <i class="fas fa-shopping-cart"></i>
                    <p>暂无热门商品，敬请期待</p>
                </div>
            @endforelse
        </div>
    </div>
</div>

<!-- 热门店铺 -->
<div class="container">
    <div class="section-header">
        <div class="d-flex justify-content-between align-items-center">
            <h2 class="section-title">
                <span class="title-icon">
                    <i class="fas fa-store"></i>
                </span>
                热门店铺
            </h2>
            <a href="{{ route('shops.index') }}" class="section-more">
                查看全部 <i class="fas fa-angle-right ms-1"></i>
            </a>
        </div>
    </div>
    <div class="section-content">
        <div class="shop-grid">
            @forelse($hotShops as $shop)
                <div class="shop-card">
                    <div class="shop-header">
                        <img src="{{ $shop->logo ? asset('storage/' . $shop->logo) : 'https://via.placeholder.com/100?text=店铺' }}"
                             alt="{{ $shop->name }}店铺LOGO" class="shop-logo">
                        <div class="shop-info">
                            <h5>{{ $shop->name }}</h5>
                            <span class="shop-badge">活跃商家</span>
                        </div>
                    </div>
                    <div class="shop-desc">
                        {{ $shop->description ?? '这是一家提供优质积分兑换商品的店铺，拥有多种商品选择。' }}
                    </div>
                    <div class="shop-stats">
                        <span class="shop-products">
                            <i class="fas fa-box me-1"></i>{{ $shop->products_count ?? 0 }} 件商品
                        </span>
                        <a href="{{ route('shops.show', $shop->id) }}" class="btn-visit-shop">
                            <i class="fas fa-arrow-right me-1"></i>进店逛逛
                        </a>
                    </div>
                </div>
            @empty
                <div class="empty-state" style="grid-column: 1 / -1;">
                    <i class="fas fa-store"></i>
                    <p>暂无热门店铺，敬请期待</p>
                </div>
            @endforelse
        </div>
    </div>
</div>

<!-- 积分活动区 -->
<div class="container">
    <div class="section-header">
        <div class="d-flex justify-content-between align-items-center">
            <h2 class="section-title">
                <span class="title-icon">
                    <i class="fas fa-gift"></i>
                </span>
                积分活动
            </h2>
            <a href="{{ route('points.activities.index') }}" class="section-more">
                查看全部 <i class="fas fa-angle-right ms-1"></i>
            </a>
        </div>
    </div>
    <div class="section-content">
        <div class="activity-grid">
            @forelse($activities as $activity)
                <div class="activity-card">
                    <div class="activity-header">
                        <div class="activity-icon">
                            <i class="fas fa-gift"></i>
                        </div>
                        <div class="activity-info">
                            <h5>{{ $activity->name }}</h5>
                        </div>
                    </div>
                    <div class="activity-desc">
                        {{ $activity->description }}
                    </div>
                    <div class="activity-footer">
                        <span class="activity-points">
                            <i class="fas fa-coins me-1"></i>奖励 {{ $activity->points }} 积分
                        </span>
                        <a href="{{ route('points.activities.index') }}" class="btn-join-activity">
                            <i class="fas fa-play me-1"></i>立即参与
                        </a>
                    </div>
                </div>
            @empty
                <div class="empty-state" style="grid-column: 1 / -1;">
                    <i class="fas fa-gift"></i>
                    <p>暂无活动，敬请期待！</p>
                </div>
            @endforelse
        </div>
    </div>
</div>

@endsection


