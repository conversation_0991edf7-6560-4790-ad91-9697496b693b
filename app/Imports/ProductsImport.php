<?php

namespace App\Imports;

use App\Models\Product;
use App\Models\Category;
use App\Models\PointExchange;
use App\Services\ImageImportService;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithValidation;
use Maatwebsite\Excel\Validators\Failure;
use Maatwebsite\Excel\Concerns\SkipsOnFailure;
use Maatwebsite\Excel\Concerns\SkipsFailures;
use Maatwebsite\Excel\Concerns\WithStartRow;

class ProductsImport implements ToCollection, WithValidation, SkipsOnFailure, WithStartRow
{
    use SkipsFailures;

    private $categories;
    private $rowCount = 0;
    private $importedCount = 0;
    private $imageService;

    public function __construct()
    {
        Log::info('[ProductImport] Import process started (using row index).');
        $this->categories = Category::all()->pluck('id', 'name');
        $this->imageService = new ImageImportService();

        // 确保导入图片目录存在
        $this->imageService->createImportDirectory();
    }

    /**
     * 指定从第几行开始读取数据（跳过表头）
     * @return int
     */
    public function startRow(): int
    {
        return 2; // 第一行是表头，从第二行开始是数据
    }

    /**
     * 导入数据处理
     * @param Collection $rows
     * @return void
     */
    public function collection(Collection $rows)
    {
        $this->rowCount = $rows->count();
        Log::info("[ProductImport] Processing {$this->rowCount} rows (using row index).");

        DB::beginTransaction();

        try {
            foreach ($rows as $index => $row) {
                $rowNumber = $index + $this->startRow();
                Log::info("[ProductImport] Processing row {$rowNumber}:", $row->toArray());

                // --- 使用新的索引访问列数据 ---
                $productName = $row[0] ?? null;     // 商品名称
                $categoryName = $row[1] ?? null;    // 分类
                $description = $row[2] ?? '商品描述'; // 描述
                $price = $row[3] ?? 0;            // 价格
                $originalPrice = $row[4] ?? null; // 原价
                $stock = $row[5] ?? 0;            // 库存
                $salesCount = $row[6] ?? 0;       // 兑换量
                $coverImageUrl = $row[7] ?? null; // 封面图片URL
                $coverImageFileName = $row[8] ?? null; // 封面图片文件名
                $productImagesUrls = $row[9] ?? null; // 商品图片URLs

                // 布尔值字段
                $isOnSaleStr = strtolower(trim($row[10] ?? ''));      // 是否上架
                $isRecommendStr = strtolower(trim($row[11] ?? ''));    // 是否推荐
                $isNewStr = strtolower(trim($row[12] ?? ''));     // 是否新品
                $isHotStr = strtolower(trim($row[13] ?? ''));     // 是否热销
                $isPointsProductStr = strtolower(trim($row[14] ?? '')); // 是否积分商品
                $pointsRequired = $row[15] ?? null;   // 所需积分
                $exchangeLimit = $row[16] ?? 0;      // 兑换限制
                $sortOrder = $row[17] ?? 0;          // 排序值
                $thirdPartyUrl = $row[18] ?? null;   // 第三方商品链接

                // 处理图片
                $processedCoverImage = $this->imageService->processImage($coverImageUrl, $coverImageFileName);
                $finalCoverImage = $processedCoverImage ?: '/images/placeholder.jpg';

                // 处理多张商品图片
                $processedProductImages = [];
                if (!empty($productImagesUrls)) {
                    $processedProductImages = $this->imageService->processMultipleImages($productImagesUrls);
                }

                // 添加日志，确认读取值
                Log::debug("[ProductImport] Row {$rowNumber}: Is Points Product value: \"{$row[14]}\", lowercase: \"{$isPointsProductStr}\"");
                Log::debug("[ProductImport] Row {$rowNumber}: Points Required value: ", [$pointsRequired]);
                Log::debug("[ProductImport] Row {$rowNumber}: Cover image processed: {$finalCoverImage}");
                Log::debug("[ProductImport] Row {$rowNumber}: Product images processed: ", $processedProductImages);

                // 宽松检查是否积分商品
                $isPointsProduct = $isPointsProductStr === '是';
                // --- 结束使用索引访问 ---

                if (empty($productName)) {
                    Log::warning("[ProductImport] Skipping row {$rowNumber} due to empty product name.");
                    continue;
                }

                // 查找或创建分类
                $categoryId = null;
                if (!empty($categoryName)) {
                    if ($this->categories->has($categoryName)) {
                        $categoryId = $this->categories[$categoryName];
                    } else {
                        Log::info("[ProductImport] Category '{$categoryName}' not found, creating new one.");

                        // 1. 生成初始 slug
                        $baseSlug = Str::slug($categoryName);

                        // 2. 处理空 slug
                        if (empty($baseSlug)) {
                            // 使用一个更可能唯一的备用方案，例如小写随机字符串
                            $baseSlug = 'category-' . strtolower(Str::random(8));
                        }

                        // 3. 确保持续检查直到 slug 唯一
                        $slug = $baseSlug;
                        $counter = 1;
                        while (Category::where('slug', $slug)->exists()) {
                            $slug = $baseSlug . '-' . $counter;
                            $counter++;
                            // 可以增加一个最大尝试次数以防止无限循环
                            if ($counter > 100) {
                                Log::error("[ProductImport] Could not generate a unique slug for category: {$categoryName}");
                                throw new \Exception("Could not generate a unique slug for category: {$categoryName}");
                            }
                        }

                        $category = Category::create([
                            'name' => $categoryName,
                            'slug' => $slug, // 使用最终确定的唯一 slug
                            'is_active' => true,
                        ]);
                        $categoryId = $category->id;
                        $this->categories[$categoryName] = $categoryId;
                    }
                }

                // 设置布尔值
                $isOnSale = $isOnSaleStr === '是';
                $isRecommend = $isRecommendStr === '是';
                $isNew = $isNewStr === '是';
                $isHot = $isHotStr === '是';

                // 创建或更新商品
                $product = Product::updateOrCreate(
                    ['name' => $productName],
                    [
                        'slug' => Str::slug($productName) . '-' . Str::random(5),
                        'category_id' => $categoryId,
                        'description' => $description,
                        'price' => $price,
                        'original_price' => $originalPrice,
                        'stock' => $stock,
                        'sales_count' => $salesCount,
                        'cover_image' => $finalCoverImage,
                        'images' => !empty($processedProductImages) ? $processedProductImages : null,
                        'is_on_sale' => $isOnSale,
                        'is_recommend' => $isRecommend,
                        'is_new' => $isNew,
                        'is_hot' => $isHot,
                        'is_points_product' => $isPointsProduct,
                        'sort_order' => $sortOrder,
                        'third_party_url' => $thirdPartyUrl,
                    ]
                );
                Log::info("[ProductImport] Product '{$product->name}' (ID: {$product->id}) processed for row {$rowNumber}.");

                // 如果是积分商品，创建或更新积分兑换规则
                if ($isPointsProduct && !empty($pointsRequired)) {
                    Log::info("[ProductImport] Setting points for product ID {$product->id} with points: {$pointsRequired}");
                    PointExchange::updateOrCreate(
                        ['product_id' => $product->id],
                        [
                            'points_required' => $pointsRequired,
                            'stock' => $product->stock,
                            'is_active' => true,
                            'exchange_limit_new' => $exchangeLimit
                        ]
                    );
                } else {
                     Log::info("[ProductImport] Skipping points setting for product ID {$product->id}. isPointsProduct: " . ($isPointsProduct ? 'true' : 'false') . ", pointsRequired empty? " . (empty($pointsRequired) ? 'yes' : 'no'));
                }
                $this->importedCount++;
            }

            DB::commit();
            Log::info("[ProductImport] Import process completed successfully. {$this->importedCount} out of {$this->rowCount} rows imported.");

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("[ProductImport] Import failed during collection processing: " . $e->getMessage(), [
                'exception' => $e,
                'trace' => Str::limit($e->getTraceAsString(), 1000)
            ]);
            throw $e;
        }
    }

    /**
     * 数据验证规则 - 新的列索引
     * @return array
     */
    public function rules(): array
    {
        // 更新列索引匹配
        return [
            '0' => 'required|string|max:255', // 商品名称
            '3' => 'required|numeric|min:0', // 价格
            '5' => 'required|integer|min:0', // 库存
            '7' => 'nullable|url|max:500', // 封面图片URL
            '9' => 'nullable|string|max:2000', // 商品图片URLs
            '15' => 'nullable|integer|min:1', // 所需积分
            '18' => 'nullable|url|max:500', // 第三方商品链接
        ];
    }

    /**
     * 自定义验证消息 - 新的列索引和描述
     * @return array
     */
    public function customValidationMessages()
    {
        return [
            '0.required' => '第 A 列(商品名称)是必填项',
            '3.required' => '第 D 列(价格)是必填项',
            '3.numeric' => '第 D 列(价格)必须是数字',
            '3.min' => '第 D 列(价格)不能小于0',
            '5.required' => '第 F 列(库存)是必填项',
            '5.integer' => '第 F 列(库存)必须是整数',
            '5.min' => '第 F 列(库存)不能小于0',
            '7.url' => '第 H 列(封面图片URL)必须是有效的URL地址',
            '7.max' => '第 H 列(封面图片URL)不能超过500个字符',
            '9.string' => '第 J 列(商品图片URLs)必须是字符串',
            '9.max' => '第 J 列(商品图片URLs)不能超过2000个字符',
            '15.integer' => '第 P 列(所需积分)必须是整数',
            '15.min' => '第 P 列(所需积分)不能小于1',
            '18.url' => '第 S 列(第三方商品链接)必须是有效的URL地址',
            '18.max' => '第 S 列(第三方商品链接)不能超过500个字符',
        ];
    }

    /**
     * 处理验证失败
     * @param Failure[] $failures
     */
    public function onFailure(Failure ...$failures)
    {
        foreach ($failures as $failure) {
            Log::warning("[ProductImport] Validation failed at row: {$failure->row()} for column index: {$failure->attribute()}", [
                'errors' => $failure->errors(),
                'values' => $failure->values()
            ]);
        }
    }
}