<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\UserAddress;
use App\Models\PointsExchange;
use App\Models\PointRecord;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PointsExchangeController extends Controller
{
    /**
     * 显示兑换确认页面
     */
    public function showConfirmPage($productId)
    {
        try {
            // 获取商品数量，默认为1
            $quantity = request('quantity', 1);
            $quantity = max(1, min(100, intval($quantity))); // 确保数量在有效范围内

            $product = Product::with('pointExchange')->findOrFail($productId);

            // 检查商品是否支持积分兑换
            if (!$product->is_points_product) {
                return redirect()->back()->with('error', '该商品不支持积分兑换');
            }

            // 检查库存
            if ($product->stock <= 0) {
                return redirect()->back()->with('error', '商品库存不足');
            }

            // 检查库存是否满足所需数量
            if ($product->stock < $quantity) {
                $quantity = $product->stock; // 如果库存不足，调整数量为最大可用库存
            }

            // 获取用户地址
            $addresses = UserAddress::where('user_id', auth()->id())->get();

            // 获取用户积分
            $userPoints = auth()->user()->points;

            // 计算总积分消耗 - 优先使用 pointExchange 表的数据
            $pointsCost = $product->pointExchange->points_required ?? $product->points_cost ?? 0;
            $totalPointsCost = $pointsCost * $quantity;

            // 判断是否可以兑换
            $canExchange = $userPoints >= $totalPointsCost;

            return view('points.exchange.confirm', compact(
                'product',
                'addresses',
                'userPoints',
                'canExchange',
                'quantity'
            ));
        } catch (\Exception $e) {
            \Log::error('兑换确认页面加载失败', [
                'error' => $e->getMessage(),
                'productId' => $productId
            ]);
            return redirect()->route('products.index')->with('error', '处理兑换请求时出错，请重试');
        }
    }

    /**
     * 显示优化版兑换确认页面（中国用户习惯）
     */
    public function showConfirmPageOptimized($productId)
    {
        try {
            // 获取商品数量，默认为1
            $quantity = request('quantity', 1);
            $quantity = max(1, min(100, intval($quantity))); // 确保数量在有效范围内

            $product = Product::with('pointExchange')->findOrFail($productId);

            // 检查商品是否支持积分兑换
            if (!$product->is_points_product) {
                return redirect()->back()->with('error', '该商品不支持积分兑换');
            }

            // 检查库存
            if ($product->stock <= 0) {
                return redirect()->back()->with('error', '商品库存不足');
            }

            // 检查库存是否满足所需数量
            if ($product->stock < $quantity) {
                $quantity = $product->stock; // 如果库存不足，调整数量为最大可用库存
            }

            // 获取用户地址
            $addresses = UserAddress::where('user_id', auth()->id())->get();

            // 获取用户积分
            $userPoints = auth()->user()->points;

            // 计算总积分消耗 - 优先使用 pointExchange 表的数据
            $pointsCost = $product->pointExchange->points_required ?? $product->points_cost ?? 0;
            $totalPointsCost = $pointsCost * $quantity;

            // 判断是否可以兑换
            $canExchange = $userPoints >= $totalPointsCost;

            return view('points.exchange.confirm-optimized', compact(
                'product',
                'addresses',
                'userPoints',
                'canExchange',
                'quantity'
            ));
        } catch (\Exception $e) {
            \Log::error('兑换确认页面加载失败', [
                'error' => $e->getMessage(),
                'productId' => $productId
            ]);
            return redirect()->route('products.index')->with('error', '处理兑换请求时出错，请重试');
        }
    }

    /**
     * 处理积分兑换
     */
    public function exchange(Request $request)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
            'address_id' => 'required|exists:user_addresses,id',
            'quantity' => 'required|integer|min:1'
        ]);

        $product = Product::with('pointExchange')->findOrFail($request->product_id);
        $address = UserAddress::findOrFail($request->address_id);
        $quantity = $request->quantity;

        // 验证地址所属权
        if ($address->user_id !== auth()->id()) {
            return redirect()->back()->with('error', '收货地址无效');
        }

        // 检查商品是否支持积分兑换
        if (!$product->is_points_product) {
            return redirect()->back()->with('error', '该商品不支持积分兑换');
        }

        // 检查库存
        if ($product->stock < $quantity) {
            return redirect()->back()->with('error', '商品库存不足');
        }

        // 计算所需积分 - 优先使用 pointExchange 表的数据
        $pointsCost = ($product->pointExchange->points_required ?? $product->points_cost ?? 0) * $quantity;

        // 检查用户积分是否足够
        $user = auth()->user();
        if ($user->points < $pointsCost) {
            return redirect()->back()->with('error', '积分不足');
        }

        try {
            DB::beginTransaction();

            // 创建兑换记录
            $exchange = PointsExchange::create([
                'user_id' => $user->id,
                'product_id' => $product->id,
                'address_id' => $address->id,
                'points_cost' => $pointsCost,
                'quantity' => $quantity,
                'order_no' => 'PE' . date('YmdHis') . rand(1000, 9999),
                'status' => 'pending'
            ]);

            // 扣除用户积分
            $user->decrement('points', $pointsCost);

            // 创建积分记录
            PointRecord::createRecord(
                $user->id,
                -$pointsCost, // 负数表示扣除积分
                'exchange',
                "积分兑换商品：{$product->name}，订单号：{$exchange->order_no}"
            );

            // 减少商品库存
            $product->decrement('stock', $quantity);

            DB::commit();

            return redirect()->route('points.exchange.order.detail', $exchange->order_no)
                           ->with('success', '兑换成功');

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('积分兑换失败: ' . $e->getMessage());
            return redirect()->back()->with('error', '兑换失败，请稍后重试');
        }
    }

    /**
     * 显示兑换历史
     */
    public function history(Request $request)
    {
        $query = PointsExchange::where('user_id', auth()->id())
                              ->with(['product', 'address'])
                              ->latest();

        // 状态筛选
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // 日期筛选
        if ($request->filled('start_date')) {
            $query->whereDate('created_at', '>=', $request->start_date);
        }
        if ($request->filled('end_date')) {
            $query->whereDate('created_at', '<=', $request->end_date);
        }

        // 商品名称搜索
        if ($request->filled('search')) {
            $query->whereHas('product', function($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%');
            });
        }

        // 订单号搜索
        if ($request->filled('order_no')) {
            $query->where('order_no', 'like', '%' . $request->order_no . '%');
        }

        $orders = $query->paginate(15);

        // 统计数据
        $statistics = [
            'total' => PointsExchange::where('user_id', auth()->id())->count(),
            'pending' => PointsExchange::where('user_id', auth()->id())->where('status', 'pending')->count(),
            'shipped' => PointsExchange::where('user_id', auth()->id())->where('status', 'shipped')->count(),
            'completed' => PointsExchange::where('user_id', auth()->id())->where('status', 'completed')->count(),
            'cancelled' => PointsExchange::where('user_id', auth()->id())->where('status', 'cancelled')->count(),
        ];

        return view('points.exchange.history', compact('orders', 'statistics'));
    }

    /**
     * 显示订单详情
     */
    public function orderDetail($orderNo)
    {
        $order = PointsExchange::where('order_no', $orderNo)
                              ->where('user_id', auth()->id())
                              ->with(['product', 'address'])
                              ->firstOrFail();

        return view('points.exchange.detail', compact('order'));
    }

    /**
     * 取消订单
     */
    public function cancelOrder($orderNo)
    {
        $order = PointsExchange::where('order_no', $orderNo)
                              ->where('user_id', auth()->id())
                              ->where('status', 'pending')
                              ->firstOrFail();

        try {
            DB::beginTransaction();

            // 更新订单状态
            $order->update(['status' => 'cancelled']);

            // 退回用户积分
            auth()->user()->increment('points', $order->points_cost);

            // 创建积分记录
            PointRecord::createRecord(
                auth()->id(),
                $order->points_cost, // 正数表示增加积分
                'exchange',
                "取消积分兑换订单退回积分：{$order->product->name}，订单号：{$order->order_no}"
            );

            // 恢复商品库存
            $order->product->increment('stock');

            DB::commit();

            return response()->json(['message' => '订单已取消']);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('取消订单失败: ' . $e->getMessage());
            return response()->json(['message' => '取消订单失败'], 500);
        }
    }

    /**
     * 确认收货
     */
    public function confirmReceived($orderNo)
    {
        $order = PointsExchange::where('order_no', $orderNo)
                              ->where('user_id', auth()->id())
                              ->where('status', 'shipping')
                              ->firstOrFail();

        try {
            $order->update([
                'status' => 'completed',
                'completed_at' => now()
            ]);

            return response()->json(['message' => '已确认收货']);

        } catch (\Exception $e) {
            Log::error('确认收货失败: ' . $e->getMessage());
            return response()->json(['message' => '确认收货失败'], 500);
        }
    }
}