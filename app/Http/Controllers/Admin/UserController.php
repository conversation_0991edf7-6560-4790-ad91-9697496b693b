<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UserController extends Controller
{
    /**
     * 显示用户列表
     */
    public function index(Request $request)
    {
        $query = User::query();

        // 搜索
        if ($request->filled('search')) {
            $search = $request->input('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%")
                  ->orWhere('username', 'like', "%{$search}%");
            });
        }

        // 角色筛选
        if ($request->filled('role')) {
            $role = $request->input('role');
            if ($role === 'admin') {
                $query->where('is_admin', true);
            } elseif ($role === 'user') {
                $query->where('is_admin', false);
            }
        }

        // 状态筛选
        if ($request->filled('status')) {
            $status = $request->input('status');
            if ($status === 'active') {
                $query->where('is_active', true);
            } elseif ($status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        // 日期范围筛选
        if ($request->filled('start_date')) {
            $startDate = $request->input('start_date');
            if ($startDate) {
                $query->whereDate('created_at', '>=', $startDate);
            }
        }
        if ($request->filled('end_date')) {
            $endDate = $request->input('end_date');
            if ($endDate) {
                $query->whereDate('created_at', '<=', $endDate);
            }
        }

        $users = $query->latest()->paginate(10);

        return view('admin.users.index', compact('users'));
    }

    /**
     * 显示创建用户表单
     */
    public function create()
    {
        return view('admin.users.create');
    }

    /**
     * 保存新用户
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['nullable', 'string', 'email', 'max:255', 'unique:users'],
            'username' => ['nullable', 'string', 'max:255', 'unique:users'],
            'phone' => ['nullable', 'string', 'max:20'],
            'wechat_id' => ['nullable', 'string', 'max:100'],
            'wechat_account' => ['nullable', 'string', 'max:50'],
            'password' => ['required', 'string', 'min:8', 'confirmed'],
            'points' => ['nullable', 'integer', 'min:0'],
            'is_admin' => ['boolean'],
            'is_active' => ['boolean'],
            'remark' => ['nullable', 'string', 'max:1000'],
        ]);

        $validated['password'] = Hash::make($validated['password']);
        $validated['is_admin'] = $request->boolean('is_admin', false);
        $validated['is_active'] = $request->has('is_active') ? $request->boolean('is_active') : false;

        $user = User::create($validated);

        return redirect()
            ->route('admin.users.index')
            ->with('success', '用户创建成功');
    }

    /**
     * 显示用户详情
     */
    public function show(User $user)
    {
        $user->load(['orders' => function ($query) {
            $query->latest()->limit(5);
        }, 'wechatUser']);

        // 如果有微信ID但没有关联微信用户，尝试查找对应的微信用户
        if (!$user->wechatUser && $user->wechat_id) {
            $wechatUser = \App\Models\WechatUser::where('openid', $user->wechat_id)->first();
            if ($wechatUser) {
                $user->wechatUser = $wechatUser;
            }
        }

        return view('admin.users.show', compact('user'));
    }

    /**
     * 显示编辑用户表单
     */
    public function edit(User $user)
    {
        return view('admin.users.edit', compact('user'));
    }

    /**
     * 更新用户信息
     */
    public function update(Request $request, User $user)
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['nullable', 'string', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
            'username' => ['nullable', 'string', 'max:255', Rule::unique('users')->ignore($user->id)],
            'phone' => ['nullable', 'string', 'max:20'],
            'wechat_id' => ['nullable', 'string', 'max:100'],
            'wechat_account' => ['nullable', 'string', 'max:50'],
            'password' => ['nullable', 'string', 'min:8', 'confirmed'],
            'points' => ['nullable', 'integer', 'min:0'],
            'is_admin' => ['boolean'],
            'is_active' => ['boolean'],
            'remark' => ['nullable', 'string', 'max:1000'],
        ]);

        if (!empty($validated['password'])) {
            $validated['password'] = Hash::make($validated['password']);
        } else {
            unset($validated['password']);
        }

        $validated['is_admin'] = $request->boolean('is_admin', false);
        $validated['is_active'] = $request->boolean('is_active', true);

        $user->update($validated);

        return redirect()
            ->route('admin.users.index')
            ->with('success', '用户信息更新成功');
    }

    /**
     * 删除用户
     */
    public function destroy(User $user)
    {
        if ($user->id === auth()->id()) {
            return back()->with('error', '不能删除当前登录的用户');
        }

        try {
            $user->delete();
            return redirect()->route('admin.users.index')
                ->with('success', '用户删除成功');
        } catch (\Exception $e) {
            return redirect()->route('admin.users.index')
                ->with('error', '用户删除失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量删除用户
     */
    public function batchDelete(Request $request)
    {
        $request->validate([
            'user_ids' => 'required|array|min:1',
            'user_ids.*' => 'integer|exists:users,id'
        ]);

        $userIds = $request->input('user_ids');
        $currentUserId = auth()->id();

        // 过滤掉当前登录用户的ID
        $userIds = array_filter($userIds, function($id) use ($currentUserId) {
            return $id != $currentUserId;
        });

        if (empty($userIds)) {
            return response()->json([
                'success' => false,
                'message' => '没有可删除的用户'
            ], 400);
        }

        try {
            DB::beginTransaction();

            // 获取要删除的用户信息（用于日志）
            $users = User::whereIn('id', $userIds)->get();
            $userNames = $users->pluck('name')->toArray();

            // 批量删除用户
            $deletedCount = User::whereIn('id', $userIds)->delete();

            DB::commit();

            // 记录日志
            Log::info('批量删除用户', [
                'admin_user' => auth()->user()->name,
                'deleted_users' => $userNames,
                'deleted_count' => $deletedCount
            ]);

            return response()->json([
                'success' => true,
                'message' => "成功删除 {$deletedCount} 个用户",
                'deleted_count' => $deletedCount
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('批量删除用户失败', [
                'admin_user' => auth()->user()->name ?? 'unknown',
                'user_ids' => $userIds,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => '删除失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 返回用户列表的JSON数据
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function listJson()
    {
        $users = User::select('id', 'name', 'email')
            ->orderBy('id')
            ->get();

        return response()->json($users);
    }

    /**
     * 将用户与微信用户关联
     *
     * @param User $user
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function linkWechatUser(User $user, Request $request)
    {
        $validated = $request->validate([
            'wechat_user_id' => 'required|exists:wechat_users,id',
        ]);

        try {
            DB::beginTransaction();

            // 获取微信用户
            $wechatUser = \App\Models\WechatUser::find($validated['wechat_user_id']);

            // 如果该微信用户已关联其他用户，先解除关联
            if ($wechatUser->user_id && $wechatUser->user_id != $user->id) {
                $oldUser = User::find($wechatUser->user_id);
                if ($oldUser) {
                    $oldUser->wechat_id = null;
                    $oldUser->wechat_account = null;
                    $oldUser->save();
                }
            }

            // 更新微信用户关联
            $wechatUser->user_id = $user->id;
            $wechatUser->save();

            // 更新系统用户的微信信息
            $user->wechat_id = $wechatUser->openid;
            $user->wechat_account = $wechatUser->nickname;
            $user->save();

            DB::commit();

            return redirect()->route('admin.users.show', $user)
                ->with('success', '用户已成功关联到微信用户');

        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()
                ->with('error', '关联失败: ' . $e->getMessage());
        }
    }

    /**
     * 执行微信用户同步命令
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function syncWechatUsers()
    {
        try {
            // 执行Artisan命令
            $exitCode = \Artisan::call('sync:wechat-users');

            // 获取命令输出
            $output = \Artisan::output();

            // 日志记录
            \Log::info('同步微信用户命令执行完成', [
                'exit_code' => $exitCode,
                'output' => $output
            ]);

            if ($exitCode === 0) {
                return redirect()->route('admin.users.index')
                    ->with('success', '微信用户同步成功')
                    ->with('command_output', $output);
            } else {
                return redirect()->route('admin.users.index')
                    ->with('error', '微信用户同步失败: ' . $output);
            }
        } catch (\Exception $e) {
            \Log::error('执行微信用户同步命令失败', [
                'error' => $e->getMessage()
            ]);

            return redirect()->route('admin.users.index')
                ->with('error', '执行同步命令失败: ' . $e->getMessage());
        }
    }

    /**
     * 用户搜索API
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function search(Request $request)
    {
        $query = $request->input('query');
        $limit = $request->input('limit', 10);

        if (empty($query)) {
            return response()->json([]);
        }

        $users = User::where('name', 'like', "%{$query}%")
            ->orWhere('email', 'like', "%{$query}%")
            ->orWhere('phone', 'like', "%{$query}%")
            ->select('id', 'name', 'email', 'phone')
            ->limit($limit)
            ->get();

        return response()->json($users);
    }

    /**
     * 显示用户导入页面
     */
    public function showImport()
    {
        return view('admin.users.import');
    }

    /**
     * 处理用户数据导入
     */
    public function import(Request $request)
    {
        $request->validate([
            'file' => 'required|file|mimes:csv,txt,xlsx,xls|max:10240', // 支持Excel文件，限制10MB大小
        ]);

        $file = $request->file('file');
        $path = $file->getRealPath();
        $extension = strtolower($file->getClientOriginalExtension());

        try {
            // 根据文件类型处理
            if (in_array($extension, ['xlsx', 'xls'])) {
                // 处理Excel文件
                return $this->importFromExcel($path);
            } else {
                // 处理CSV文件
                return $this->importFromCsv($path);
            }
        } catch (\Exception $e) {
            Log::error('用户导入失败: ' . $e->getMessage(), [
                'file' => $file->getClientOriginalName(),
                'trace' => $e->getTraceAsString()
            ]);
            return back()->withErrors(['file' => '导入过程中发生错误: ' . $e->getMessage()]);
        }
    }

    /**
     * 从CSV文件导入用户数据
     */
    private function importFromCsv($path)
    {
        // 检测文件编码并转换为UTF-8
        $content = file_get_contents($path);
        $encoding = mb_detect_encoding($content, ['UTF-8', 'GBK', 'GB2312', 'BIG5'], true);

        if ($encoding && $encoding !== 'UTF-8') {
            $content = mb_convert_encoding($content, 'UTF-8', $encoding);
            $tempPath = tempnam(sys_get_temp_dir(), 'user_import_');
            file_put_contents($tempPath, $content);
            $path = $tempPath;
        }

        $handle = fopen($path, 'r');
        if (!$handle) {
            throw new \Exception('无法读取CSV文件');
        }

        // 读取表头
        $header = fgetcsv($handle);

        // 验证CSV格式是否正确 - 只检查必填字段
        $requiredColumns = ['姓名'];
        $missingColumns = array_diff($requiredColumns, $header);

        if (!empty($missingColumns)) {
            fclose($handle);
            if (isset($tempPath) && file_exists($tempPath)) {
                unlink($tempPath); // 清理临时文件
            }
            return back()->withErrors(['file' => '导入文件格式不正确，缺少以下必填列：' . implode(', ', $missingColumns)]);
        }

        // 检查是否至少有邮箱或手机号其中一列
        if (!in_array('邮箱', $header) && !in_array('手机号', $header)) {
            fclose($handle);
            if (isset($tempPath) && file_exists($tempPath)) {
                unlink($tempPath); // 清理临时文件
            }
            return back()->withErrors(['file' => '导入文件必须包含"邮箱"或"手机号"列（至少一个）']);
        }

        // 列索引映射
        $columnMap = array_flip($header);

        return $this->processUserImportData($handle, $columnMap, isset($tempPath) ? $tempPath : null);
    }

    /**
     * 从Excel文件导入用户数据
     */
    private function importFromExcel($path)
    {
        // 检查是否安装了PhpSpreadsheet
        if (!class_exists('\PhpOffice\PhpSpreadsheet\IOFactory')) {
            throw new \Exception('系统未安装Excel处理组件，请联系管理员');
        }

        try {
            $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($path);
            $worksheet = $spreadsheet->getActiveSheet();
            $data = $worksheet->toArray();

            if (empty($data)) {
                throw new \Exception('Excel文件为空或无法读取');
            }

            // 获取表头
            $header = array_shift($data);

            // 验证格式 - 只检查必填字段
            $requiredColumns = ['姓名'];
            $missingColumns = array_diff($requiredColumns, $header);

            if (!empty($missingColumns)) {
                return back()->withErrors(['file' => '导入文件格式不正确，缺少以下必填列：' . implode(', ', $missingColumns)]);
            }

            // 检查是否至少有邮箱或手机号其中一列
            if (!in_array('邮箱', $header) && !in_array('手机号', $header)) {
                return back()->withErrors(['file' => '导入文件必须包含"邮箱"或"手机号"列（至少一个）']);
            }

            $columnMap = array_flip($header);

            // 创建临时CSV文件进行统一处理
            $tempPath = tempnam(sys_get_temp_dir(), 'user_import_excel_');
            $handle = fopen($tempPath, 'w');

            // 写入表头
            fputcsv($handle, $header);

            // 写入数据
            foreach ($data as $row) {
                fputcsv($handle, $row);
            }

            fclose($handle);

            // 重新打开文件进行处理
            $handle = fopen($tempPath, 'r');
            fgetcsv($handle); // 跳过表头

            return $this->processUserImportData($handle, $columnMap, $tempPath);

        } catch (\Exception $e) {
            throw new \Exception('Excel文件处理失败: ' . $e->getMessage());
        }
    }

    /**
     * 处理用户导入数据
     */
    private function processUserImportData($handle, $columnMap, $tempPath = null)
    {
        $importCount = 0;
        $errorCount = 0;
        $errorRows = [];
        $rowNumber = 1; // 从第2行开始（第1行是表头）

        DB::beginTransaction();

        try {
            while (($row = fgetcsv($handle)) !== false) {
                $rowNumber++;

                // 跳过空行
                if (empty(array_filter($row))) {
                    continue;
                }

                try {
                    // 提取数据 - 安全地获取列数据
                    $name = trim($row[$columnMap['姓名']] ?? '');
                    $email = isset($columnMap['邮箱']) ? trim($row[$columnMap['邮箱']] ?? '') : '';
                    $phone = isset($columnMap['手机号']) ? trim($row[$columnMap['手机号']] ?? '') : '';
                    $password = isset($columnMap['密码']) ? trim($row[$columnMap['密码']] ?? '') : '';
                    $username = isset($columnMap['用户名']) ? trim($row[$columnMap['用户名']] ?? '') : '';
                    $points = isset($columnMap['积分']) ? intval($row[$columnMap['积分']] ?? 0) : 0;
                    $roleValue = isset($columnMap['角色']) ? trim($row[$columnMap['角色']] ?? '') : '';
                    $statusValue = isset($columnMap['状态']) ? trim($row[$columnMap['状态']] ?? '') : '';
                    $remark = isset($columnMap['备注']) ? trim($row[$columnMap['备注']] ?? '') : '';

                    // 如果没有提供密码，生成默认密码
                    if (empty($password)) {
                        $password = 'user123456'; // 默认密码
                    }

                    // 处理角色
                    $isAdmin = in_array(strtolower($roleValue), ['admin', '管理员', '1', 'true']);

                    // 处理状态 - 默认启用
                    $isActive = !in_array(strtolower($statusValue), ['inactive', '禁用', '0', 'false']);

                    // 验证必填字段
                    if (empty($name)) {
                        throw new \Exception('姓名不能为空');
                    }

                    // 验证密码（现在密码已经有默认值，只需验证长度）
                    if (strlen($password) < 6) {
                        throw new \Exception('密码长度不能少于6位');
                    }

                    // 邮箱和手机号至少要有一个
                    if (empty($email) && empty($phone)) {
                        throw new \Exception('邮箱和手机号至少要填写一个');
                    }

                    // 验证邮箱格式（如果提供）
                    if (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
                        throw new \Exception('邮箱格式不正确');
                    }

                    // 验证手机号格式（如果提供）
                    if (!empty($phone) && !preg_match('/^1[3-9]\d{9}$/', $phone)) {
                        throw new \Exception('手机号格式不正确');
                    }

                    // 检查邮箱是否已存在
                    if (!empty($email) && User::where('email', $email)->exists()) {
                        throw new \Exception('邮箱已存在');
                    }

                    // 检查用户名是否已存在
                    if (!empty($username) && User::where('username', $username)->exists()) {
                        throw new \Exception('用户名已存在');
                    }

                    // 检查手机号是否已存在
                    if (!empty($phone) && User::where('phone', $phone)->exists()) {
                        throw new \Exception('手机号已存在');
                    }

                    // 创建用户
                    $userData = [
                        'name' => $name,
                        'email' => $email ?: null,
                        'username' => $username ?: null,
                        'phone' => $phone ?: null,
                        'password' => Hash::make($password),
                        'points' => $points,
                        'is_admin' => $isAdmin,
                        'is_active' => $isActive,
                        'remark' => $remark ?: null,
                        'email_verified_at' => now(), // 导入的用户默认邮箱已验证
                    ];

                    $user = User::create($userData);

                    // 如果有积分，创建对应的积分记录
                    if ($points > 0) {
                        \App\Models\PointRecord::create([
                            'user_id' => $user->id,
                            'points' => $points,
                            'type' => 'manual',
                            'description' => '用户导入初始积分',
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                    }

                    $importCount++;

                    Log::info('用户导入成功', [
                        'row' => $rowNumber,
                        'name' => $name,
                        'email' => $email,
                        'phone' => $phone,
                        'points' => $points,
                        'user_id' => $user->id
                    ]);

                } catch (\Exception $e) {
                    $errorCount++;
                    $errorMessage = "第{$rowNumber}行: {$e->getMessage()}";
                    $errorRows[] = $errorMessage;

                    Log::warning('用户导入行失败', [
                        'row' => $rowNumber,
                        'error' => $e->getMessage(),
                        'data' => $row
                    ]);
                }
            }

            fclose($handle); // 关闭文件句柄

            // 清理临时文件
            if ($tempPath && file_exists($tempPath)) {
                unlink($tempPath);
            }

            // 如果所有行都有错误，回滚事务并返回错误
            if ($importCount == 0 && $errorCount > 0) {
                DB::rollBack();
                return back()->withErrors(['file' => '导入失败，所有行都存在问题。'])->with('errorRows', $errorRows);
            }

            // 提交事务
            DB::commit();

            // 返回成功消息，包含导入统计信息
            $message = "成功导入 {$importCount} 个用户";
            if ($errorCount > 0) {
                $message .= "，{$errorCount} 行记录导入失败";
            }

            // 记录导入日志
            Log::info('用户批量导入完成', [
                'import_count' => $importCount,
                'error_count' => $errorCount,
                'admin_user' => auth()->user()->name ?? 'unknown'
            ]);

            // 重定向回导入页面，显示结果
            return redirect()->route('admin.points.users.import.show')
                ->with('success', $message)
                ->with('import_stats', [
                    'total_processed' => $importCount + $errorCount,
                    'success_count' => $importCount,
                    'error_count' => $errorCount
                ])
                ->with('errorRows', $errorRows);

        } catch (\Exception $e) {
            DB::rollBack();

            if ($handle) {
                fclose($handle);
            }

            // 清理临时文件
            if ($tempPath && file_exists($tempPath)) {
                unlink($tempPath);
            }

            throw $e;
        }
    }

    /**
     * 下载用户导入模板
     */
    public function downloadTemplate(Request $request)
    {
        $format = $request->get('format', 'csv');

        if ($format === 'excel') {
            return $this->downloadExcelTemplate();
        } else {
            return $this->downloadCsvTemplate();
        }
    }

    /**
     * 下载CSV模板
     */
    private function downloadCsvTemplate()
    {
        $headers = [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="user_import_template.csv"',
        ];

        $callback = function() {
            $file = fopen('php://output', 'w');

            // 添加BOM以支持Excel正确显示中文
            fwrite($file, "\xEF\xBB\xBF");

            // 写入表头
            fputcsv($file, ['姓名', '邮箱', '手机号', '密码', '用户名', '积分', '角色', '状态', '备注']);

            // 写入示例数据
            fputcsv($file, ['张三', '<EMAIL>', '13800138000', '123456', 'zhangsan', '100', '普通用户', '启用', '导入的测试用户']);
            fputcsv($file, ['李四', '', '13800138001', '', 'lisi', '200', '管理员', '启用', '只有手机号的用户']);
            fputcsv($file, ['王五', '<EMAIL>', '', 'mypassword', '', '50', '普通用户', '启用', '只有邮箱的用户']);

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * 下载Excel模板
     */
    private function downloadExcelTemplate()
    {
        // 检查是否安装了PhpSpreadsheet
        if (!class_exists('\PhpOffice\PhpSpreadsheet\Spreadsheet')) {
            return $this->downloadCsvTemplate(); // 降级到CSV
        }

        try {
            $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();

            // 设置表头
            $headers = ['姓名', '邮箱', '手机号', '密码', '用户名', '积分', '角色', '状态', '备注'];
            $sheet->fromArray($headers, null, 'A1');

            // 添加示例数据
            $exampleData = [
                ['张三', '<EMAIL>', '13800138000', '123456', 'zhangsan', 100, '普通用户', '启用', '导入的测试用户'],
                ['李四', '', '13800138001', '', 'lisi', 200, '管理员', '启用', '只有手机号的用户'],
                ['王五', '<EMAIL>', '', 'mypassword', '', 50, '普通用户', '启用', '只有邮箱的用户'],
            ];
            $sheet->fromArray($exampleData, null, 'A2');

            // 设置列宽
            foreach (range('A', 'I') as $column) {
                $sheet->getColumnDimension($column)->setAutoSize(true);
            }

            // 创建Writer
            $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);

            // 设置响应头
            $headers = [
                'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'Content-Disposition' => 'attachment; filename="user_import_template.xlsx"',
                'Cache-Control' => 'max-age=0',
            ];

            return response()->stream(function() use ($writer) {
                $writer->save('php://output');
            }, 200, $headers);

        } catch (\Exception $e) {
            Log::error('生成Excel模板失败: ' . $e->getMessage());
            return $this->downloadCsvTemplate(); // 降级到CSV
        }
    }
}