<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\Order;
use App\Models\PointExchangeRecord;
use App\Models\PointRecord;
use App\Models\Product;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AnalyticsController extends Controller
{
    /**
     * 显示数据分析首页
     * 只负责渲染视图，数据通过 AJAX 从 getData 获取
     */
    public function index(Request $request)
    {
        // 传递默认日期给视图，让 JS 用于首次加载
        $defaultStartDate = $request->get('start_date', now()->subDays(29)->toDateString());
        $defaultEndDate = $request->get('end_date', now()->toDateString());

        return view('admin.analytics.index', compact('defaultStartDate', 'defaultEndDate'));
    }

    /**
     * 获取所有分析数据以供前端 AJAX 调用
     */
    public function getData(Request $request)
    {
        $startDate = Carbon::parse($request->get('start_date', now()->subDays(29)))->startOfDay();
        $endDate = Carbon::parse($request->get('end_date', now()))->endOfDay();

        // 确保日期间隔内每天都有数据点
        $period = new \DatePeriod(
            $startDate,
            new \DateInterval('P1D'),
            $endDate->copy()->addDay() // Include the end date
        );

        $dateMap = [];
        foreach ($period as $date) {
            $dateString = $date->format('Y-m-d');
            $dateMap[$dateString] = 0;
        }

        // --- User Analytics ---
        $newUsersData = User::select(
            DB::raw('DATE(created_at) as date'),
            DB::raw('COUNT(*) as count')
        )
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->keyBy('date');

        $newUsers = array_map(function ($date, $default) use ($newUsersData) {
            return (object) [
                'date' => $date,
                'count' => $newUsersData[$date]->count ?? $default
            ];
        }, array_keys($dateMap), array_values($dateMap));

        // 活跃用户统计 (基于积分记录活动)
        $activeUsersData = PointRecord::select(
            DB::raw('DATE(created_at) as date'),
            DB::raw('COUNT(DISTINCT user_id) as count')
        )
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->keyBy('date');

        $activeUsers = array_map(function ($date, $default) use ($activeUsersData) {
            return (object) [
                'date' => $date,
                'count' => $activeUsersData[$date]->count ?? $default
            ];
        }, array_keys($dateMap), array_values($dateMap));

        $userBehaviors = Order::select(
            'payment_method',
            DB::raw('COUNT(*) as count')
        )
            ->where('status', Order::STATUS_COMPLETED) // Only completed orders
            ->whereBetween('created_at', [$startDate, $endDate])
            ->whereNotNull('payment_method')
            ->groupBy('payment_method')
            ->get()
            ->map(function ($item) { // Map payment method names here
                $item->payment_method_label = Order::getPaymentMethodLabel($item->payment_method);
                return $item;
            });


        // --- Sales Analytics (基于积分兑换记录) ---
        $salesData = collect();
        if (DB::table('point_exchange_records')->exists()) {
            $salesData = DB::table('point_exchange_records')
                ->select(
                    DB::raw('DATE(created_at) as date'),
                    DB::raw('SUM(points) as amount')
                )
                ->where('status', 'completed')
                ->whereBetween('created_at', [$startDate, $endDate])
                ->groupBy('date')
                ->orderBy('date')
                ->get()
                ->keyBy('date');
        }

        $sales = array_map(function ($date, $default) use ($salesData) {
            return (object) [
                'date' => $date,
                'amount' => $salesData[$date]->amount ?? $default
            ];
        }, array_keys($dateMap), array_values($dateMap));


        // 兑换方式统计（基于积分兑换记录的状态）
        $paymentMethods = collect();
        if (DB::table('point_exchange_records')->exists()) {
            $paymentMethods = DB::table('point_exchange_records')
                ->select(
                    'status as payment_method',
                    DB::raw('COUNT(*) as count'),
                    DB::raw('SUM(points) as amount')
                )
                ->whereBetween('created_at', [$startDate, $endDate])
                ->groupBy('status')
                ->orderBy('amount', 'desc')
                ->get()
                ->map(function ($item) {
                    $statusNames = [
                        'pending' => '待处理',
                        'completed' => '已完成',
                        'cancelled' => '已取消',
                        'processing' => '处理中'
                    ];
                    $item->payment_method_label = $statusNames[$item->payment_method] ?? $item->payment_method;
                    return $item;
                });
        }


        // 热门兑换商品（基于积分兑换记录）
        $hotProducts = collect();
        if (DB::table('point_exchange_records')->exists() && DB::table('products')->exists()) {
            $hotProducts = DB::table('point_exchange_records')
                ->join('products', 'products.id', '=', 'point_exchange_records.product_id')
                ->select(
                    'products.name',
                    'products.id',
                    DB::raw('SUM(point_exchange_records.quantity) as total_quantity'),
                    DB::raw('SUM(point_exchange_records.points) as total_amount')
                )
                ->where('point_exchange_records.status', 'completed')
                ->whereBetween('point_exchange_records.created_at', [$startDate, $endDate])
                ->groupBy('products.id', 'products.name')
                ->orderByDesc('total_quantity')
                ->limit(10)
                ->get();
        }


        // --- Points Analytics ---
        $pointsIssuedData = PointRecord::select(
            DB::raw('DATE(created_at) as date'),
            DB::raw('SUM(points) as points')
        )
            ->where('points', '>', 0)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->keyBy('date');

        $pointsIssued = array_map(function ($date, $default) use ($pointsIssuedData) {
            return (object) [
                'date' => $date,
                'points' => $pointsIssuedData[$date]->points ?? $default
            ];
        }, array_keys($dateMap), array_values($dateMap));

        // Points Consumed Trend (Using PointRecord for consistency)
        $pointsConsumedData = PointRecord::select(
            DB::raw('DATE(created_at) as date'),
            DB::raw('SUM(ABS(points)) as points') // Use ABS to get positive value for consumption
        )
            ->where('points', '<', 0)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->keyBy('date');

        $pointsConsumed = array_map(function ($date, $default) use ($pointsConsumedData) {
            return (object) [
                'date' => $date,
                'points' => $pointsConsumedData[$date]->points ?? $default
            ];
        }, array_keys($dateMap), array_values($dateMap));


        // Points Category Distribution (基于积分兑换记录的商品分类)
        $categoryDistribution = collect();
        if (DB::table('point_exchange_records')->exists() && DB::table('products')->exists() && DB::table('categories')->exists()) {
            $categoryDistribution = DB::table('point_exchange_records')
                ->join('products', 'point_exchange_records.product_id', '=', 'products.id')
                ->join('categories', 'products.category_id', '=', 'categories.id')
                ->select(
                    'categories.name as category_name',
                    DB::raw('COUNT(point_exchange_records.id) as exchange_count'),
                    DB::raw('SUM(point_exchange_records.points) as total_points')
                )
                ->where('point_exchange_records.status', 'completed')
                ->whereBetween('point_exchange_records.created_at', [$startDate, $endDate])
                ->groupBy('categories.id', 'categories.name')
                ->orderByDesc('exchange_count')
                ->get();
        }

        // Points Product Ranking (基于积分兑换记录的商品排行)
        $productRanking = collect();
        if (DB::table('point_exchange_records')->exists() && DB::table('products')->exists()) {
            $productRanking = DB::table('point_exchange_records')
                ->join('products', 'point_exchange_records.product_id', '=', 'products.id')
                ->select(
                    'products.name as product_name',
                    'products.id as product_id',
                    DB::raw('SUM(point_exchange_records.quantity) as total_quantity'),
                    DB::raw('SUM(point_exchange_records.points) as total_points')
                )
                ->where('point_exchange_records.status', 'completed')
                ->whereBetween('point_exchange_records.created_at', [$startDate, $endDate])
                ->groupBy('products.id', 'products.name')
                ->orderByDesc('total_points')
                ->limit(10)
                ->get();
        }

        // 用户行为统计（基于积分记录）
        $userBehaviors = collect([
            (object) ['behavior' => '积分获得', 'count' => PointRecord::where('points', '>', 0)->whereBetween('created_at', [$startDate, $endDate])->count()],
            (object) ['behavior' => '积分消费', 'count' => PointRecord::where('points', '<', 0)->whereBetween('created_at', [$startDate, $endDate])->count()],
        ]);

        // --- Consolidate Response Data ---
        $responseData = [
            'userAnalytics' => [
                'newUsers' => $newUsers,
                'activeUsers' => $activeUsers,
                'userBehaviors' => $userBehaviors,
            ],
            'salesAnalytics' => [
                'sales' => $sales,
                'paymentMethods' => $paymentMethods,
                'hotProducts' => $hotProducts,
            ],
            'pointsAnalytics' => [
                'issued' => $pointsIssued,
                'consumed' => $pointsConsumed,
                'categoryDistribution' => $categoryDistribution,
                'productRanking' => $productRanking,
            ],
            'startDate' => $startDate->toDateString(),
            'endDate' => $endDate->toDateString(),
        ];

        return response()->json($responseData);
    }

    /*
     * Removed export method stub for now.
     * If needed later, it can be implemented to call getData or use specific queries.
     */
}