<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\PointExchangeRecord;
use App\Models\Product;
use App\Models\User;
use App\Models\Admin;
use App\Models\PointRecord;
use App\Models\UserAddress;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use App\Services\OrderNumberGenerator;
use App\Models\PointExchange;
use App\Notifications\PointExchangeNotification;
use App\Notifications\NewPointExchangeOrderNotification;

// 尝试引入Sentry类，如不存在则忽略
use Sentry\Tracing\SpanStatus;

class PointExchangeController extends Controller
{
    /**
     * 显示兑换确认页面
     *
     * @param Request $request
     * @param int $productId
     * @return \Illuminate\View\View
     */
    public function showConfirmPage(Request $request, int $productId)
    {
        $product = Product::findOrFail($productId);

        if (!$product->is_points_product) {
            return back()->with('error', '该商品不支持积分兑换');
        }

        if ($product->stock <= 0) {
            return back()->with('error', '商品库存不足');
        }

        // 查询对应的积分兑换规则
        $pointExchange = PointExchange::where('product_id', $product->id)
            ->where('is_active', true)
            ->firstOrFail();

        $user = auth()->user();

        // 获取用户所有地址
        $addresses = UserAddress::where('user_id', $user->id)->get();

        // 获取默认地址
        $address = $addresses->where('is_default', true)->first();

        return view('points.exchange.confirm', [
            'product' => $product,
            'pointExchange' => $pointExchange,
            'address' => $address,
            'addresses' => $addresses,
            'userPoints' => $user->points,
            'canExchange' => $user->points >= $pointExchange->points_required
        ]);
    }

    /**
     * 处理积分兑换
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function exchange(Request $request)
    {
        try {
            // 验证请求数据
            $validatedData = $request->validate([
                'product_id' => 'required|exists:products,id',
                'address_id' => 'required|exists:user_addresses,id',
                'quantity' => 'required|integer|min:1'
            ]);

            // 记录兑换开始
            $transactionContext = new \Sentry\Tracing\TransactionContext();
            $transactionContext->setName('积分兑换流程');
            $transactionContext->setOp('points.exchange');

            // 开始事务
            $transaction = \Sentry\startTransaction($transactionContext);

            // 设置事务数据
            \Sentry\configureScope(function (\Sentry\State\Scope $scope) use ($validatedData): void {
                $scope->setTag('product_id', (string)$validatedData['product_id']);
                $scope->setTag('address_id', (string)$validatedData['address_id']);
                $scope->setTag('quantity', (string)$validatedData['quantity']);
                $scope->setTag('user_id', (string)auth()->id());
            });

            // 获取所需数据
            $product = Product::findOrFail($validatedData['product_id']);
            $user = auth()->user();
            $address = UserAddress::findOrFail($validatedData['address_id']);
            $quantity = (int)$validatedData['quantity'];

            // 查询对应的积分兑换规则
            $pointExchange = PointExchange::where('product_id', $product->id)
                ->where('is_active', true)
                ->firstOrFail();

            // 计算总消耗积分
            $totalPointsRequired = $pointExchange->points_required * $quantity;

            // 获取收货地址
            $address = UserAddress::findOrFail($request->address_id);

            // 记录详细的地址信息用于调试
            Log::info('兑换使用的地址信息', [
                'address_id' => $address->id,
                'user_id' => $address->user_id,
                'contact_name' => $address->contact_name,
                'contact_phone' => $address->contact_phone,
                'province' => $address->province,
                'city' => $address->city,
                'district' => $address->district,
                'detail' => $address->detail,
                'full_address' => $address->province . $address->city . $address->district . ' ' . $address->detail
            ]);

            // 添加Sentry的用户上下文
            \Sentry\configureScope(function (\Sentry\State\Scope $scope) use ($user, $product, $address, $pointExchange, $quantity, $totalPointsRequired): void {
                $scope->setUser([
                    'id' => $user->id,
                    'username' => $user->name,
                    'email' => $user->email,
                    'points' => (string)$user->points
                ]);

                $scope->setContext('product', [
                    'id' => $product->id,
                    'name' => $product->name,
                    'points_price' => $product->points_price,
                    'stock' => $product->stock,
                    'is_points_product' => $product->is_points_product
                ]);

                $scope->setContext('exchange', [
                    'id' => $pointExchange->id,
                    'points_required' => $pointExchange->points_required,
                    'total_points' => $totalPointsRequired,
                    'quantity' => $quantity,
                    'stock' => $pointExchange->stock
                ]);

                $scope->setContext('address', [
                    'id' => $address->id,
                    'name' => $address->contact_name,
                    'phone' => $address->contact_phone,
                    'province' => $address->province,
                    'city' => $address->city
                ]);
            });

            // 检查能否兑换
            if (!$product->is_points_product) {
                \Sentry\captureMessage('用户尝试兑换非积分商品: Product ID ' . $product->id);
                return back()->with('error', '该商品不支持积分兑换');
            }

            if ($product->stock < $quantity) {
                \Sentry\captureMessage('用户尝试兑换库存不足商品: Product ID ' . $product->id . ', Stock: ' . $product->stock . ', Requested: ' . $quantity);
                return back()->with('error', '商品库存不足');
            }

            if ($user->points < $totalPointsRequired) {
                \Sentry\captureMessage('用户积分不足: User ID ' . $user->id . ', Points: ' . $user->points . ', Needed: ' . $totalPointsRequired);
                return back()->with('error', '您的积分不足，无法完成兑换');
            }

            \Sentry\addBreadcrumb(new \Sentry\Breadcrumb(
                \Sentry\Breadcrumb::LEVEL_INFO,
                \Sentry\Breadcrumb::TYPE_DEFAULT,
                'exchange_validation',
                '积分兑换前验证通过',
                [
                    'user_points' => $user->points,
                    'required_points' => $totalPointsRequired,
                    'product_stock' => $product->stock,
                    'quantity' => $quantity
                ]
            ));

            // 启动事务
            DB::beginTransaction();

            // 创建兑换订单
            $orderData = [
                'user_id' => $user->id,
                'exchange_id' => $pointExchange->id,
                'product_id' => $product->id, // 添加产品ID，确保产品关联正确
                'order_no' => OrderNumberGenerator::generatePointExchangeNumber() ?? 'PE' . date('YmdHis') . rand(1000, 9999),
                'points' => $totalPointsRequired,
                'quantity' => $quantity,
                'status' => 'pending',
                'shipping_name' => $address->contact_name,
                'shipping_phone' => $address->contact_phone,
                'shipping_address' => $address->province . $address->city . $address->district . ' ' . $address->detail,
            ];

            // 记录即将创建的订单数据
            Log::info('即将创建的订单数据', $orderData);

            $order = PointExchangeRecord::create($orderData);

            // 验证订单是否创建成功以及是否包含地址信息
            $savedOrder = PointExchangeRecord::find($order->id);
            Log::info('订单创建后的数据', [
                'order_id' => $savedOrder->id,
                'order_no' => $savedOrder->order_no,
                'shipping_name' => $savedOrder->shipping_name,
                'shipping_phone' => $savedOrder->shipping_phone,
                'shipping_address' => $savedOrder->shipping_address
            ]);

            // 扣减用户积分
            $originalPoints = $user->points;
            $user->decrement('points', $totalPointsRequired);

            \Sentry\addBreadcrumb(new \Sentry\Breadcrumb(
                \Sentry\Breadcrumb::LEVEL_INFO,
                \Sentry\Breadcrumb::TYPE_DEFAULT,
                'points_deducted',
                '用户积分已扣减',
                [
                    'original_points' => $originalPoints,
                    'deducted_points' => $totalPointsRequired,
                    'remaining_points' => $user->points
                ]
            ));

            // 记录积分变更
            PointRecord::create([
                'user_id' => $user->id,
                'points' => -$totalPointsRequired,
                'description' => '兑换商品：' . $product->name . ' x ' . $quantity,
                'type' => 'exchange'
            ]);

            // 扣减商品库存
            $originalStock = $product->stock;
            $product->decrement('stock', $quantity);

            \Sentry\addBreadcrumb(new \Sentry\Breadcrumb(
                \Sentry\Breadcrumb::LEVEL_INFO,
                \Sentry\Breadcrumb::TYPE_DEFAULT,
                'stock_deducted',
                '商品库存已扣减',
                [
                    'original_stock' => $originalStock,
                    'deducted_quantity' => $quantity,
                    'remaining_stock' => $product->stock
                ]
            ));

            // 提交事务
            DB::commit();

            // 通知管理员新的积分兑换订单已创建
            $this->notifyAdminsAboutNewOrder($order);

            // 完成Sentry事务
            if (isset($transaction)) {
                $this->safeSetTransactionStatus($transaction, 'ok');
                $transaction->finish();
            }

            // 发送兑换成功通知
            try {
                $user->notify(new PointExchangeNotification($order, 'created'));

                // 记录通知发送日志
                Log::info('兑换成功通知已发送', [
                    'user_id' => $user->id,
                    'order_id' => $order->id,
                    'order_no' => $order->order_no
                ]);
            } catch (\Exception $e) {
                Log::error('发送兑换成功通知失败', [
                    'error' => $e->getMessage(),
                    'user_id' => $user->id,
                    'order_id' => $order->id
                ]);
                // 通知发送失败不影响主流程
            }

            // 页面重定向到成功页
            return redirect()->route('points.exchange.success', ['order' => $order->id])
                ->with('success', '商品兑换成功！');

        } catch (\Illuminate\Validation\ValidationException $e) {
            \Sentry\captureException($e);
            return back()->withErrors($e->errors())->withInput();
        } catch (\Exception $e) {
            // 回滚事务
            if (DB::transactionLevel() > 0) {
                DB::rollBack();
            }

            // 捕获异常到Sentry
            \Sentry\captureException($e);

            // 记录错误日志
            Log::error('积分兑换失败：' . $e->getMessage(), [
                'user_id' => auth()->id(),
                'product_id' => $request->product_id ?? null,
                'address_id' => $request->address_id ?? null,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'exception_class' => get_class($e)
            ]);

            // 返回错误信息
            return back()->with('error', '兑换失败：' . $e->getMessage());
        }
    }

    /**
     * 显示兑换历史
     */
    public function history(Request $request)
    {
        $user = Auth::user();

        $query = PointExchangeRecord::byUser($user->id)
            ->with(['product', 'exchange.product'])
            ->orderBy('created_at', 'desc');

        // 状态筛选
        if ($request->filled('status')) {
            $query->byStatus($request->status);
        }

        // 日期筛选
        if ($request->filled('start_date')) {
            $query->whereDate('created_at', '>=', $request->start_date);
        }
        if ($request->filled('end_date')) {
            $query->whereDate('created_at', '<=', $request->end_date);
        }

        // 商品名称搜索
        if ($request->filled('search')) {
            $query->whereHas('product', function($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%');
            });
        }

        // 订单号搜索
        if ($request->filled('order_no')) {
            $query->where('order_no', 'like', '%' . $request->order_no . '%');
        }

        $orders = $query->paginate(15);

        // 统计数据
        $statistics = [
            'total' => PointExchangeRecord::byUser($user->id)->count(),
            'pending' => PointExchangeRecord::byUser($user->id)->byStatus('pending')->count(),
            'shipped' => PointExchangeRecord::byUser($user->id)->byStatus('shipped')->count(),
            'completed' => PointExchangeRecord::byUser($user->id)->byStatus('completed')->count(),
            'cancelled' => PointExchangeRecord::byUser($user->id)->byStatus('cancelled')->count(),
        ];

        return view('points.exchange.history', compact('orders', 'statistics'));
    }

    /**
     * 订单详情页面
     *
     * @param PointExchangeRecord $order
     * @return \Illuminate\View\View
     */
    public function orderDetail(PointExchangeRecord $order)
    {
        // 检查访问权限
        if ($order->user_id !== auth()->id()) {
            abort(403, '您没有权限查看此订单');
        }

        // 添加面包屑
        \Sentry\addBreadcrumb(
            new \Sentry\Breadcrumb(
                \Sentry\Breadcrumb::LEVEL_INFO,
                \Sentry\Breadcrumb::TYPE_DEFAULT,
                'order_detail_view',
                '用户查看积分兑换订单详情',
                [
                    'order_id' => $order->id,
                    'order_no' => $order->order_no,
                    'status' => $order->status
                ]
            )
        );

        return view('orders.point_exchange_detail', [
            'order' => $order->load('exchange.product', 'user')
        ]);
    }

    /**
     * 根据订单号查询订单详情
     *
     * @param string $orderNumber
     * @return \Illuminate\View\View
     */
    public function orderDetailByNumber(string $orderNumber)
    {
        try {
            // 检查是否为纯数字ID
            if (is_numeric($orderNumber)) {
                // 尝试通过ID查找订单
                $orderById = PointExchangeRecord::where('id', $orderNumber)
                    ->where('user_id', auth()->id())
                    ->first();

                if ($orderById) {
                    // 记录Sentry信息
                    \Sentry\addBreadcrumb(
                        new \Sentry\Breadcrumb(
                            \Sentry\Breadcrumb::LEVEL_INFO,
                            \Sentry\Breadcrumb::TYPE_DEFAULT,
                            'order_id_redirect',
                            '用户使用ID访问订单，正在重定向到正确的订单号页面',
                            [
                                'order_id' => $orderById->id,
                                'order_no' => $orderById->order_no
                            ]
                        )
                    );

                    // 重定向到正确的订单号URL
                    return redirect()->route('points.exchange.order.detail', $orderById->order_no);
                }
            }

            // 验证订单号格式 - PX开头的订单号
            if (!preg_match('/^PX\d{14}[A-Za-z0-9]+$/', $orderNumber)) {
                \Sentry\captureMessage('无效的订单号格式: ' . $orderNumber);
                abort(404, '无效的订单号格式');
            }

            // 根据订单号查询订单
            $order = PointExchangeRecord::where('order_no', $orderNumber)
                ->where('user_id', auth()->id())
                ->firstOrFail();

            // 记录查询成功
            Log::info('通过订单号查询订单成功', [
                'order_no' => $orderNumber,
                'user_id' => auth()->id(),
                'order_id' => $order->id
            ]);

            // 添加面包屑
            \Sentry\addBreadcrumb(
                new \Sentry\Breadcrumb(
                    \Sentry\Breadcrumb::LEVEL_INFO,
                    \Sentry\Breadcrumb::TYPE_DEFAULT,
                    'order_detail_by_number',
                    '用户通过订单号查看积分兑换订单详情',
                    [
                        'order_id' => $order->id,
                        'order_no' => $order->order_no,
                        'status' => $order->status
                    ]
                )
            );

            return view('orders.point_exchange_detail', [
                'order' => $order->load('exchange.product', 'user')
            ]);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            \Sentry\captureException($e);
            Log::error('订单不存在: ' . $orderNumber, ['user_id' => auth()->id()]);
            abort(404, '订单不存在或您无权查看');
        } catch (\Exception $e) {
            \Sentry\captureException($e);
            Log::error('查询订单详情出错: ' . $e->getMessage(), [
                'order_no' => $orderNumber,
                'user_id' => auth()->id(),
                'exception' => get_class($e)
            ]);
            abort(500, '系统错误，请稍后重试');
        }
    }

    /**
     * 取消兑换订单
     *
     * @param Request $request
     * @param string $orderNumber
     * @return \Illuminate\Http\JsonResponse
     */
    public function cancelOrder(Request $request, string $orderNumber)
    {
        $order = PointExchangeRecord::with(['product', 'user'])
            ->where('order_no', $orderNumber)
            ->where('user_id', auth()->id())
            ->firstOrFail();

        if ($order->status !== 'pending') {
            return response()->json(['message' => '订单状态不允许取消'], 422);
        }

        try {
            DB::beginTransaction();

            // 更新订单状态
            $order->update([
                'status' => 'cancelled',
                'cancelled_at' => now(),
                'cancel_reason' => '用户取消'
            ]);

            // 退还积分
            $order->user->increment('points', $order->points);

            // 创建积分记录
            \App\Models\PointRecord::create([
                'user_id' => $order->user_id,
                'points' => $order->points,
                'type' => 'refund',
                'description' => '取消兑换订单，退回积分：' . $order->order_no
            ]);

            // 恢复商品库存 - 添加空值检查
            if ($order->product) {
                $order->product->increment('stock');
            } else {
                // 记录日志，商品不存在但继续执行
                Log::warning('取消订单时无法恢复库存: 商品不存在', [
                    'order_no' => $order->order_no,
                    'product_id' => $order->product_id
                ]);

                // 添加Sentry监控
                if (class_exists('\Sentry\SentrySdk')) {
                    \Sentry\captureMessage('取消订单时无法恢复库存: 商品不存在', \Sentry\Severity::warning());
                    \Sentry\configureScope(function (\Sentry\State\Scope $scope) use ($order): void {
                        $scope->setContext('order', [
                            'id' => $order->id,
                            'order_no' => $order->order_no,
                            'product_id' => $order->product_id,
                            'points' => $order->points,
                            'user_id' => $order->user_id
                        ]);
                    });
                }
            }

            // 发送订单取消通知
            try {
                $order->user->notify(new PointExchangeNotification($order, 'cancelled'));

                // 记录通知发送日志
                Log::info('订单取消通知已发送', [
                    'user_id' => $order->user_id,
                    'order_id' => $order->id,
                    'order_no' => $order->order_no
                ]);
            } catch (\Exception $notificationError) {
                Log::error('发送订单取消通知失败', [
                    'error' => $notificationError->getMessage(),
                    'user_id' => $order->user_id,
                    'order_id' => $order->id
                ]);
            }

            DB::commit();

            return response()->json(['message' => '订单已取消']);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('取消兑换订单失败：' . $e->getMessage(), [
                'order_no' => $orderNumber,
                'exception' => get_class($e),
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // 添加Sentry监控
            if (class_exists('\Sentry\SentrySdk')) {
                \Sentry\captureException($e);
            }

            return response()->json(['message' => '取消失败，请稍后重试'], 500);
        }
    }

    /**
     * 确认收货
     *
     * @param Request $request
     * @param string $orderNumber
     * @return \Illuminate\Http\Response
     */
    public function confirmReceived(Request $request, string $orderNumber)
    {
        try {
            // 创建Sentry事务
            $transaction = null;
            if (class_exists('\Sentry\SentrySdk')) {
                $transactionContext = new \Sentry\Tracing\TransactionContext();
                $transactionContext->setName('确认收货API');
                $transactionContext->setOp('points.exchange.order.confirm');
                $transaction = \Sentry\startTransaction($transactionContext);

                // 记录基本信息
                \Sentry\configureScope(function (\Sentry\State\Scope $scope) use ($orderNumber, $request): void {
                    $scope->setTag('order_no', $orderNumber);
                    $scope->setTag('user_ip', $request->ip());
                    $scope->setTag('user_agent', $request->userAgent());
                    $scope->setUser([
                        'id' => auth()->id(),
                        'username' => auth()->user()->name ?? '未知用户',
                        'ip_address' => $request->ip()
                    ]);
                    $scope->setContext('request', [
                        'url' => $request->fullUrl(),
                        'method' => $request->method(),
                        'headers' => $request->headers->all(),
                        'timestamp' => now()->toIso8601String()
                    ]);
                });

                \Sentry\addBreadcrumb(
                    new \Sentry\Breadcrumb(
                        \Sentry\Breadcrumb::LEVEL_INFO,
                        \Sentry\Breadcrumb::TYPE_HTTP,
                        'http.request',
                        '接收到确认收货请求',
                        [
                            'order_no' => $orderNumber,
                            'request_method' => $request->method(),
                            'request_url' => $request->fullUrl(),
                            'timestamp' => now()->toIso8601String()
                        ]
                    )
                );
            }

            $order = PointExchangeRecord::where('order_no', $orderNumber)
                ->where('user_id', auth()->id())
                ->firstOrFail();

            // 记录订单状态检查
            if (class_exists('\Sentry\SentrySdk')) {
                \Sentry\addBreadcrumb(
                    new \Sentry\Breadcrumb(
                        \Sentry\Breadcrumb::LEVEL_INFO,
                        \Sentry\Breadcrumb::TYPE_DEFAULT,
                        'order.status_check',
                        '检查订单状态',
                        [
                            'order_no' => $orderNumber,
                            'order_id' => $order->id,
                            'current_status' => $order->status,
                            'expected_status' => 'shipped',
                            'check_time' => now()->toIso8601String()
                        ]
                    )
                );
            }

            if ($order->status !== 'shipped') {
                // 记录状态验证失败
                if (class_exists('\Sentry\SentrySdk')) {
                    \Sentry\captureMessage(
                        '用户尝试确认非发货状态的订单: ' . $orderNumber . ', 当前状态: ' . $order->status,
                        \Sentry\Severity::warning()
                    );

                    if ($transaction) {
                        $this->safeSetTransactionStatus($transaction, 'internal_error');
                        $transaction->finish();
                    }
                }

                if ($request->wantsJson()) {
                    return response()->json(['message' => '订单状态不允许确认收货'], 422);
                }

                return redirect()->back()->with('error', '订单状态不允许确认收货');
            }

            // 获取用户当前积分，用于后续调试
            $user = auth()->user();
            $beforePoints = $user->points;

            // 记录开始更新状态
            if (class_exists('\Sentry\SentrySdk')) {
                \Sentry\addBreadcrumb(
                    new \Sentry\Breadcrumb(
                        \Sentry\Breadcrumb::LEVEL_INFO,
                        \Sentry\Breadcrumb::TYPE_DEFAULT,
                        'order.update_start',
                        '开始更新订单状态',
                        [
                            'order_no' => $orderNumber,
                            'order_id' => $order->id,
                            'from_status' => $order->status,
                            'to_status' => 'completed',
                            'update_time' => now()->toIso8601String()
                        ]
                    )
                );
            }

            $order->update([
                'status' => 'completed',
                'completed_at' => now()
            ]);

            // 发送确认收货通知
            try {
                auth()->user()->notify(new PointExchangeNotification($order, 'completed'));

                // 记录通知发送日志
                Log::info('订单完成通知已发送', [
                    'user_id' => auth()->id(),
                    'order_id' => $order->id,
                    'order_no' => $order->order_no
                ]);
            } catch (\Exception $notificationError) {
                Log::error('发送订单完成通知失败', [
                    'error' => $notificationError->getMessage(),
                    'user_id' => auth()->id(),
                    'order_id' => $order->id
                ]);
            }

            // 使用fresh()获取最新数据
            $order = $order->fresh();

            // 记录状态更新完成
            if (class_exists('\Sentry\SentrySdk')) {
                \Sentry\addBreadcrumb(
                    new \Sentry\Breadcrumb(
                        \Sentry\Breadcrumb::LEVEL_INFO,
                        \Sentry\Breadcrumb::TYPE_DEFAULT,
                        'order.update_complete',
                        '订单状态更新完成',
                        [
                            'order_no' => $orderNumber,
                            'order_id' => $order->id,
                            'new_status' => $order->status,
                            'completed_at' => $order->completed_at,
                            'update_result' => ($order->status === 'completed' ? '成功' : '失败')
                        ]
                    )
                );
            }

            // 添加更多上下文便于调试
            \Sentry\configureScope(function (\Sentry\State\Scope $scope) use ($order, $beforePoints, $user): void {
                $scope->setContext('order', [
                    'id' => $order->id,
                    'order_no' => $order->order_no,
                    'product_id' => $order->product_id,
                    'points_cost' => $order->points,
                    'status' => $order->status,
                    'completed_at' => $order->completed_at
                ]);
                $scope->setContext('user', [
                    'id' => $user->id,
                    'points_before' => $beforePoints,
                    'points_after' => $user->points,
                ]);
            });

            // 完成交易记录
            if (class_exists('\Sentry\SentrySdk') && $transaction) {
                $this->safeSetTransactionStatus($transaction, 'ok');
                $transaction->finish();
            }

            if ($request->wantsJson()) {
                return response()->json(['message' => '确认收货成功']);
            }

            // 使用订单号进行重定向
            return redirect()->route('points.exchange.order.detail', $orderNumber)
                           ->with('success', '确认收货成功！订单已完成');

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            // 记录找不到订单错误
            if (class_exists('\Sentry\SentrySdk')) {
                \Sentry\captureException($e);

                if (isset($transaction)) {
                    $this->safeSetTransactionStatus($transaction, 'not_found');
                    $transaction->finish();
                }
            }

            Log::error('确认收货失败 - 找不到订单: ' . $orderNumber);

            if ($request->wantsJson()) {
                return response()->json(['message' => '订单不存在'], 404);
            }

            return redirect()->back()->with('error', '订单不存在');

        } catch (\Exception $e) {
            // 记录其他错误
            if (class_exists('\Sentry\SentrySdk')) {
                \Sentry\captureException($e);

                if (isset($transaction)) {
                    $this->safeSetTransactionStatus($transaction, 'internal_error');
                    $transaction->finish();
                }
            }

            Log::error('确认收货失败：' . $e->getMessage(), [
                'order_number' => $orderNumber,
                'user_id' => auth()->id(),
                'exception' => get_class($e),
                'trace' => $e->getTraceAsString()
            ]);

            if ($request->wantsJson()) {
                return response()->json(['message' => '确认收货失败，请稍后重试'], 500);
            }

            return redirect()->back()->with('error', '确认收货失败，请稍后重试');
        }
    }

    /**
     * 显示兑换成功页面
     */
    public function success(PointExchangeRecord $order)
    {
        try {
            $user = Auth::user();

            // 确保当前用户只能查看自己的订单
            if ($order->user_id !== $user->id) {
                \Sentry\captureMessage('用户尝试查看他人的订单: User ID ' . $user->id . ', Order User ID ' . $order->user_id);
                return redirect()->route('points.exchange.history')
                    ->with('error', '您无权查看该订单');
            }

            // 预加载所有必要的关联关系
            $order->load(['product', 'exchange.product', 'user']);

            // 检查产品信息 - 记录日志但不阻止查看
            if (!$order->product && !($order->exchange && $order->exchange->product)) {
                Log::info('积分兑换订单商品信息不完整', ['order_id' => $order->id, 'order_no' => $order->order_no]);
                // 不返回错误，继续展示订单
            }

            // 检查地址信息是否完整
            $addressComplete = $order->shipping_name && $order->shipping_phone && $order->shipping_address;

            if (!$addressComplete) {
                Log::info('积分兑换订单收货信息不完整', [
                    'order_id' => $order->id,
                    'order_no' => $order->order_no,
                    'shipping_name' => $order->shipping_name,
                    'shipping_phone' => $order->shipping_phone,
                    'shipping_address' => $order->shipping_address
                ]);
            }

            // 确保数量字段有值
            if (!$order->quantity || $order->quantity <= 0) {
                Log::info('积分兑换订单数量信息异常', ['order_id' => $order->id, 'order_no' => $order->order_no, 'quantity' => $order->quantity]);
                // 如果数量有问题，确保至少显示为1
                $order->quantity = max(1, intval($order->quantity));
            }

            // 获取用户当前积分
            $currentPoints = $user->points;

            // 计算兑换前的积分
            $beforePoints = $currentPoints + $order->points;

            // 设置Sentry上下文以便更好地理解页面渲染时的状态
            \Sentry\configureScope(function (\Sentry\State\Scope $scope) use ($order, $currentPoints, $beforePoints, $user, $addressComplete): void {
                $scope->setContext('order_details', [
                    'id' => $order->id,
                    'order_no' => $order->order_no,
                    'status' => $order->status,
                    'points' => $order->points,
                    'quantity' => $order->quantity,
                    'created_at' => $order->created_at->format('Y-m-d H:i:s'),
                    'product_id' => $order->product_id,
                    'product_name' => $order->product->name ?? ($order->exchange && $order->exchange->product ? $order->exchange->product->name : '商品信息不完整'),
                    'address_complete' => $addressComplete
                ]);

                $scope->setContext('user_points', [
                    'current_points' => $currentPoints,
                    'before_points' => $beforePoints,
                    'points_spent' => $order->points
                ]);

                $scope->setUser([
                    'id' => $user->id,
                    'username' => $user->name,
                    'email' => $user->email
                ]);
            });

            \Sentry\addBreadcrumb(new \Sentry\Breadcrumb(
                \Sentry\Breadcrumb::LEVEL_INFO,
                \Sentry\Breadcrumb::TYPE_DEFAULT,
                'success_page',
                '显示兑换成功页面',
                [
                    'order_id' => $order->id,
                    'order_no' => $order->order_no,
                    'points' => $order->points,
                    'current_points' => $currentPoints,
                    'before_points' => $beforePoints
                ]
            ));

            return view('points.exchange.success', [
                'order' => $order,
                'currentPoints' => $currentPoints,
                'beforePoints' => $beforePoints,
                'addressComplete' => $addressComplete
            ]);
        } catch (\Exception $e) {
            \Sentry\captureException($e);
            Log::error('显示兑换成功页面失败：' . $e->getMessage(), [
                'order_id' => $order->id ?? null,
                'user_id' => Auth::id(),
                'exception' => get_class($e),
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return redirect()->route('points.exchange.history')
                ->with('error', '显示订单详情失败，请稍后重试');
        }
    }

    /**
     * 安全设置Sentry事务状态
     *
     * @param mixed $transaction
     * @param string $status
     * @return void
     */
    private function safeSetTransactionStatus($transaction, string $status): void
    {
        if (!$transaction) {
            return;
        }

        try {
            if (class_exists('Sentry\Tracing\SpanStatus')) {
                // 尝试使用SpanStatus类
                switch ($status) {
                    case 'ok':
                        $transaction->setStatus(SpanStatus::ok());
                        break;
                    case 'internal_error':
                        $transaction->setStatus(SpanStatus::internalError());
                        break;
                    case 'not_found':
                        $transaction->setStatus(SpanStatus::notFound());
                        break;
                    default:
                        $transaction->setStatus(SpanStatus::unknownError());
                }
            } else {
                // 回退到使用字符串
                $transaction->setStatus($status);
            }
        } catch (\Throwable $e) {
            // 如果出错，使用字符串设置
            try {
                $transaction->setStatus($status);
            } catch (\Throwable $e) {
                // 完全失败时记录日志
                Log::warning('无法设置Sentry事务状态: ' . $e->getMessage());
            }
        }
    }

    /**
     * 通知管理员新的积分兑换订单已创建
     *
     * @param PointExchangeRecord $order
     * @return void
     */
    private function notifyAdminsAboutNewOrder(PointExchangeRecord $order): void
    {
        try {
            // 获取有权限的管理员（订单管理员和超级管理员）
            $admins = Admin::whereHas('roles', function ($query) {
                $query->whereIn('slug', ['super_admin', 'order_manager', 'point_manager']);
            })->get();

            // 确保订单关联了产品
            if (!$order->product && $order->product_id) {
                $order->load('product');
            }

            foreach ($admins as $admin) {
                $admin->notify(new NewPointExchangeOrderNotification($order));
            }

            // 记录日志
            Log::info('积分兑换订单通知已发送给管理员', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'admin_count' => count($admins)
            ]);
        } catch (\Exception $e) {
            Log::error('发送积分兑换订单通知失败', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
}