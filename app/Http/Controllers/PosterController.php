<?php

namespace App\Http\Controllers;

use App\Models\Shop;
use App\Models\ShopShare;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use PosterEditor\PosterEditor;

// 引入 endroid/qr-code 相关类
use Endroid\QrCode\QrCode;
use Endroid\QrCode\Writer\PngWriter;
use Endroid\QrCode\Encoding\Encoding;
use Endroid\QrCode\ErrorCorrectionLevel\ErrorCorrectionLevelHigh;

// 移除 Intervention Image Facade 引用
// use Intervention\Image\Facades\Image;

class PosterController extends Controller
{
    /**
     * 生成店铺海报 (恢复到基础版本，不进行二维码叠加)
     *
     * @param Request $request
     * @param int $shopId 店铺ID
     * @return \Illuminate\Http\Response
     */
    public function generateShopPoster(Request $request, $shopId = null)
    {
        try {
            // --- 获取 $shop 的逻辑 ---
            if (empty($shopId)) {
                $shop = Auth::user()->shops()->where('is_main', true)->first();

                if (!$shop) {
                    $shop = Auth::user()->shops()->first();
                }

                if (!$shop) {
                    return response()->json([
                        'success' => false,
                        'message' => '未找到店铺'
                    ], 404);
                }
            } else {
                $shop = Shop::findOrFail($shopId);

                // 检查当前用户是否有权限访问该店铺
                if ($shop->user_id !== Auth::id()) {
                    return response()->json([
                        'success' => false,
                        'message' => '无权限访问该店铺'
                    ], 403);
                }
            }
            // --- 获取 $shop 结束 ---

            // 直接调用 createShopPoster 生成基础海报 (内部二维码插入已注释)
            Log::info('开始生成基础海报 (恢复模式)', ['shop_id' => $shop->id]);
            $posterPath = $this->createShopPoster($shop);
            Log::info('基础海报生成完成 (恢复模式)', ['path' => $posterPath]);

            // 直接返回基础海报信息
            return response()->json([
                'success' => true,
                'data' => [
                    'poster_url' => asset('storage/' . $posterPath),
                    'shop_id' => $shop->id,
                    'shop_name' => $shop->name,
                    'logo' => $shop->logo ? shop_logo_url($shop->logo) : null,
                    'description' => $shop->description,
                    'contact_phone' => $shop->contact_phone,
                    'contact_wechat' => $shop->contact_wechat
                ]
            ]);
        } catch (\Exception $e) {
            // 保持之前的错误处理逻辑
            Log::error('生成店铺海报失败 (恢复模式): ' . $e->getMessage(), [
                'shop_id' => $shopId,
                'user_id' => Auth::id(),
                'shop_object_id' => isset($shop) ? $shop->id : 'N/A',
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => '生成海报失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取有效的字体路径
     *
     * @return string 字体文件的完整路径
     */
    protected function getFontPath()
    {
        // 首选项：本地中文字体（按优先级排序）
        $chineseFonts = [
            public_path('fonts/msyh.ttf'), // 微软雅黑（最佳中文显示）
            public_path('fonts/simhei.ttf'), // 黑体
            public_path('fonts/SIMHEI.TTF'), // 黑体（大写）
            public_path('fonts/SourceHanSansCN-Regular.ttf'), // 思源黑体
            public_path('fonts/NotoSansSC-Regular.ttf'), // Noto中文
        ];

        foreach ($chineseFonts as $path) {
            if (file_exists($path) && is_readable($path)) {
                Log::info('使用中文字体', ['path' => $path]);
                return $path;
            }
        }

        // 其次：系统字体目录中的中文字体
        $systemChinesePaths = [
            '/usr/share/fonts/simhei.ttf',
            '/usr/share/fonts/truetype/simhei.ttf',
            '/usr/share/fonts/chinese/simhei.ttf',
            '/usr/share/fonts/Noto_Sans_SC/static/NotoSansSC-Regular.ttf',
            '/usr/share/fonts/Noto_Sans_SC/static/NotoSansSC-Bold.ttf',
            '/usr/share/fonts/truetype/noto/NotoSansCJK-Regular.ttc',
            '/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc'
        ];

        foreach ($systemChinesePaths as $path) {
            if (file_exists($path) && is_readable($path)) {
                Log::info('使用系统中文字体', ['path' => $path]);
                return $path;
            }
        }

        // 最后选项：确实存在的系统字体（基于实际扫描结果）
        $confirmedFallbackPaths = [
            '/usr/share/fonts/dejavu/DejaVuLGCSans.ttf',
            '/usr/share/fonts/dejavu/DejaVuLGCSans-Bold.ttf',
            '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf',
            '/usr/share/fonts/dejavu/DejaVuSans.ttf',
            '/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf'
        ];

        foreach ($confirmedFallbackPaths as $path) {
            if (file_exists($path) && is_readable($path)) {
                Log::warning('使用英文备用字体，中文可能显示为方块', ['path' => $path]);
                return $path;
            }
        }

        // 如果所有字体都找不到，抛出异常
        Log::error('找不到任何可用的字体文件');
        throw new \Exception('无法找到可用的字体文件，请检查系统字体安装');
    }

    /**
     * 创建店铺海报
     *
     * @param App\Models\Shop $shop 店铺对象
     * @return string 生成的海报图片路径
     */
    protected function createShopPoster($shop)
    {
        try {
            // 获取海报配置
            $config = $shop->getPosterConfig();

            // 获取有效的字体路径
            $fontPath = $this->getFontPath();
            Log::info('使用的字体路径', ['path' => $fontPath]); // 记录字体路径
            if (!file_exists($fontPath)) {
                 Log::error('字体文件不存在', ['path' => $fontPath]);
                 // 可以考虑抛出异常或使用备用逻辑
            }

            // 记录配置信息用于调试
            Log::info('海报配置信息 (简化测试)', [
                'shop_id' => $shop->id,
                // ... (可以保留部分关键配置日志)
            ]);

            $posterFilename = 'posters/shop_' . $shop->id . '_' . Str::random(10) . '_test.jpg'; // 加后缀以便区分
            $fullPath = storage_path('app/public/' . $posterFilename);

            // 确保目录存在
            $directory = dirname($fullPath);
            if (!file_exists($directory)) {
                mkdir($directory, 0755, true);
            }

            // 创建海报编辑器实例
            $poster = new PosterEditor();

            // 1. 创建京东风格的渐变背景画布
            Log::info('创建京东风格画布', ['width' => 750, 'height' => 1334]);
            $poster->canvas(750, 1334, [
                'color' => [248, 249, 250], // 京东浅灰背景色
                'opacity' => 100
            ]);

            // 添加顶部红色装饰条（京东品牌色）
            $poster->rectangle(0, 0, 750, 8, [
                'color' => [226, 43, 38], // 京东红色 #E22B26
                'opacity' => 0 // PosterEditor中opacity为0表示不透明
            ]);

            // 添加主内容区域白色背景
            $poster->rectangle(30, 80, 690, 600, [
                'color' => [255, 255, 255],
                'opacity' => 0 // PosterEditor中opacity为0表示不透明
            ]);

            // 添加底部二维码区域背景
            $poster->rectangle(30, 720, 690, 320, [
                'color' => [255, 255, 255],
                'opacity' => 0 // PosterEditor中opacity为0表示不透明
            ]);

            // --- 京东风格内容元素 ---

            // 添加"店铺推荐"标签
            $poster->text('店铺推荐', [
                'x' => 60,
                'y' => 120,
                'width' => 120,
                'height' => 30,
                'fontsize' => 18,
                'color' => [226, 43, 38], // 京东红色
                'opacity' => 0, // PosterEditor中0表示不透明
                'horizontal' => 'left',
                'fontpath' => $fontPath
            ]);

            // 添加店铺名称（京东风格 - 更大更醒目，黑色字体）
            if (!empty($config['title'])) {
                Log::info('添加京东风格标题', ['title' => $config['title']]);
                $poster->text($config['title'], [
                    'x' => 60,
                    'y' => 160,
                    'width' => 630,
                    'height' => 90,
                    'fontsize' => 48, // 调整字体大小
                    'color' => [0, 0, 0], // 纯黑色，确保清晰可见
                    'opacity' => 0, // PosterEditor中0表示不透明
                    'horizontal' => 'left',
                    'fontpath' => $fontPath
                ]);
            }

            /*
            // 保持注释: 添加店铺logo (再次注释掉)
            if (!empty($config['logo'])) {
                Log::info('添加 Logo', ['logo_path' => $config['logo']]);
                if (file_exists($config['logo'])) { // 检查 Logo 文件是否存在
                    $poster->insert($config['logo'], [
                        'x' => 375,
                        'y' => 200, // 使用上次调整后的坐标
                        'opacity' => 100
                    ])->fit(150, 150);
                    Log::info('Logo 添加完成');
                } else {
                    Log::warning('Logo 文件不存在，跳过添加', ['path' => $config['logo']]);
                }
            }
            */

            // 添加店铺描述（京东风格 - 更清晰的层次，黑色字体）
            if (!empty($config['description'])) {
                Log::info('添加京东风格描述', ['desc' => Str::limit($config['description'], 80)]);

                // 限制描述长度，保持简洁
                $description = Str::limit($config['description'], 120, '...');

                $poster->text($description, [
                    'x' => 60,
                    'y' => 270,
                    'width' => 630,
                    'height' => 120,
                    'fontsize' => 24, // 适中的字体大小
                    'color' => [0, 0, 0], // 纯黑色，确保清晰可见
                    'opacity' => 0, // PosterEditor中0表示不透明
                    'horizontal' => 'left',
                    'fontpath' => $fontPath
                ]);
            }

            // 添加店铺亮点标签（京东风格，黑色字体）
            $poster->text('✓ 正品保障', [
                'x' => 60,
                'y' => 380,
                'width' => 150,
                'height' => 30,
                'fontsize' => 20,
                'color' => [0, 0, 0], // 黑色
                'opacity' => 0,
                'horizontal' => 'left',
                'fontpath' => $fontPath
            ]);

            $poster->text('✓ 快速发货', [
                'x' => 230,
                'y' => 380,
                'width' => 150,
                'height' => 30,
                'fontsize' => 20,
                'color' => [0, 0, 0], // 黑色
                'opacity' => 0,
                'horizontal' => 'left',
                'fontpath' => $fontPath
            ]);

            $poster->text('✓ 优质服务', [
                'x' => 400,
                'y' => 380,
                'width' => 150,
                'height' => 30,
                'fontsize' => 20,
                'color' => [0, 0, 0], // 黑色
                'opacity' => 0,
                'horizontal' => 'left',
                'fontpath' => $fontPath
            ]);

            // 添加店铺创建时间（更简洁的显示，黑色字体）
            if (!empty($config['created_at'])) {
                Log::info('添加创建时间', ['time' => $config['created_at']]);
                $poster->text($config['created_at'], [
                    'x' => 60,
                    'y' => 430,
                    'width' => 630,
                    'height' => 30,
                    'fontsize' => 18,
                    'color' => [0, 0, 0], // 黑色
                    'opacity' => 0,
                    'horizontal' => 'left',
                    'fontpath' => $fontPath
                ]);
            }

            // 移除店铺分类显示（按要求去掉）
            // 店铺分类信息已移除

            // 添加联系信息（京东风格 - 更突出）
            if (!empty($config['contact_info'])) {
                Log::info('添加联系信息', ['info' => $config['contact_info']]);

                // 添加联系方式标题
                $poster->text('联系方式', [
                    'x' => 60,
                    'y' => 520,
                    'width' => 120,
                    'height' => 30,
                    'fontsize' => 20,
                    'color' => [51, 51, 51],
                    'opacity' => 100,
                    'horizontal' => 'left',
                    'fontpath' => $fontPath,
                    'weight' => 'bold'
                ]);

                $poster->text($config['contact_info'], [
                    'x' => 60,
                    'y' => 560,
                    'width' => 630,
                    'height' => 80,
                    'fontsize' => 18,
                    'color' => [102, 102, 102],
                    'opacity' => 100,
                    'horizontal' => 'left',
                    'fontpath' => $fontPath,
                    'lineheight' => 1.4
                ]);
            }

            // --- 先生成二维码 (使用新的 endroid/qr-code 方法) ---
            $qrCodePath = null;
            $qrCodeGd = null; // 用于存储二维码 GD 资源
            try {
                Log::info('调用 generateQrCode (endroid/qr-code)', ['shop_id' => $shop->id]);
                $qrCodePath = $this->generateQrCode($shop);
                Log::info('generateQrCode (endroid/qr-code) 调用成功', ['path' => $qrCodePath]);

                // 尝试使用 GD 加载二维码图片
                if ($qrCodePath && file_exists($qrCodePath)) {
                    $qrCodeGd = @imagecreatefrompng($qrCodePath);
                    if (!$qrCodeGd) {
                        Log::error('使用 GD 加载二维码 PNG 文件失败', ['path' => $qrCodePath]);
                        // 即使二维码加载失败，也继续生成基础海报
                    } else {
                        Log::info('二维码 PNG 文件成功加载为 GD 资源');
                    }
                } else {
                    Log::warning('二维码文件路径无效或不存在，无法加载', ['path' => $qrCodePath]);
                }

            } catch (\Exception $e) {
                Log::error('createShopPoster 中处理二维码时失败: ' . $e->getMessage());
                // 记录错误，但继续生成海报的其余部分（二维码会缺失）
            }
            // --- 生成二维码结束 ---

            // 恢复: 添加二维码 (使用 generateQrCode 返回的 $qrCodePath) - 这段不再需要，因为我们用 GD 叠加
            /*
            if (!empty($qrCodePath)) { // 检查路径是否有效且非空
                Log::info('尝试添加二维码到海报', ['qr_path' => $qrCodePath]);
                if (file_exists($qrCodePath)) { // 再次检查文件是否存在
                    try {
                        // 暂时注释掉二维码插入
                        Log::info('二维码插入已注释掉，跳过此步骤进行测试');
                    } catch (\Exception $e) {
                        Log::error('向海报实例插入二维码图片时出错: ' . $e->getMessage(), [
                            'path' => $qrCodePath,
                            'trace' => $e->getTraceAsString()
                        ]);
                        // 插入失败，二维码将不会出现在最终海报上
                    }
                } else {
                    Log::warning('由 generateQrCode 返回的二维码文件不存在，跳过添加', ['path' => $qrCodePath]);
                }
            } else {
                 Log::warning('generateQrCode 未返回有效路径，跳过添加二维码');
            }
            */

            // 恢复: 添加底部提示 (也改为左对齐) - 移除此文本
            /*
            Log::info('添加底部提示');
            $poster->text('扫描二维码，立即查看店铺详情', [
                'x' => 50, // 左边距
                'y' => 1050, // 调整后的坐标
                'width' => 650, // 调整宽度
                'height' => 50,
                'fontsize' => 24,
                'color' => [0, 0, 0],
                'opacity' => 100,
                'horizontal' => 'left', // 左对齐
                'fontpath' => $fontPath
            ]);
            */
            Log::info('已移除底部的"扫描二维码..."文本');

            // --- 恢复结束 ---

            // 2. 保存海报 - 修改为 GD 叠加和保存
            Log::info('准备使用 GD 叠加二维码并保存最终海报', [
                'shop_id' => $shop->id,
                'poster_path' => $fullPath,
                'qrcode_path' => $qrCodePath ?? 'N/A'
            ]);

            // PosterEditor 没有 getGdResource 方法，使用保存到临时文件再加载的方式
            $baseImageGd = null;
            $tempBasePath = storage_path('app/public/posters/temp_base_' . $shop->id . '_' . Str::random(5) . '.jpg');
            try {
                Log::info('尝试将基础海报保存到临时文件', ['path' => $tempBasePath]);
                $poster->save($tempBasePath); // 让 PosterEditor 保存基础海报
                Log::info('基础海报已保存到临时文件，尝试使用 GD 加载');
                $baseImageGd = @imagecreatefromjpeg($tempBasePath);
            } finally {
                // 无论加载是否成功，都尝试删除临时文件
                if (file_exists($tempBasePath)) {
                    @unlink($tempBasePath);
                    Log::info('已删除临时基础海报文件', ['path' => $tempBasePath]);
                }
            }
            if (!$baseImageGd) {
                 Log::error('GD 无法加载临时基础海报文件', ['path' => $tempBasePath]);
                 throw new \Exception('无法创建或加载基础海报的 GD 资源');
            }
            Log::info('已通过临时文件加载基础海报 GD 资源');

            // 如果二维码 GD 资源有效，则进行叠加（京东风格布局）
            if ($qrCodeGd) {
                $baseWidth = imagesx($baseImageGd);
                $baseHeight = imagesy($baseImageGd);
                $qrWidth = imagesx($qrCodeGd);
                $qrHeight = imagesy($qrCodeGd);

                // 计算二维码在底部区域居中的位置
                $dst_x = ($baseWidth - $qrWidth) / 2;
                $dst_y = 780; // 固定在底部区域

                Log::info('计算京东风格二维码叠加位置', ['x' => $dst_x, 'y' => $dst_y, 'qr_w' => $qrWidth, 'qr_h' => $qrHeight]);

                // 使用 imagecopy 将二维码绘制到底图上
                $copied = imagecopy($baseImageGd, $qrCodeGd, intval($dst_x), intval($dst_y), 0, 0, $qrWidth, $qrHeight);

                if ($copied) {
                    Log::info('二维码成功叠加到基础海报 GD 资源');

                    // 添加二维码说明文字（使用GD直接绘制）
                    $textColor = imagecolorallocate($baseImageGd, 102, 102, 102); // 灰色
                    $font = $this->getFontPath();

                    // 添加"扫码进入店铺"文字
                    if (function_exists('imagettftext') && file_exists($font)) {
                        imagettftext($baseImageGd, 16, 0, 280, 1020, $textColor, $font, '扫码进入店铺');
                        imagettftext($baseImageGd, 14, 0, 300, 1050, $textColor, $font, '更多优质商品等你来');
                    }
                } else {
                    Log::error('imagecopy 叠加二维码失败');
                    // 即使叠加失败，仍然保存基础海报
                }
                // 释放二维码 GD 资源
                imagedestroy($qrCodeGd);
            } else {
                Log::warning('二维码 GD 资源无效，跳过叠加步骤');
            }

            // 使用 imagejpeg 保存最终合成的 GD 资源 (质量 90)
            $saved = imagejpeg($baseImageGd, $fullPath, 90);

            if (!$saved) {
                Log::error('使用 imagejpeg 保存最终海报失败', ['path' => $fullPath]);
                 // 尝试释放资源
                if (isset($baseImageGd) && is_resource($baseImageGd)) {
                     imagedestroy($baseImageGd);
                }
                throw new \Exception('无法保存最终生成的海报文件。');
            }

            // 释放基础海报 GD 资源
            imagedestroy($baseImageGd);

            // 如果之前生成了二维码文件，可以选择删除它
            if ($qrCodePath && file_exists($qrCodePath)) {
                 @unlink($qrCodePath);
                 Log::info('已删除临时二维码文件', ['path' => $qrCodePath]);
            }

            Log::info('最终海报（已叠加二维码）保存成功', ['path' => $posterFilename]);
            return $posterFilename; // 返回最终海报的文件名

        } catch (\Exception $e) {
            Log::error('创建海报失败 (GD 叠加): ' . $e->getMessage(), [
                'shop_id' => $shop->id,
                'trace' => $e->getTraceAsString()
            ]);
            throw new \Exception('生成海报时发生错误 (GD 叠加): ' . $e->getMessage());
        }
    }

    /**
     * 使用 endroid/qr-code 生成店铺二维码
     *
     * @param App\Models\Shop $shop 店铺对象
     * @return string 二维码图片路径
     * @throws \Exception 如果生成失败
     */
    protected function generateQrCode($shop)
    {
        $fullPath = null;
        $shopUrl = null;

        try {
            // 1. 生成文件名和完整路径
            $filename = 'qrcodes/shop_' . $shop->id . '_' . Str::random(10) . '.png';
            $fullPath = storage_path('app/public/' . $filename);

            // 2. 确保目录存在并可写
            $directory = dirname($fullPath);
            if (!file_exists($directory)) {
                Log::info('二维码目录不存在，尝试创建', ['dir' => $directory]);
                mkdir($directory, 0755, true);
            }
            if (!is_writable($directory)) {
                Log::error('二维码目录不可写!', ['dir' => $directory]);
                throw new \Exception("二维码目录不可写: " . $directory);
            }
            Log::info('二维码目录准备就绪 (使用 endroid/qr-code)', ['dir' => $directory]);

            // 3. 生成店铺链接
            $shopUrl = route('shops.show', ['shop' => $shop->id]);
            Log::info('准备使用 endroid/qr-code 生成二维码', [
                'shop_id' => $shop->id,
                'shop_url' => $shopUrl,
                'qrcode_path' => $fullPath
            ]);

            // 4. 创建 QrCode 对象
            $qrCode = QrCode::create($shopUrl)
                ->setEncoding(new Encoding('UTF-8')) // 设置编码
                ->setErrorCorrectionLevel(new ErrorCorrectionLevelHigh()) // 设置容错级别
                ->setSize(200) // 设置尺寸
                ->setMargin(1); // 设置边距
                // 可以添加颜色等设置 ->setForegroundColor(new Color(0, 0, 0))
                                // ->setBackgroundColor(new Color(255, 255, 255));

            // 5. 创建 PngWriter
            $writer = new PngWriter();

            // 6. 获取 PNG 图片结果
            $result = $writer->write($qrCode);

            // 7. 保存结果到文件
            $saveResult = file_put_contents($fullPath, $result->getString());

            if ($saveResult === false) {
                Log::error('endroid/qr-code: 保存 PNG 文件失败', ['path' => $fullPath]);
                throw new \Exception('无法将二维码内容写入文件。');
            }

            // 8. 验证文件是否生成成功
            if (file_exists($fullPath) && filesize($fullPath) > 0) {
                Log::info('endroid/qr-code: 二维码生成并保存成功', [
                    'shop_id' => $shop->id,
                    'qrcode_path' => $fullPath,
                    'file_size' => filesize($fullPath)
                ]);
                return $fullPath; // 成功返回路径
            } else {
                $fileExists = file_exists($fullPath);
                $fileSize = $fileExists ? filesize($fullPath) : 'N/A';
                Log::error('endroid/qr-code: 保存后文件无效或大小为零', [
                    'path' => $fullPath,
                    'exists' => $fileExists,
                    'size' => $fileSize
                ]);
                throw new \Exception("endroid/qr-code 生成的二维码文件无效。");
            }

        } catch (\Exception $e) {
            Log::error('使用 endroid/qr-code 生成二维码时捕获到异常: ' . $e->getMessage(), [
                'shop_id' => isset($shop) ? $shop->id : 'N/A',
                'trace' => $e->getTraceAsString()
            ]);

            // 向上层抛出异常
            throw new \Exception('使用 endroid/qr-code 生成二维码时发生错误: ' . $e->getMessage(), 0, $e);
        }
    }

    /**
     * 显示图片预览页面，用于微信内浏览器分享
     *
     * @param Request $request
     * @param string $path 海报路径
     * @return \Illuminate\Http\Response
     */
    public function showPoster(Request $request, $path)
    {
        // 验证路径安全性，防止目录遍历
        if (strpos($path, '..') !== false) {
            abort(404);
        }

        $posterUrl = asset('storage/posters/' . $path);
        $shopId = null;

        // 从文件名中提取店铺ID
        if (preg_match('/shop_(\d+)_/', $path, $matches)) {
            $shopId = $matches[1];
            $shop = Shop::find($shopId);
        }

        // 获取分享设置
        $shareTitle = $shop->name ?? '积分商城';
        $shareDesc = $shop->description ?? '积分兑换，尽在商城';
        $shareImgUrl = $posterUrl;

        return view('posters.share', compact('posterUrl', 'shopId', 'shareTitle', 'shareDesc', 'shareImgUrl'));
    }

    /**
     * 记录海报分享
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function recordShare(Request $request)
    {
        try {
            $validated = $request->validate([
                'shop_id' => 'required|integer|exists:shops,id',
                'platform' => 'required|string|in:wechat,timeline,qq,weibo,link,web_share,download,douyin,tiktok,xiaohongshu,zhihu,dingding,facebook,twitter,instagram,native_share,other',
                'poster_id' => 'required|string',
            ]);

            // 记录分享统计
            $shop = Shop::findOrFail($validated['shop_id']);

            $shareData = [
                'user_id' => Auth::id(),
                'platform' => $validated['platform'],
                'poster_id' => $validated['poster_id'],
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ];

            // 如果有其他数据，一并保存
            if ($request->has('app_info')) {
                $shareData['metadata'] = $request->input('app_info');
            }

            // 创建分享记录
            $shop->shareRecords()->create($shareData);

            return response()->json([
                'success' => true,
                'message' => '分享记录已保存'
            ]);
        } catch (\Exception $e) {
            Log::error('记录海报分享失败: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => '记录分享失败'
            ], 500);
        }
    }
}