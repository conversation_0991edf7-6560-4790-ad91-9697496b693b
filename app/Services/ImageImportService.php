<?php

namespace App\Services;

use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class ImageImportService
{
    /**
     * 处理图片导入
     * 
     * @param string|null $imageUrl 图片URL
     * @param string|null $imageFileName 本地图片文件名
     * @return string|null 处理后的图片路径
     */
    public function processImage($imageUrl = null, $imageFileName = null)
    {
        try {
            // 优先处理本地文件
            if (!empty($imageFileName)) {
                return $this->processLocalImage($imageFileName);
            }
            
            // 处理网络图片URL
            if (!empty($imageUrl)) {
                return $this->processUrlImage($imageUrl);
            }
            
            return null;
        } catch (\Exception $e) {
            Log::error('图片处理失败: ' . $e->getMessage(), [
                'imageUrl' => $imageUrl,
                'imageFileName' => $imageFileName,
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * 处理本地图片文件
     * 
     * @param string $fileName 文件名
     * @return string|null
     */
    protected function processLocalImage($fileName)
    {
        $importPath = 'import/images/' . $fileName;
        
        // 检查文件是否存在
        if (!Storage::exists($importPath)) {
            Log::warning("本地图片文件不存在: {$importPath}");
            return null;
        }
        
        // 验证文件类型
        $extension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
        if (!in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp'])) {
            Log::warning("不支持的图片格式: {$extension}");
            return null;
        }
        
        // 生成新的文件名
        $newFileName = 'products/' . date('Y/m/') . Str::random(20) . '.' . $extension;
        
        // 复制文件到产品图片目录
        if (Storage::copy($importPath, 'public/' . $newFileName)) {
            Log::info("本地图片处理成功: {$fileName} -> {$newFileName}");
            return $newFileName;
        }
        
        Log::error("本地图片复制失败: {$fileName}");
        return null;
    }

    /**
     * 处理网络图片URL
     * 
     * @param string $url 图片URL
     * @return string|null
     */
    protected function processUrlImage($url)
    {
        // 验证URL格式
        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            Log::warning("无效的图片URL: {$url}");
            return null;
        }
        
        // 如果是相对路径或已经是本地路径，直接返回
        if (strpos($url, 'http') !== 0) {
            return $url;
        }
        
        try {
            // 下载图片
            $response = Http::timeout(30)->get($url);
            
            if (!$response->successful()) {
                Log::warning("图片下载失败: {$url}, 状态码: " . $response->status());
                return null;
            }
            
            // 获取文件扩展名
            $extension = $this->getImageExtensionFromUrl($url, $response->header('Content-Type'));
            if (!$extension) {
                Log::warning("无法确定图片格式: {$url}");
                return null;
            }
            
            // 生成文件名
            $fileName = 'products/' . date('Y/m/') . Str::random(20) . '.' . $extension;
            
            // 保存文件
            if (Storage::put('public/' . $fileName, $response->body())) {
                Log::info("网络图片下载成功: {$url} -> {$fileName}");
                return $fileName;
            }
            
            Log::error("图片保存失败: {$url}");
            return null;
            
        } catch (\Exception $e) {
            Log::error("下载图片异常: {$url}, 错误: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 从URL或Content-Type获取图片扩展名
     * 
     * @param string $url
     * @param string|null $contentType
     * @return string|null
     */
    protected function getImageExtensionFromUrl($url, $contentType = null)
    {
        // 先从Content-Type判断
        if ($contentType) {
            $mimeToExt = [
                'image/jpeg' => 'jpg',
                'image/jpg' => 'jpg',
                'image/png' => 'png',
                'image/gif' => 'gif',
                'image/webp' => 'webp',
            ];
            
            if (isset($mimeToExt[$contentType])) {
                return $mimeToExt[$contentType];
            }
        }
        
        // 从URL路径获取扩展名
        $path = parse_url($url, PHP_URL_PATH);
        $extension = strtolower(pathinfo($path, PATHINFO_EXTENSION));
        
        if (in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp'])) {
            return $extension === 'jpeg' ? 'jpg' : $extension;
        }
        
        return 'jpg'; // 默认使用jpg
    }

    /**
     * 处理多张图片URLs
     * 
     * @param string $imageUrls 用逗号分隔的图片URLs
     * @return array 处理后的图片路径数组
     */
    public function processMultipleImages($imageUrls)
    {
        if (empty($imageUrls)) {
            return [];
        }
        
        $urls = array_map('trim', explode(',', $imageUrls));
        $processedImages = [];
        
        foreach ($urls as $url) {
            if (!empty($url)) {
                $processedImage = $this->processUrlImage($url);
                if ($processedImage) {
                    $processedImages[] = $processedImage;
                }
            }
        }
        
        return $processedImages;
    }

    /**
     * 创建导入图片目录
     */
    public function createImportDirectory()
    {
        $importDir = 'import/images';
        
        if (!Storage::exists($importDir)) {
            Storage::makeDirectory($importDir);
            Log::info("创建导入图片目录: {$importDir}");
        }
        
        return Storage::path($importDir);
    }
}
