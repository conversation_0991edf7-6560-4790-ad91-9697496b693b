<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class CreateImportDirectories extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:create-directories';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '创建导入功能所需的目录结构';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('正在创建导入功能所需的目录...');

        $directories = [
            'import',
            'import/images',
            'import/temp',
            'products',
            'products/' . date('Y'),
            'products/' . date('Y/m'),
        ];

        foreach ($directories as $directory) {
            if (!Storage::exists($directory)) {
                Storage::makeDirectory($directory);
                $this->info("✓ 创建目录: storage/app/{$directory}");
            } else {
                $this->comment("- 目录已存在: storage/app/{$directory}");
            }
        }

        // 创建public目录结构
        $publicDirectories = [
            'public/products',
            'public/products/' . date('Y'),
            'public/products/' . date('Y/m'),
        ];

        foreach ($publicDirectories as $directory) {
            if (!Storage::exists($directory)) {
                Storage::makeDirectory($directory);
                $this->info("✓ 创建目录: storage/app/{$directory}");
            } else {
                $this->comment("- 目录已存在: storage/app/{$directory}");
            }
        }

        // 创建示例说明文件
        $readmeContent = "# 图片导入目录说明

## 目录结构
- `import/images/` - 存放待导入的商品图片文件
- `import/temp/` - 临时文件目录
- `products/` - 处理后的商品图片存储目录

## 使用方法
1. 将商品图片文件放入 `import/images/` 目录
2. 在Excel导入模板的封面图片文件名列中填写文件名
3. 执行商品导入操作

## 支持的图片格式
- JPG/JPEG
- PNG
- GIF
- WebP

## 注意事项
- 图片文件大小建议不超过2MB
- 文件名不要包含特殊字符
- 确保文件名与Excel中填写的完全一致
";

        Storage::put('import/README.md', $readmeContent);
        $this->info("✓ 创建说明文件: storage/app/import/README.md");

        $this->info('');
        $this->info('🎉 导入目录创建完成！');
        $this->info('');
        $this->info('现在您可以：');
        $this->info('1. 将图片文件上传到: ' . Storage::path('import/images'));
        $this->info('2. 下载商品导入模板');
        $this->info('3. 在模板中填写图片文件名');
        $this->info('4. 执行批量导入');

        return 0;
    }
}
