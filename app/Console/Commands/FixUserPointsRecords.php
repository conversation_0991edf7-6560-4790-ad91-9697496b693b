<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\PointRecord;
use Illuminate\Support\Facades\DB;

class FixUserPointsRecords extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:user-points-records';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '修复有积分但没有积分记录的用户';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('开始修复用户积分记录...');

        // 查找有积分但没有积分记录的用户
        $users = User::where('points', '>', 0)
            ->whereNotExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from('point_records')
                    ->whereColumn('point_records.user_id', 'users.id');
            })
            ->get();

        if ($users->isEmpty()) {
            $this->info('没有需要修复的用户。');
            return 0;
        }

        $this->info("找到 {$users->count()} 个需要修复的用户：");

        $bar = $this->output->createProgressBar($users->count());
        $bar->start();

        $fixedCount = 0;

        DB::beginTransaction();
        try {
            foreach ($users as $user) {
                // 创建积分记录
                PointRecord::create([
                    'user_id' => $user->id,
                    'points' => $user->getOriginal('points'),
                    'type' => 'manual',
                    'description' => '用户导入初始积分（系统修复）',
                    'created_at' => $user->created_at ?? now(),
                    'updated_at' => now(),
                ]);

                $fixedCount++;
                $bar->advance();
            }

            DB::commit();
            $bar->finish();
            $this->newLine();
            $this->info("成功修复 {$fixedCount} 个用户的积分记录。");

        } catch (\Exception $e) {
            DB::rollBack();
            $bar->finish();
            $this->newLine();
            $this->error("修复过程中发生错误: " . $e->getMessage());
            return 1;
        }

        return 0;
    }
}
