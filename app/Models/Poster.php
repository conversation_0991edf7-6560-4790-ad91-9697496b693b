<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Poster extends Model
{
    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'shop_id',
        'title',
        'description',
        'image_path',
        'template',
        'settings',
        'share_count',
        'is_active'
    ];

    /**
     * 应该被类型转换的属性
     *
     * @var array
     */
    protected $casts = [
        'settings' => 'array',
        'is_active' => 'boolean',
        'share_count' => 'integer'
    ];

    /**
     * 获取海报所属的店铺
     */
    public function shop()
    {
        return $this->belongsTo(Shop::class);
    }

    /**
     * 获取海报分享记录
     */
    public function shareRecords()
    {
        return $this->morphMany(ShareRecord::class, 'target');
    }

    /**
     * 获取海报图片URL
     */
    public function getImageUrlAttribute()
    {
        if ($this->image_path) {
            return asset('storage/' . $this->image_path);
        }
        return asset('images/default-poster.jpg');
    }

    /**
     * 获取海报名称（title的别名）
     */
    public function getNameAttribute()
    {
        return $this->title;
    }

    /**
     * 增加分享计数
     */
    public function incrementShareCount()
    {
        $this->increment('share_count');
    }

    /**
     * 获取海报关联的商品
     */
    public function products()
    {
        return $this->belongsToMany(Product::class, 'poster_product')
            ->withTimestamps();
    }
}