<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithProperties;
use Maatwebsite\Excel\Concerns\WithCustomCsvSettings;
use Maatwebsite\Excel\Excel as ExcelTypes;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class ProductsTemplate implements FromArray, WithHeadings, ShouldAutoSize, WithStyles, WithProperties, WithCustomCsvSettings
{
    protected $format;

    /**
     * 构造函数，接收格式
     */
    public function __construct(string $format = 'xlsx')
    {
        \Log::info('ProductsTemplate 类被实例化，格式: ' . $format);
        $this->format = $format;
    }

    /**
     * 设置属性以替代WriterType方法
     */
    public function properties(): array
    {
        return [
            'creator'        => '积分商城系统',
            'lastModifiedBy' => '积分商城系统',
            'title'          => '商品导入模板',
            'description'    => '用于批量导入商品数据的模板',
            'subject'        => '商品数据',
            'category'       => '模板',
            'manager'        => '管理员',
            'company'        => '系统',
        ];
    }

    /**
     * 设置表头 - 新顺序
     * @return array
     */
    public function headings(): array
    {
        return [
            '商品名称（必填）', // 0
            '分类',           // 1
            '描述',           // 2
            '价格（必填）',   // 3
            '原价',           // 4
            '库存（必填）',   // 5
            '兑换量',         // 6
            '封面图片URL',    // 7 (支持网络图片URL)
            '封面图片文件名', // 8 (支持本地图片文件，需放在images文件夹中)
            '商品图片URLs',   // 9 (多张图片URL，用英文逗号分隔)
            '是否上架',       // 10 (填"是"或"否")
            '是否推荐',       // 11 (填"是"或"否")
            '是否新品',       // 12 (填"是"或"否")
            '是否热销',       // 13 (填"是"或"否")
            '是否积分商品',   // 14 (填"是"或"否")
            '所需积分',       // 15 (积分商品必填)
            '兑换限制',       // 16 (0或空表示不限制)
            '排序值',         // 17 (数字，越大越靠前)
            '第三方商品链接', // 18 (可选，填写第三方平台的商品URL)
        ];
    }

    /**
     * 返回空数组，不包含示例数据
     * @return array
     */
    public function array(): array
    {
        return []; // 返回空数组
    }

    /**
     * 样式设置 - 简化版
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        if ($this->format === 'csv') {
            return []; // CSV 不支持样式
        }

        // 只设置表头加粗
        $sheet->getStyle('A1:S1')->applyFromArray([
            'font' => ['bold' => true],
            'alignment' => ['horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER],
        ]);

        // 添加说明行 (不再合并单元格或设置特殊样式)
        $sheet->setCellValue('A3', '填表说明：'); // 调整起始行，紧跟表头下方
        $sheet->setCellValue('A4', '1. 带（必填）的列不能为空。');
        $sheet->setCellValue('A5', '2. "是否..." 类型的列，请填写 "是" 或 "否"。');
        $sheet->setCellValue('A6', '3. 如果 "是否积分商品" 为 "是"，则 "所需积分" 必须填写正整数。');
        $sheet->setCellValue('A7', '4. "兑换限制" 表示每个用户最多可兑换的数量，0 或空表示不限制。');
        $sheet->setCellValue('A8', '5. "分类" 如果不存在将自动创建。');
        $sheet->setCellValue('A9', '6. "排序值" 为数字，数值越大排序越靠前。');
        $sheet->setCellValue('A10', '7. "第三方商品链接" 可填写其他平台的商品URL，方便管理员在订单中查看。');
        $sheet->setCellValue('A11', '8. 图片支持两种方式：');
        $sheet->setCellValue('A12', '   - 封面图片URL：直接填写网络图片地址');
        $sheet->setCellValue('A13', '   - 封面图片文件名：将图片文件放在storage/app/import/images/文件夹中，填写文件名');
        $sheet->setCellValue('A14', '   - 商品图片URLs：多张图片用英文逗号分隔');
        $sheet->setCellValue('A15', '9. 图片文件支持jpg、jpeg、png、gif格式，建议大小不超过2MB。');

        return [];
    }

    /**
     * 获取自定义 CSV 设置
     * @return array
     */
    public function getCsvSettings(): array
    {
        return [
            'use_bom' => true, // 启用 UTF-8 BOM
        ];
    }
}