# 商品批量导入图片功能使用说明

## 功能概述

现在支持在Excel表格中批量导入商品图片，支持两种方式：
1. **网络图片URL** - 直接填写图片的网络地址
2. **本地图片文件** - 将图片文件放在服务器指定目录，填写文件名

## 准备工作

### 1. 创建导入目录
运行以下命令创建必要的目录结构：
```bash
php artisan import:create-directories
```

### 2. 下载最新导入模板
在管理后台的商品管理页面，点击"批量导入" -> "下载导入模板"

## 使用方法

### 方式一：使用网络图片URL

1. 在Excel模板的"封面图片URL"列中填写完整的图片网址
2. 在"商品图片URLs"列中填写多张图片网址，用英文逗号分隔
3. 上传Excel文件进行导入

示例：
```
封面图片URL: https://example.com/images/product1.jpg
商品图片URLs: https://example.com/images/product1-1.jpg,https://example.com/images/product1-2.jpg
```

### 方式二：使用本地图片文件

1. 将图片文件上传到服务器的 `storage/app/import/images/` 目录
2. 在Excel模板的"封面图片文件名"列中填写文件名（包含扩展名）
3. 上传Excel文件进行导入

示例：
```
封面图片文件名: product1.jpg
```

## 支持的图片格式

- JPG/JPEG
- PNG
- GIF
- WebP

## 注意事项

1. **文件大小限制**：建议单张图片不超过2MB
2. **文件名规范**：不要包含特殊字符，确保与Excel中填写的完全一致
3. **网络图片**：系统会自动下载网络图片并保存到本地
4. **本地图片**：文件必须存在于 `storage/app/import/images/` 目录中
5. **优先级**：如果同时填写了本地文件名和网络URL，优先使用本地文件

## 目录结构

```
storage/app/
├── import/
│   ├── images/          # 存放待导入的图片文件
│   ├── temp/           # 临时文件目录
│   └── README.md       # 说明文件
└── public/
    └── products/       # 处理后的商品图片存储目录
        └── 2025/
            └── 05/
```

## 错误处理

如果图片处理失败，系统会：
1. 记录错误日志到 `storage/logs/laravel.log`
2. 使用默认占位图片 `/images/placeholder.jpg`
3. 继续处理其他商品数据

## 示例Excel数据

| 商品名称 | 分类 | 描述 | 价格 | 库存 | 封面图片URL | 封面图片文件名 | 商品图片URLs |
|---------|------|------|------|------|-------------|---------------|-------------|
| 测试商品1 | 电子产品 | 这是测试商品 | 99.00 | 100 | | product1.jpg | |
| 测试商品2 | 家居用品 | 另一个测试商品 | 199.00 | 50 | https://example.com/product2.jpg | | https://example.com/p2-1.jpg,https://example.com/p2-2.jpg |

## 常见问题

**Q: 图片没有显示怎么办？**
A: 检查图片文件是否存在于正确的目录，文件名是否与Excel中填写的一致。

**Q: 网络图片下载失败怎么办？**
A: 确保服务器能够访问外网，图片URL是有效的，图片文件大小不超过限制。

**Q: 如何批量上传图片到服务器？**
A: 可以使用FTP、SCP等工具将图片文件上传到 `storage/app/import/images/` 目录。

**Q: 导入后图片路径是什么？**
A: 系统会自动生成路径，格式为 `products/年份/月份/随机文件名.扩展名`
