<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>积分兑换记录页面优化预览</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        /* 京东风格主题色 */
        :root {
            --jd-red: #e93b3d;
            --jd-red-light: #f10215;
            --jd-gray: #f5f5f5;
            --jd-light: #fff8f0;
            --jd-orange: #ff6700;
            --jd-blue: #005aa0;
        }

        /* 整体布局 - 京东风格 */
        body {
            background-color: var(--jd-gray);
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }

        .exchange-history-container {
            background-color: var(--jd-gray);
            padding-top: 1rem;
            padding-bottom: 2rem;
            min-height: calc(100vh - 200px);
        }

        /* 页面标题 - 京东风格 */
        .page-title {
            background: linear-gradient(135deg, var(--jd-red-light), var(--jd-red));
            color: white;
            padding: 20px 0;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-radius: 4px;
        }

        .page-title h3 {
            margin: 0;
            font-weight: 600;
            font-size: 1.5rem;
        }

        /* 筛选栏 - 京东风格 */
        .filter-section {
            background: white;
            border-radius: 4px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            border: 1px solid #e0e0e0;
        }

        .filter-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid var(--jd-red);
            display: inline-block;
        }

        .filter-tabs {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 15px;
        }

        .filter-tab {
            padding: 8px 16px;
            border: 1px solid #ddd;
            border-radius: 3px;
            background: white;
            color: #666;
            text-decoration: none;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.2s;
            cursor: pointer;
        }

        .filter-tab:hover {
            border-color: var(--jd-red);
            color: var(--jd-red);
            background: #fafafa;
            text-decoration: none;
        }

        .filter-tab.active {
            background: var(--jd-red);
            color: white;
            border-color: var(--jd-red);
            box-shadow: 0 2px 4px rgba(233, 59, 61, 0.2);
        }

        .filter-tab .badge {
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 10px;
            font-weight: 600;
        }

        .filter-tab.active .badge {
            background-color: rgba(255, 255, 255, 0.2) !important;
            color: white !important;
        }

        /* 订单卡片 - 京东风格 */
        .exchange-card {
            background-color: white;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 15px;
            transition: all 0.2s;
            box-shadow: 0 1px 2px rgba(0,0,0,0.05);
        }

        .exchange-card:hover {
            border-color: var(--jd-red);
            box-shadow: 0 2px 8px rgba(233, 59, 61, 0.15);
            transform: translateY(-1px);
        }

        .exchange-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-bottom: 12px;
            border-bottom: 1px solid #f0f0f0;
            margin-bottom: 15px;
        }

        .exchange-date {
            color: #666;
            font-size: 13px;
            font-weight: 500;
        }

        .exchange-status {
            padding: 4px 12px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* 状态颜色 - 京东风格 */
        .status-pending {
            background: linear-gradient(45deg, #faad14, #ffc53d);
            color: white;
            box-shadow: 0 2px 4px rgba(250, 173, 20, 0.3);
        }
        .status-shipped {
            background: linear-gradient(45deg, #1890ff, #40a9ff);
            color: white;
            box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);
        }
        .status-completed {
            background: linear-gradient(45deg, #52c41a, #73d13d);
            color: white;
            box-shadow: 0 2px 4px rgba(82, 196, 26, 0.3);
        }
        .status-cancelled {
            background: linear-gradient(45deg, #ff4d4f, #ff7875);
            color: white;
            box-shadow: 0 2px 4px rgba(255, 77, 79, 0.3);
        }

        .exchange-content {
            display: flex;
            align-items: flex-start;
        }

        .exchange-image {
            width: 70px;
            height: 70px;
            flex-shrink: 0;
            border-radius: 4px;
            overflow: hidden;
            margin-right: 15px;
            border: 1px solid #f0f0f0;
        }

        .exchange-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.2s;
        }

        .exchange-image:hover img {
            transform: scale(1.05);
        }

        .exchange-info {
            flex-grow: 1;
            min-width: 0;
        }

        .exchange-product-name {
            font-size: 15px;
            font-weight: 600;
            margin-bottom: 8px;
            color: #333;
            line-height: 1.4;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .exchange-product-name:hover {
            color: var(--jd-red);
        }

        .exchange-order-no {
            color: #666;
            font-size: 13px;
            margin-bottom: 5px;
            font-family: 'Courier New', monospace;
        }

        .exchange-meta {
            color: #666;
            font-size: 13px;
            margin-bottom: 5px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .exchange-points {
            color: var(--jd-red);
            font-weight: 700;
            font-size: 16px;
            font-family: "Arial", sans-serif;
        }

        /* 操作按钮 - 京东风格 */
        .exchange-actions {
            margin-top: 15px;
            text-align: right;
            display: flex;
            justify-content: flex-end;
            gap: 8px;
            flex-wrap: wrap;
        }

        .btn-sm {
            font-size: 12px;
            padding: 6px 12px;
            border-radius: 3px;
            font-weight: 500;
            transition: all 0.2s;
            border: 1px solid;
            text-decoration: none;
        }

        .btn-outline-primary {
            color: var(--jd-blue);
            border-color: var(--jd-blue);
            background: white;
        }

        .btn-outline-primary:hover {
            background: var(--jd-blue);
            color: white;
            transform: translateY(-1px);
        }

        .btn-outline-danger {
            color: #ff4d4f;
            border-color: #ff4d4f;
            background: white;
        }

        .btn-outline-danger:hover {
            background: #ff4d4f;
            color: white;
            transform: translateY(-1px);
        }

        .btn-success {
            background: linear-gradient(45deg, #52c41a, #73d13d);
            border-color: #52c41a;
            color: white;
        }

        .btn-success:hover {
            background: linear-gradient(45deg, #389e0d, #52c41a);
            transform: translateY(-1px);
        }
    </style>
</head>
<body>
    <div class="container exchange-history-container">
        <div class="row justify-content-center">
            <div class="col-md-10 col-lg-8">
                <!-- 页面标题 -->
                <div class="page-title text-center">
                    <h3><i class="fas fa-history me-2"></i>积分兑换记录</h3>
                </div>

                <!-- 筛选区域 -->
                <div class="filter-section">
                    <div class="filter-title">
                        <i class="fas fa-filter me-2"></i>订单筛选
                    </div>
                    
                    <!-- 状态筛选标签 -->
                    <div class="filter-tabs">
                        <a href="#" class="filter-tab active">
                            <i class="fas fa-list me-1"></i>全部订单
                            <span class="badge bg-secondary ms-1">25</span>
                        </a>
                        <a href="#" class="filter-tab">
                            <i class="fas fa-clock me-1"></i>待发货
                            <span class="badge bg-warning ms-1">3</span>
                        </a>
                        <a href="#" class="filter-tab">
                            <i class="fas fa-shipping-fast me-1"></i>已发货
                            <span class="badge bg-info ms-1">5</span>
                        </a>
                        <a href="#" class="filter-tab">
                            <i class="fas fa-check-circle me-1"></i>已完成
                            <span class="badge bg-success ms-1">15</span>
                        </a>
                        <a href="#" class="filter-tab">
                            <i class="fas fa-times-circle me-1"></i>已取消
                            <span class="badge bg-danger ms-1">2</span>
                        </a>
                    </div>

                    <!-- 搜索功能 -->
                    <div class="row g-2 mb-3">
                        <div class="col-md-4">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" class="form-control" placeholder="搜索商品名称">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-receipt"></i>
                                </span>
                                <input type="text" class="form-control" placeholder="搜索订单号">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="d-flex gap-2">
                                <button class="btn btn-primary flex-fill">
                                    <i class="fas fa-search me-1"></i>搜索
                                </button>
                                <button class="btn btn-outline-secondary">
                                    <i class="fas fa-undo"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 日期筛选 -->
                    <div class="d-flex align-items-center gap-3 flex-wrap">
                        <label class="form-label mb-0">开始日期:</label>
                        <input type="date" class="form-control" style="max-width: 150px;">
                        
                        <label class="form-label mb-0">结束日期:</label>
                        <input type="date" class="form-control" style="max-width: 150px;">
                        
                        <button class="btn btn-warning">
                            <i class="fas fa-search me-1"></i>筛选
                        </button>
                        
                        <a href="#" class="btn btn-secondary">
                            <i class="fas fa-undo me-1"></i>重置
                        </a>
                    </div>
                </div>

                <!-- 示例订单卡片 -->
                <div class="exchange-card">
                    <div class="exchange-header">
                        <span class="exchange-date">
                            <i class="fas fa-calendar-alt me-1"></i>
                            2025-05-13 16:55
                        </span>
                        <span class="exchange-status status-pending">
                            <i class="fas fa-clock me-1"></i>待发货
                        </span>
                    </div>

                    <div class="exchange-content">
                        <div class="exchange-image">
                            <img src="https://via.placeholder.com/70x70/e93b3d/ffffff?text=商品" alt="商品图片">
                        </div>
                        <div class="exchange-info">
                            <div class="exchange-product-name">
                                ThinkPlus联想128GB Type-C USB3.2 高速U盘金属商务办公便携存储盘
                            </div>
                            <div class="exchange-order-no">
                                <i class="fas fa-receipt me-1"></i>订单号: PX20250513165500njCKh
                            </div>
                            <div class="exchange-meta">
                                <span>
                                    <i class="fas fa-coins me-1"></i>消耗积分: 
                                    <span class="exchange-points">6950</span>
                                </span>
                                <span>
                                    <i class="fas fa-cubes me-1"></i>数量: 1
                                </span>
                            </div>
                        </div>
                    </div>

                    <div class="exchange-actions">
                        <a href="#" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-eye me-1"></i>查看详情
                        </a>
                        <button class="btn btn-outline-danger btn-sm">
                            <i class="fas fa-times me-1"></i>取消订单
                        </button>
                    </div>
                </div>

                <!-- 已发货状态示例 -->
                <div class="exchange-card">
                    <div class="exchange-header">
                        <span class="exchange-date">
                            <i class="fas fa-calendar-alt me-1"></i>
                            2025-05-08 14:25
                        </span>
                        <span class="exchange-status status-shipped">
                            <i class="fas fa-shipping-fast me-1"></i>已发货
                        </span>
                    </div>

                    <div class="exchange-content">
                        <div class="exchange-image">
                            <img src="https://via.placeholder.com/70x70/1890ff/ffffff?text=商品" alt="商品图片">
                        </div>
                        <div class="exchange-info">
                            <div class="exchange-product-name">
                                米家宠物喂食器2 小米自动狗狗猫咪喂食器智能定时定量投食机
                            </div>
                            <div class="exchange-order-no">
                                <i class="fas fa-receipt me-1"></i>订单号: PX20250508142527ECpF8J
                            </div>
                            <div class="exchange-meta">
                                <span>
                                    <i class="fas fa-coins me-1"></i>消耗积分: 
                                    <span class="exchange-points">34900</span>
                                </span>
                            </div>
                        </div>
                    </div>

                    <div class="exchange-actions">
                        <a href="#" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-eye me-1"></i>查看详情
                        </a>
                        <button class="btn btn-success btn-sm">
                            <i class="fas fa-check me-1"></i>确认收货
                        </button>
                    </div>
                </div>

                <!-- 已完成状态示例 -->
                <div class="exchange-card">
                    <div class="exchange-header">
                        <span class="exchange-date">
                            <i class="fas fa-calendar-alt me-1"></i>
                            2025-05-07 18:14
                        </span>
                        <span class="exchange-status status-completed">
                            <i class="fas fa-check-circle me-1"></i>已完成
                        </span>
                    </div>

                    <div class="exchange-content">
                        <div class="exchange-image">
                            <img src="https://via.placeholder.com/70x70/52c41a/ffffff?text=商品" alt="商品图片">
                        </div>
                        <div class="exchange-info">
                            <div class="exchange-product-name">
                                小米手环8 NFC版 智能运动手环 血氧监测 50米防水
                            </div>
                            <div class="exchange-order-no">
                                <i class="fas fa-receipt me-1"></i>订单号: PX20250507181412RNesfA
                            </div>
                            <div class="exchange-meta">
                                <span>
                                    <i class="fas fa-coins me-1"></i>消耗积分: 
                                    <span class="exchange-points">2890</span>
                                </span>
                            </div>
                        </div>
                    </div>

                    <div class="exchange-actions">
                        <a href="#" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-eye me-1"></i>查看详情
                        </a>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
