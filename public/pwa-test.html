<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PWA安装测试页面</title>
    <link rel="manifest" href="/manifest.json">
    <meta name="theme-color" content="#e93b3d">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        
        button {
            background: linear-gradient(45deg, #e93b3d, #f10215);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: all 0.3s;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(233, 59, 61, 0.3);
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .debug-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 14px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 PWA安装功能测试</h1>
        
        <div id="status-container">
            <!-- 状态信息将在这里显示 -->
        </div>
        
        <div class="controls">
            <button id="install-btn" onclick="installPWA()" disabled>安装PWA应用</button>
            <button onclick="checkStatus()">检查状态</button>
            <button onclick="clearDebugInfo()">清除调试信息</button>
        </div>
        
        <div class="debug-info" id="debug-info">
            正在检查PWA支持状态...
        </div>
        
        <div class="instructions">
            <h3>📱 使用说明：</h3>
            <ul>
                <li><strong>Chrome/Edge (Android):</strong> 点击"安装PWA应用"按钮</li>
                <li><strong>Safari (iOS):</strong> 点击分享按钮 → "添加到主屏幕"</li>
                <li><strong>Firefox:</strong> 地址栏会显示安装图标</li>
                <li><strong>桌面浏览器:</strong> 地址栏右侧会显示安装图标</li>
            </ul>
        </div>
    </div>

    <script>
        let deferredPrompt;
        let debugInfo = [];
        
        function addDebugInfo(message) {
            const timestamp = new Date().toLocaleTimeString();
            debugInfo.push(`[${timestamp}] ${message}`);
            document.getElementById('debug-info').textContent = debugInfo.join('\n');
        }
        
        function clearDebugInfo() {
            debugInfo = [];
            document.getElementById('debug-info').textContent = '调试信息已清除';
        }
        
        function showStatus(message, type = 'info') {
            const container = document.getElementById('status-container');
            const statusDiv = document.createElement('div');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
            container.appendChild(statusDiv);
        }
        
        function checkStatus() {
            clearStatus();
            
            // 检查PWA支持
            if ('serviceWorker' in navigator) {
                showStatus('✅ 浏览器支持Service Worker', 'success');
                addDebugInfo('浏览器支持Service Worker');
            } else {
                showStatus('❌ 浏览器不支持Service Worker', 'error');
                addDebugInfo('浏览器不支持Service Worker');
            }
            
            // 检查是否已安装
            if (window.matchMedia && window.matchMedia('(display-mode: standalone)').matches) {
                showStatus('✅ PWA已安装并在独立模式下运行', 'success');
                addDebugInfo('PWA已安装并在独立模式下运行');
            } else {
                showStatus('ℹ️ PWA未安装或在浏览器中运行', 'info');
                addDebugInfo('PWA未安装或在浏览器中运行');
            }
            
            // 检查安装提示状态
            if (deferredPrompt) {
                showStatus('✅ 安装提示可用', 'success');
                addDebugInfo('安装提示可用');
                document.getElementById('install-btn').disabled = false;
            } else {
                showStatus('⚠️ 安装提示不可用', 'warning');
                addDebugInfo('安装提示不可用');
            }
            
            // 检查manifest
            fetch('/manifest.json')
                .then(response => response.json())
                .then(manifest => {
                    showStatus('✅ Manifest文件加载成功', 'success');
                    addDebugInfo(`Manifest加载成功: ${manifest.name}`);
                })
                .catch(error => {
                    showStatus('❌ Manifest文件加载失败', 'error');
                    addDebugInfo(`Manifest加载失败: ${error.message}`);
                });
        }
        
        function clearStatus() {
            document.getElementById('status-container').innerHTML = '';
        }
        
        async function installPWA() {
            if (!deferredPrompt) {
                showStatus('❌ 安装提示不可用', 'error');
                addDebugInfo('安装提示不可用');
                return;
            }
            
            try {
                addDebugInfo('开始安装流程...');
                deferredPrompt.prompt();
                
                const { outcome } = await deferredPrompt.userChoice;
                addDebugInfo(`用户选择: ${outcome}`);
                
                if (outcome === 'accepted') {
                    showStatus('🎉 PWA安装成功！', 'success');
                    addDebugInfo('PWA安装成功');
                } else {
                    showStatus('😔 用户取消了安装', 'warning');
                    addDebugInfo('用户取消了安装');
                }
                
                deferredPrompt = null;
                document.getElementById('install-btn').disabled = true;
                
            } catch (error) {
                showStatus('❌ 安装过程出错', 'error');
                addDebugInfo(`安装错误: ${error.message}`);
            }
        }
        
        // 监听beforeinstallprompt事件
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;
            addDebugInfo('收到beforeinstallprompt事件');
            document.getElementById('install-btn').disabled = false;
            showStatus('🎯 PWA可以安装！', 'success');
        });
        
        // 监听appinstalled事件
        window.addEventListener('appinstalled', (e) => {
            addDebugInfo('收到appinstalled事件');
            showStatus('🎉 PWA已成功安装到设备！', 'success');
            deferredPrompt = null;
            document.getElementById('install-btn').disabled = true;
        });
        
        // 页面加载完成后检查状态
        document.addEventListener('DOMContentLoaded', () => {
            addDebugInfo('页面加载完成');
            checkStatus();
            
            // 注册Service Worker
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.register('/sw.js')
                    .then(registration => {
                        addDebugInfo('Service Worker注册成功');
                        console.log('SW registered: ', registration);
                    })
                    .catch(registrationError => {
                        addDebugInfo(`Service Worker注册失败: ${registrationError.message}`);
                        console.log('SW registration failed: ', registrationError);
                    });
            }
        });
    </script>
</body>
</html>
