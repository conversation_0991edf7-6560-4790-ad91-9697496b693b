// 注册 Service Worker 函数
function registerServiceWorker() {
  // 确保浏览器支持 Service Worker
  if ('serviceWorker' in navigator) {
    // 页面加载完成后注册
    window.addEventListener('load', async () => {
      try {
        // 注册 Service Worker
        const registration = await navigator.serviceWorker.register('/sw.js', {
          scope: '/'
        });

        console.log('Service Worker 注册成功，scope: ', registration.scope);

        // 检查是否有更新
        if (registration.waiting) {
          // 有新版本等待激活
          notifyUserAboutUpdate(registration);
        }

        // 监听新的 Service Worker 安装
        registration.addEventListener('updatefound', () => {
          const newWorker = registration.installing;

          newWorker.addEventListener('statechange', () => {
            // 当新的 Service Worker 安装完成且进入等待状态
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              notifyUserAboutUpdate(registration);
            }
          });
        });

        // 页面打开10秒后主动检查更新
        setTimeout(() => {
          console.log('10秒后主动检查Service Worker更新');
          registration.update().then(() => {
            console.log('Service Worker更新检查完成');
            if (registration.waiting) {
              console.log('发现新版本的Service Worker');
              notifyUserAboutUpdate(registration);
            }
          }).catch(err => {
            console.error('Service Worker更新检查失败: ', err);
          });
        }, 10000); // 10秒后检查更新

        // 定期检查Service Worker是否需要更新
        setInterval(() => {
          registration.update().catch(err => {
            console.log('Service Worker 更新检查失败: ', err);
          });
        }, 60 * 60 * 1000); // 每小时检查一次更新
      } catch (error) {
        console.error('Service Worker 注册失败: ', error);
      }
    });

    // 检测控制页面的 Service Worker 是否发生了变化
    let refreshing = false;
    navigator.serviceWorker.addEventListener('controllerchange', () => {
      if (!refreshing) {
        refreshing = true;
        window.location.reload();
      }
    });

    // 当用户重新获得网络连接时，尝试更新缓存的页面，特别是对登录状态敏感的页面
    window.addEventListener('online', () => {
      // 如果当前页面是登录页、首页或其他需要实时状态的页面，自动刷新
      const currentPath = window.location.pathname;
      const pathsToRefresh = ['/', '/login', '/register', '/user', '/products', '/shops/my', '/posters/share'];

      if (pathsToRefresh.some(path => currentPath === path || currentPath.startsWith(path))) {
        console.log('网络恢复连接，刷新页面以获取最新内容');
        window.location.reload();
      }
    });
  } else {
    console.log('此浏览器不支持 Service Workers');
  }
}

// 通知用户有新版本
function notifyUserAboutUpdate(registration) {
  // 在页面显示更新提醒
  const notifyUserDiv = document.createElement('div');
  notifyUserDiv.style.position = 'fixed';
  notifyUserDiv.style.bottom = '0';
  notifyUserDiv.style.left = '0';
  notifyUserDiv.style.right = '0';
  notifyUserDiv.style.backgroundColor = '#0d6efd';
  notifyUserDiv.style.color = 'white';
  notifyUserDiv.style.padding = '16px';
  notifyUserDiv.style.textAlign = 'center';
  notifyUserDiv.style.zIndex = '9999';
  notifyUserDiv.style.boxShadow = '0 -2px 10px rgba(0,0,0,0.2)';
  notifyUserDiv.style.animation = 'slideUp 0.5s ease-out';

  // 添加动画样式
  const styleElement = document.createElement('style');
  styleElement.textContent = `
    @keyframes slideUp {
      from { transform: translateY(100%); }
      to { transform: translateY(0); }
    }
    @keyframes pulse {
      0% { transform: scale(1); }
      50% { transform: scale(1.05); }
      100% { transform: scale(1); }
    }
  `;
  document.head.appendChild(styleElement);

  notifyUserDiv.innerHTML = `
    <div style="display: flex; justify-content: space-between; align-items: center; max-width: 1200px; margin: 0 auto;">
      <span>应用有新版本可用！已更新分享功能</span>
      <div>
        <button id="update-app-btn" style="background-color: white; color: #0d6efd; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-right: 8px; animation: pulse 1.5s infinite;">立即更新</button>
        <button id="dismiss-update-btn" style="background-color: transparent; color: white; border: 1px solid white; padding: 8px 16px; border-radius: 4px; cursor: pointer;">稍后</button>
      </div>
    </div>
  `;

  document.body.appendChild(notifyUserDiv);

  // 更新按钮点击事件
  document.getElementById('update-app-btn').addEventListener('click', () => {
    if (registration.waiting) {
      // 发送消息给等待中的 Service Worker 让它接管页面
      registration.waiting.postMessage({ type: 'SKIP_WAITING' });
    }
    document.body.removeChild(notifyUserDiv);
  });

  // 忽略更新按钮
  document.getElementById('dismiss-update-btn').addEventListener('click', () => {
    document.body.removeChild(notifyUserDiv);
  });
}

// 添加清除登录页面缓存的函数
function clearLoginPageCache() {
  if ('caches' in window) {
    caches.keys().then(cacheNames => {
      return Promise.all(
        cacheNames.filter(cacheName => {
          return cacheName.startsWith('byeshop-cache-');
        }).map(cacheName => {
          return caches.open(cacheName).then(cache => {
            return cache.keys().then(requests => {
              return Promise.all(
                requests.filter(request => {
                  const url = new URL(request.url);
                  return url.pathname === '/' ||
                         url.pathname.includes('/login') ||
                         url.pathname.includes('/register');
                }).map(request => {
                  return cache.delete(request);
                })
              );
            });
          });
        })
      );
    }).then(() => {
      console.log('首页和登录页面缓存已清除');
    }).catch(error => {
      console.error('清除缓存失败:', error);
    });
  }
}

// 检测登录表单提交事件，在登录后清除缓存
document.addEventListener('DOMContentLoaded', () => {
  const loginForm = document.querySelector('form[action*="login"]');
  if (loginForm) {
    loginForm.addEventListener('submit', () => {
      // 登录表单提交后，标记需要清除缓存
      sessionStorage.setItem('clearCacheAfterLogin', 'true');
    });
  }

  // 检查是否需要清除缓存（登录后）
  if (sessionStorage.getItem('clearCacheAfterLogin') === 'true') {
    clearLoginPageCache();
    sessionStorage.removeItem('clearCacheAfterLogin');
  }
});

// 启用安装提示功能
let deferredPrompt;
let installModalShown = false;

window.addEventListener('beforeinstallprompt', (e) => {
  // 阻止 Chrome 67 及更早版本自动显示安装提示
  e.preventDefault();
  // 存储事件，以便稍后触发
  deferredPrompt = e;

  // 立即显示安装提示
  showInstallPromotion();
});

// 检查是否应该显示安装提示
function shouldShowInstallPrompt() {
  // 检查是否已经安装
  if (window.matchMedia && window.matchMedia('(display-mode: standalone)').matches) {
    return false;
  }

  // 检查是否已经拒绝过（24小时内不再提示）
  const lastPrompted = localStorage.getItem('pwa_install_last_prompted');
  const lastRejected = localStorage.getItem('pwa_install_rejected');

  if (lastRejected) {
    const rejectedTime = new Date(lastRejected);
    const now = new Date();
    const hoursDiff = (now - rejectedTime) / (1000 * 60 * 60);

    // 24小时内不再提示
    if (hoursDiff < 24) {
      return false;
    }
  }

  // 检查是否已经永久拒绝
  if (localStorage.getItem('pwa_install_never_show')) {
    return false;
  }

  return true;
}

// 显示安装提示模态框
function showInstallPromotion() {
  // 检查是否应该显示
  if (!shouldShowInstallPrompt() || installModalShown) {
    return;
  }

  installModalShown = true;

  // 创建模态框背景
  const modalOverlay = document.createElement('div');
  modalOverlay.id = 'pwa-install-modal';
  modalOverlay.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(4px);
    animation: fadeIn 0.3s ease-out;
  `;

  // 创建模态框内容
  const modalContent = document.createElement('div');
  modalContent.style.cssText = `
    background: white;
    border-radius: 16px;
    padding: 0;
    max-width: 400px;
    width: 90%;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: slideUp 0.3s ease-out;
    overflow: hidden;
  `;

  modalContent.innerHTML = `
    <style>
      @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
      }
      @keyframes slideUp {
        from { transform: translateY(30px); opacity: 0; }
        to { transform: translateY(0); opacity: 1; }
      }
      .pwa-modal-header {
        background: linear-gradient(135deg, #e93b3d, #f10215);
        color: white;
        padding: 24px;
        text-align: center;
        position: relative;
      }
      .pwa-modal-icon {
        width: 64px;
        height: 64px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 16px;
        font-size: 28px;
      }
      .pwa-modal-title {
        font-size: 20px;
        font-weight: 700;
        margin: 0 0 8px 0;
        text-shadow: 0 2px 4px rgba(0,0,0,0.2);
      }
      .pwa-modal-subtitle {
        font-size: 14px;
        opacity: 0.9;
        margin: 0;
      }
      .pwa-modal-body {
        padding: 24px;
      }
      .pwa-features {
        list-style: none;
        padding: 0;
        margin: 0 0 24px 0;
      }
      .pwa-features li {
        display: flex;
        align-items: center;
        padding: 8px 0;
        font-size: 14px;
        color: #666;
      }
      .pwa-features li::before {
        content: "✓";
        color: #00b74a;
        font-weight: bold;
        margin-right: 12px;
        width: 16px;
        text-align: center;
      }
      .pwa-modal-buttons {
        display: flex;
        gap: 12px;
      }
      .pwa-btn {
        flex: 1;
        padding: 12px 20px;
        border: none;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s;
        text-align: center;
      }
      .pwa-btn-primary {
        background: linear-gradient(45deg, #e93b3d, #f10215);
        color: white;
        box-shadow: 0 4px 12px rgba(233, 59, 61, 0.3);
      }
      .pwa-btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(233, 59, 61, 0.4);
      }
      .pwa-btn-secondary {
        background: #f8f9fa;
        color: #666;
        border: 1px solid #e0e0e0;
      }
      .pwa-btn-secondary:hover {
        background: #e9ecef;
        color: #495057;
      }
      .pwa-later-options {
        margin-top: 12px;
        text-align: center;
      }
      .pwa-later-link {
        color: #999;
        font-size: 12px;
        text-decoration: none;
        cursor: pointer;
      }
      .pwa-later-link:hover {
        color: #666;
        text-decoration: underline;
      }
    </style>

    <div class="pwa-modal-header">
      <div class="pwa-modal-icon">📱</div>
      <h3 class="pwa-modal-title">安装积分商城APP</h3>
      <p class="pwa-modal-subtitle">享受更快速、更便捷的购物体验</p>
    </div>

    <div class="pwa-modal-body">
      <ul class="pwa-features">
        <li>无需下载，一键安装到桌面</li>
        <li>离线浏览，随时查看商品信息</li>
        <li>消息推送，第一时间获取优惠</li>
        <li>快速启动，秒开应用体验</li>
        <li>节省流量，智能缓存技术</li>
      </ul>

      <div class="pwa-modal-buttons">
        <button id="pwa-install-btn" class="pwa-btn pwa-btn-primary">
          立即安装
        </button>
        <button id="pwa-later-btn" class="pwa-btn pwa-btn-secondary">
          以后提醒
        </button>
      </div>

      <div class="pwa-later-options">
        <a href="#" id="pwa-never-btn" class="pwa-later-link">不再提醒</a>
      </div>
    </div>
  `;

  modalOverlay.appendChild(modalContent);

  // 延迟显示，给页面加载一些时间
  setTimeout(() => {
    if (deferredPrompt && document.body) {
      document.body.appendChild(modalOverlay);

      // 记录显示时间
      localStorage.setItem('pwa_install_last_prompted', new Date().toISOString());

      // 安装按钮点击处理
      document.getElementById('pwa-install-btn').addEventListener('click', async () => {
        try {
          // 显示安装提示
          deferredPrompt.prompt();
          // 等待用户响应
          const { outcome } = await deferredPrompt.userChoice;

          console.log('PWA安装结果:', outcome);

          if (outcome === 'accepted') {
            // 用户接受安装
            localStorage.setItem('pwa_install_accepted', new Date().toISOString());
            // 显示感谢消息
            showInstallSuccessMessage();
          } else {
            // 用户拒绝安装
            localStorage.setItem('pwa_install_rejected', new Date().toISOString());
          }

          // 清理
          deferredPrompt = null;
          document.body.removeChild(modalOverlay);

        } catch (error) {
          console.error('PWA安装过程出错:', error);
          document.body.removeChild(modalOverlay);
        }
      });

      // 以后提醒按钮点击处理
      document.getElementById('pwa-later-btn').addEventListener('click', () => {
        localStorage.setItem('pwa_install_rejected', new Date().toISOString());
        document.body.removeChild(modalOverlay);
      });

      // 不再提醒按钮点击处理
      document.getElementById('pwa-never-btn').addEventListener('click', (e) => {
        e.preventDefault();
        localStorage.setItem('pwa_install_never_show', 'true');
        document.body.removeChild(modalOverlay);
      });

      // 点击背景关闭
      modalOverlay.addEventListener('click', (e) => {
        if (e.target === modalOverlay) {
          localStorage.setItem('pwa_install_rejected', new Date().toISOString());
          document.body.removeChild(modalOverlay);
        }
      });
    }
  }, 2000); // 2秒后显示，给用户一些浏览时间
}

// 显示安装成功消息
function showInstallSuccessMessage() {
  const successToast = document.createElement('div');
  successToast.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(45deg, #00b74a, #52c41a);
    color: white;
    padding: 16px 24px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 183, 74, 0.3);
    z-index: 10001;
    font-size: 14px;
    font-weight: 600;
    animation: slideInRight 0.3s ease-out;
  `;

  successToast.innerHTML = `
    <style>
      @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
      }
    </style>
    <div style="display: flex; align-items: center;">
      <span style="margin-right: 8px;">🎉</span>
      安装成功！您可以在桌面找到积分商城应用
    </div>
  `;

  document.body.appendChild(successToast);

  // 3秒后自动消失
  setTimeout(() => {
    if (document.body.contains(successToast)) {
      document.body.removeChild(successToast);
    }
  }, 3000);
}

// 手动触发安装提示
function triggerInstallPrompt() {
  if (deferredPrompt) {
    installModalShown = false; // 重置状态
    showInstallPromotion();
  } else {
    // 如果没有安装提示事件，显示提示信息
    showInstallUnavailableMessage();
  }
}

// 显示安装不可用消息
function showInstallUnavailableMessage() {
  const toast = document.createElement('div');
  toast.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(45deg, #ff6700, #ff8c00);
    color: white;
    padding: 16px 24px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(255, 103, 0, 0.3);
    z-index: 10001;
    font-size: 14px;
    font-weight: 600;
    animation: slideInRight 0.3s ease-out;
    max-width: 300px;
  `;

  toast.innerHTML = `
    <style>
      @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
      }
    </style>
    <div style="display: flex; align-items: center;">
      <span style="margin-right: 8px;">ℹ️</span>
      <div>
        <div>当前浏览器不支持安装</div>
        <div style="font-size: 12px; opacity: 0.9; margin-top: 4px;">
          请使用Chrome、Edge或Safari浏览器
        </div>
      </div>
    </div>
  `;

  document.body.appendChild(toast);

  // 4秒后自动消失
  setTimeout(() => {
    if (document.body.contains(toast)) {
      document.body.removeChild(toast);
    }
  }, 4000);
}

// 添加安装按钮到导航栏
function addInstallButtonToNavbar() {
  // 等待DOM加载完成
  document.addEventListener('DOMContentLoaded', () => {
    // 检查是否已经安装
    if (window.matchMedia && window.matchMedia('(display-mode: standalone)').matches) {
      return; // 已安装，不显示按钮
    }

    // 检查是否已经添加过按钮
    if (document.getElementById('pwa-install-nav-btn') || document.getElementById('pwa-install-mobile-btn')) {
      return;
    }

    // 桌面端：添加到顶部导航栏
    const desktopNavbar = document.querySelector('.navbar-nav');
    if (desktopNavbar) {
      const installNavItem = document.createElement('li');
      installNavItem.className = 'nav-item d-none d-md-block';
      installNavItem.innerHTML = `
        <a class="nav-link" href="#" id="pwa-install-nav-btn" style="color: #e93b3d; font-weight: 600;">
          <i class="fas fa-download me-1"></i>安装APP
        </a>
      `;

      // 添加到导航栏
      desktopNavbar.appendChild(installNavItem);

      // 绑定点击事件
      document.getElementById('pwa-install-nav-btn').addEventListener('click', (e) => {
        e.preventDefault();
        triggerInstallPrompt();
      });
    }

    // 移动端：添加到底部导航栏
    const mobileBottomNav = document.querySelector('.mobile-bottom-nav .d-flex');
    if (mobileBottomNav) {
      const installMobileItem = document.createElement('a');
      installMobileItem.className = 'nav-link';
      installMobileItem.href = '#';
      installMobileItem.id = 'pwa-install-mobile-btn';
      installMobileItem.style.cssText = 'color: #e93b3d; font-weight: 600;';
      installMobileItem.innerHTML = `
        <i class="fas fa-download"></i>
        <span>安装</span>
      `;

      // 添加到底部导航栏
      mobileBottomNav.appendChild(installMobileItem);

      // 绑定点击事件
      installMobileItem.addEventListener('click', (e) => {
        e.preventDefault();
        triggerInstallPrompt();
      });
    }

    // 如果都没找到导航栏，创建一个浮动按钮
    if (!desktopNavbar && !mobileBottomNav) {
      createFloatingInstallButton();
    }
  });
}

// 创建浮动安装按钮
function createFloatingInstallButton() {
  // 检查是否已经创建
  if (document.getElementById('pwa-floating-install-btn')) {
    return;
  }

  const floatingBtn = document.createElement('div');
  floatingBtn.id = 'pwa-floating-install-btn';
  floatingBtn.style.cssText = `
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 56px;
    height: 56px;
    background: linear-gradient(45deg, #e93b3d, #f10215);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    cursor: pointer;
    box-shadow: 0 4px 16px rgba(233, 59, 61, 0.4);
    z-index: 1000;
    transition: all 0.3s;
  `;

  floatingBtn.innerHTML = '<i class="fas fa-download"></i>';
  floatingBtn.title = '安装积分商城APP';

  // 悬停效果
  floatingBtn.addEventListener('mouseenter', () => {
    floatingBtn.style.transform = 'scale(1.1)';
    floatingBtn.style.boxShadow = '0 6px 20px rgba(233, 59, 61, 0.5)';
  });

  floatingBtn.addEventListener('mouseleave', () => {
    floatingBtn.style.transform = 'scale(1)';
    floatingBtn.style.boxShadow = '0 4px 16px rgba(233, 59, 61, 0.4)';
  });

  // 点击事件
  floatingBtn.addEventListener('click', () => {
    triggerInstallPrompt();
  });

  document.body.appendChild(floatingBtn);
}

// 检测PWA安装状态变化
function detectPWAInstallation() {
  // 监听应用安装事件
  window.addEventListener('appinstalled', (e) => {
    console.log('PWA已成功安装');

    // 隐藏所有安装按钮
    hideAllInstallButtons();

    // 显示安装成功消息
    showInstallSuccessMessage();

    // 记录安装成功
    localStorage.setItem('pwa_install_accepted', new Date().toISOString());
  });

  // 检查是否在PWA模式下运行
  if (window.matchMedia && window.matchMedia('(display-mode: standalone)').matches) {
    console.log('应用正在PWA模式下运行');

    // 隐藏所有安装按钮
    hideAllInstallButtons();
  }
}

// 隐藏所有安装按钮
function hideAllInstallButtons() {
  // 桌面端导航栏按钮
  const desktopInstallBtn = document.getElementById('pwa-install-nav-btn');
  if (desktopInstallBtn && desktopInstallBtn.parentElement) {
    desktopInstallBtn.parentElement.style.display = 'none';
  }

  // 移动端底部导航栏按钮
  const mobileInstallBtn = document.getElementById('pwa-install-mobile-btn');
  if (mobileInstallBtn) {
    mobileInstallBtn.style.display = 'none';
  }

  // 浮动按钮
  const floatingInstallBtn = document.getElementById('pwa-floating-install-btn');
  if (floatingInstallBtn) {
    floatingInstallBtn.style.display = 'none';
  }

  // 九宫格导航中的安装按钮
  const gridInstallBtn = document.getElementById('pwa-install-grid-btn');
  if (gridInstallBtn && gridInstallBtn.parentElement) {
    gridInstallBtn.parentElement.style.display = 'none';
  }

  const gridInstallBtnGuest = document.getElementById('pwa-install-grid-btn-guest');
  if (gridInstallBtnGuest && gridInstallBtnGuest.parentElement) {
    gridInstallBtnGuest.parentElement.style.display = 'none';
  }
}

// 初始化PWA功能
function initPWA() {
  // 注册 Service Worker
  registerServiceWorker();

  // 添加导航栏安装按钮
  addInstallButtonToNavbar();

  // 检测PWA安装状态
  detectPWAInstallation();

  // 暴露全局函数供其他脚本调用
  window.triggerPWAInstall = triggerInstallPrompt;
}

// 启动PWA功能
initPWA();

// 调试：检查PWA安装按钮是否正确添加
document.addEventListener('DOMContentLoaded', () => {
  setTimeout(() => {
    console.log('PWA调试信息:');
    console.log('- 是否已安装PWA:', window.matchMedia && window.matchMedia('(display-mode: standalone)').matches);
    console.log('- 桌面端安装按钮:', document.getElementById('pwa-install-nav-btn'));
    console.log('- 移动端安装按钮:', document.getElementById('pwa-install-mobile-btn'));
    console.log('- 浮动安装按钮:', document.getElementById('pwa-floating-install-btn'));
    console.log('- 移动端底部导航栏:', document.querySelector('.mobile-bottom-nav .d-flex'));
    console.log('- 桌面端导航栏:', document.querySelector('.navbar-nav'));
    console.log('- deferredPrompt状态:', deferredPrompt);
  }, 2000);
});